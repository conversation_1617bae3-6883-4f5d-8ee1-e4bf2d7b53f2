'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Spinner } from '@/components/ui/spinner';
import LogoUploader from '@/components/settings/LogoUploader';
import { Input } from '@/components/ui/input';

export default function SettingsPage() {
  const [tenant, setTenant] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTenantData = async () => {
      try {
        // In a real implementation, this would fetch the tenant data from your API
        // const response = await fetch('/api/tenants/current');
        // const data = await response.json();
        // setTenant(data.tenant);

        // For development, use mock data
        const mockTenant = {
          id: '1',
          name: 'Acme Inc',
          businessName: 'Acme Corporation',
          businessRegNo: '12345678',
          email: '<EMAIL>',
          phone: '+1234567890',
          address: '123 Main St, City, Country',
          logo: null,
          createdAt: '2023-01-01T00:00:00.000Z',
        };

        setTenant(mockTenant);
      } catch (err) {
        console.error('Error fetching tenant data:', err);
        setError('Failed to load tenant data. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchTenantData();
  }, []);

  const handleLogoUpdated = (logoUrl: string | null) => {
    setTenant((prev: any) => ({
      ...prev,
      logo: logoUrl,
    }));
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spinner size="lg" />
        <span className="ml-2 text-text-secondary">Loading...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4">
        <p>{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-2 text-red-600 hover:text-red-800 font-medium"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between bg-white p-6 rounded-lg shadow-sm mb-6">
        <div>
          <h1 className="text-2xl font-bold text-text-primary">Settings</h1>
          <p className="mt-1 text-sm text-text-secondary">
            Manage your account settings and preferences.
          </p>
        </div>
      </div>

      <Tabs defaultValue="general" className="space-y-4">
        <TabsList className="mb-6 bg-white p-1 rounded-lg border">
          <TabsTrigger value="general" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">General</TabsTrigger>
          <TabsTrigger value="branding" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">Branding</TabsTrigger>
          <TabsTrigger value="templates" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">Templates</TabsTrigger>
          <TabsTrigger value="notifications" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">Notifications</TabsTrigger>
          <TabsTrigger value="integrations" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">Integrations</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-4">
          <Card className="border-none shadow-md">
            <CardHeader className="pb-2 border-b">
              <CardTitle className="text-lg font-bold text-text-primary">Company Information</CardTitle>
              <CardDescription className="text-text-secondary">
                Update your company details that will appear on invoices and other documents.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-1">
                    Company Name
                  </label>
                  <input
                    type="text"
                    className="w-full p-2 border rounded-md"
                    value={tenant?.businessName || ''}
                    readOnly
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-1">
                    Business Registration Number
                  </label>
                  <input
                    type="text"
                    className="w-full p-2 border rounded-md"
                    value={tenant?.businessRegNo || ''}
                    readOnly
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-1">
                    Email
                  </label>
                  <input
                    type="email"
                    className="w-full p-2 border rounded-md"
                    value={tenant?.email || ''}
                    readOnly
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-1">
                    Phone
                  </label>
                  <input
                    type="tel"
                    className="w-full p-2 border rounded-md"
                    value={tenant?.phone || ''}
                    readOnly
                  />
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-text-primary mb-1">
                    Address
                  </label>
                  <textarea
                    className="w-full p-2 border rounded-md"
                    rows={3}
                    value={tenant?.address || ''}
                    readOnly
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="branding" className="space-y-4">
          <LogoUploader
            currentLogo={tenant?.logo}
            onLogoUpdated={handleLogoUpdated}
          />
        </TabsContent>

        <TabsContent value="templates" className="space-y-4">
          <Card className="border-none shadow-md">
            <CardHeader className="pb-2 border-b">
              <CardTitle className="text-lg font-bold text-text-primary">PDF Templates</CardTitle>
              <CardDescription className="text-text-secondary">
                Customize how your invoices and other documents look.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-text-secondary mb-4">
                Create and manage custom PDF templates for your invoices and other documents.
              </p>
              <div className="flex justify-center">
                <a href="/dashboard/settings/templates">
                  <Button size="sm" className="bg-indigo-600 hover:bg-indigo-700">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Manage Templates
                  </Button>
                </a>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications">
          <Card className="border-none shadow-md">
            <CardHeader className="pb-2 border-b">
              <CardTitle className="text-lg font-bold text-text-primary">Notification Preferences</CardTitle>
              <CardDescription className="text-text-secondary">
                Configure how and when you receive notifications.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-text-secondary">
                Notification settings will be available soon.
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="integrations">
          <Card className="border-none shadow-md">
            <CardHeader className="pb-2 border-b">
              <CardTitle className="text-lg font-bold text-text-primary">Integrations</CardTitle>
              <CardDescription className="text-text-secondary">
                Connect your account with other services.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-text-secondary">
                Integration settings will be available soon.
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
