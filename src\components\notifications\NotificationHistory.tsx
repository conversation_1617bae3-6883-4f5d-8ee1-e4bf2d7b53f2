'use client';

import { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { DatePicker } from '@/components/ui/date-picker';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious
} from '@/components/ui/pagination';
import { Search, Filter, RefreshCw, Send } from 'lucide-react';

// Mock data for notification history
const mockNotifications = [
  {
    id: '1',
    type: 'invoice_created',
    channel: 'email',
    recipient: '<EMAIL>',
    recipientName: '<PERSON>',
    status: 'delivered',
    sentAt: '2023-06-15T10:30:00Z',
    deliveredAt: '2023-06-15T10:30:05Z',
    readAt: '2023-06-15T11:45:22Z',
    subject: 'Invoice #INV-001 Created',
    relatedId: 'INV-001',
    relatedType: 'invoice',
  },
  {
    id: '2',
    type: 'payment_reminder',
    channel: 'whatsapp',
    recipient: '+60123456789',
    recipientName: 'Jane Smith',
    status: 'delivered',
    sentAt: '2023-06-14T09:15:00Z',
    deliveredAt: '2023-06-14T09:15:10Z',
    readAt: '2023-06-14T10:20:15Z',
    subject: 'Payment Reminder for Invoice #INV-002',
    relatedId: 'INV-002',
    relatedType: 'invoice',
  },
  {
    id: '3',
    type: 'payment_overdue',
    channel: 'email',
    recipient: '<EMAIL>',
    recipientName: 'Robert Johnson',
    status: 'failed',
    sentAt: '2023-06-13T14:45:00Z',
    deliveredAt: null,
    readAt: null,
    subject: 'Overdue Payment for Invoice #INV-003',
    relatedId: 'INV-003',
    relatedType: 'invoice',
    error: 'Recipient mailbox full',
  },
  {
    id: '4',
    type: 'payment_received',
    channel: 'email',
    recipient: '<EMAIL>',
    recipientName: 'Sarah Williams',
    status: 'delivered',
    sentAt: '2023-06-12T16:20:00Z',
    deliveredAt: '2023-06-12T16:20:08Z',
    readAt: null,
    subject: 'Payment Received for Invoice #INV-004',
    relatedId: 'INV-004',
    relatedType: 'invoice',
  },
  {
    id: '5',
    type: 'invoice_created',
    channel: 'whatsapp',
    recipient: '+60198765432',
    recipientName: 'Michael Brown',
    status: 'pending',
    sentAt: '2023-06-11T11:10:00Z',
    deliveredAt: null,
    readAt: null,
    subject: 'Invoice #INV-005 Created',
    relatedId: 'INV-005',
    relatedType: 'invoice',
  },
];

export default function NotificationHistory() {
  const [notifications, setNotifications] = useState(mockNotifications);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [channelFilter, setChannelFilter] = useState('all');
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);

  const handleSearch = () => {
    // In a real implementation, this would call your API with filters
    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      // Filter the mock data based on search criteria
      let filtered = [...mockNotifications];

      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        filtered = filtered.filter(n =>
          n.recipientName.toLowerCase().includes(query) ||
          n.recipient.toLowerCase().includes(query) ||
          n.relatedId.toLowerCase().includes(query) ||
          n.subject.toLowerCase().includes(query)
        );
      }

      if (statusFilter !== 'all') {
        filtered = filtered.filter(n => n.status === statusFilter);
      }

      if (typeFilter !== 'all') {
        filtered = filtered.filter(n => n.type === typeFilter);
      }

      if (channelFilter !== 'all') {
        filtered = filtered.filter(n => n.channel === channelFilter);
      }

      if (startDate) {
        filtered = filtered.filter(n => new Date(n.sentAt) >= startDate);
      }

      if (endDate) {
        const endOfDay = new Date(endDate);
        endOfDay.setHours(23, 59, 59, 999);
        filtered = filtered.filter(n => new Date(n.sentAt) <= endOfDay);
      }

      setNotifications(filtered);
      setIsLoading(false);
    }, 500);
  };

  const handleResend = (id: string) => {
    // In a real implementation, this would call your API to resend the notification
    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      // Update the notification status
      const updatedNotifications = notifications.map(n => {
        if (n.id === id) {
          return {
            ...n,
            status: 'pending',
            sentAt: new Date().toISOString(),
            deliveredAt: null,
            readAt: null,
          };
        }
        return n;
      });

      setNotifications(updatedNotifications);
      setIsLoading(false);
    }, 500);
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'delivered':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Delivered</Badge>;
      case 'read':
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200">Read</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200">Pending</Badge>;
      case 'failed':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-200">Failed</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-200">{status}</Badge>;
    }
  };

  const getChannelIcon = (channel: string) => {
    switch (channel) {
      case 'email':
        return '✉️';
      case 'whatsapp':
        return '📱';
      default:
        return '📄';
    }
  };

  return (
    <Card className="border-none shadow-md">
      <CardHeader className="pb-2 border-b">
        <CardTitle className="text-lg font-bold">Notification History</CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <table className="w-full text-sm text-left">
            <thead className="text-xs font-medium text-gray-500 bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="px-6 py-3">Type</th>
                <th className="px-6 py-3">Channel</th>
                <th className="px-6 py-3">Recipient</th>
                <th className="px-6 py-3">Status</th>
                <th className="px-6 py-3">Sent At</th>
                <th className="px-6 py-3">Delivered At</th>
                <th className="px-6 py-3 text-right">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {notifications.length === 0 ? (
                <tr>
                  <td colSpan={7} className="px-6 py-4 text-center">
                    No notifications found
                  </td>
                </tr>
              ) : (
                notifications.map((notification) => (
                  <tr key={notification.id} className="bg-white hover:bg-gray-50 transition-colors">
                    <td className="px-6 py-4">
                      <div className="font-medium">{notification.type.replace('_', ' ')}</div>
                      <div className="text-sm text-gray-500">{notification.relatedId}</div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        <span className="mr-1">{getChannelIcon(notification.channel)}</span>
                        <span className="capitalize">{notification.channel}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 font-medium">
                      <div>{notification.recipientName}</div>
                      <div className="text-sm text-gray-500">{notification.recipient}</div>
                    </td>
                    <td className="px-6 py-4">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        notification.status === 'delivered' ? 'bg-green-100 text-green-800' :
                        notification.status === 'read' ? 'bg-blue-100 text-blue-800' :
                        notification.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {notification.status === 'delivered' && (
                          <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        )}
                        {notification.status === 'pending' && (
                          <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        )}
                        {notification.status === 'failed' && (
                          <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                          </svg>
                        )}
                        {notification.status.charAt(0).toUpperCase() + notification.status.slice(1)}
                      </span>
                      {notification.error && (
                        <div className="text-xs text-red-500 mt-1">{notification.error}</div>
                      )}
                    </td>
                    <td className="px-6 py-4 text-gray-500">
                      {formatDate(notification.sentAt)}
                    </td>
                    <td className="px-6 py-4 text-gray-500">
                      {formatDate(notification.deliveredAt)}
                    </td>
                    <td className="px-6 py-4 text-right">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-indigo-600 hover:text-indigo-800 hover:bg-indigo-50"
                          onClick={() => handleResend(notification.id)}
                          disabled={notification.status === 'pending' || isLoading}
                        >
                          Resend
                        </Button>
                        <Button variant="ghost" size="sm" className="text-gray-600 hover:text-gray-800 hover:bg-gray-50">
                          View
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200">
          <div className="text-sm text-gray-500">
            Showing <span className="font-medium">1</span> to <span className="font-medium">{notifications.length}</span> of <span className="font-medium">{notifications.length}</span> results
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" size="sm" disabled>
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Previous
            </Button>
            <Button variant="outline" size="sm">
              Next
              <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
