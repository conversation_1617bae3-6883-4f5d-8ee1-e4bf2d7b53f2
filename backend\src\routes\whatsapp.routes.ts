import { Router } from 'express';
import { sendInvoiceViaWhatsApp, sendPaymentReminder, configureWhatsApp } from '../controllers/whatsapp.controller';
import { authenticate } from '../middleware/auth.middleware';
import { ExpressHandler } from '../types/express';

const router = Router();

// All routes are protected
router.post('/send-invoice', authenticate as ExpressHandler, sendInvoiceViaWhatsApp as ExpressHandler);
router.post('/send-reminder', authenticate as ExpressHandler, sendPaymentReminder as ExpressHandler);
router.post('/configure', authenticate as ExpressHandler, configureWhatsApp as ExpressHandler);

export default router;
