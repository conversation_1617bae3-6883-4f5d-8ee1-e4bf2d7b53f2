import { prisma } from '../index';
import { logger } from '../utils/logger';
import twilio from 'twilio';
import axios from 'axios';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

/**
 * WhatsApp Service
 * Handles WhatsApp messaging integration using Twilio or Meta API
 */
export class WhatsAppService {
  private twilioClient: twilio.Twilio | null = null;
  private metaApiToken: string | null = null;

  constructor() {
    // Initialize Twilio client if credentials are available
    if (process.env.TWILIO_ACCOUNT_SID && process.env.TWILIO_AUTH_TOKEN) {
      this.twilioClient = twilio(
        process.env.TWILIO_ACCOUNT_SID,
        process.env.TWILIO_AUTH_TOKEN
      );
    }

    // Initialize Meta API token if available
    if (process.env.META_API_TOKEN) {
      this.metaApiToken = process.env.META_API_TOKEN;
    }
  }

  /**
   * Get WhatsApp settings for a tenant
   * @param tenantId Tenant ID
   */
  async getSettings(tenantId: string) {
    try {
      const settings = await prisma.whatsAppSettings.findFirst({
        where: { tenantId },
      });

      if (!settings) {
        // Create default settings
        return await prisma.whatsAppSettings.create({
          data: {
            tenantId,
            enabled: false,
            provider: 'TWILIO',
          },
        });
      }

      return settings;
    } catch (error) {
      logger.error('Error getting WhatsApp settings:', error);
      throw error;
    }
  }

  /**
   * Update WhatsApp settings for a tenant
   * @param tenantId Tenant ID
   * @param settings Settings to update
   */
  async updateSettings(tenantId: string, settings: any) {
    try {
      const existingSettings = await prisma.whatsAppSettings.findFirst({
        where: { tenantId },
      });

      if (existingSettings) {
        // Update existing settings
        return await prisma.whatsAppSettings.update({
          where: { id: existingSettings.id },
          data: settings,
        });
      } else {
        // Create new settings
        return await prisma.whatsAppSettings.create({
          data: {
            tenantId,
            ...settings,
          },
        });
      }
    } catch (error) {
      logger.error('Error updating WhatsApp settings:', error);
      throw error;
    }
  }

  /**
   * Get all WhatsApp templates for a tenant
   * @param tenantId Tenant ID
   */
  async getTemplates(tenantId: string) {
    try {
      return await prisma.whatsAppTemplate.findMany({
        where: { tenantId },
      });
    } catch (error) {
      logger.error('Error getting WhatsApp templates:', error);
      throw error;
    }
  }

  /**
   * Get WhatsApp template by ID
   * @param templateId Template ID
   */
  async getTemplate(templateId: string) {
    try {
      return await prisma.whatsAppTemplate.findUnique({
        where: { id: templateId },
      });
    } catch (error) {
      logger.error('Error getting WhatsApp template:', error);
      throw error;
    }
  }

  /**
   * Create WhatsApp template for a tenant
   * @param tenantId Tenant ID
   * @param template Template data
   */
  async createTemplate(tenantId: string, template: any) {
    try {
      return await prisma.whatsAppTemplate.create({
        data: {
          ...template,
          tenantId,
        },
      });
    } catch (error) {
      logger.error('Error creating WhatsApp template:', error);
      throw error;
    }
  }

  /**
   * Update WhatsApp template
   * @param templateId Template ID
   * @param template Template data
   */
  async updateTemplate(templateId: string, template: any) {
    try {
      return await prisma.whatsAppTemplate.update({
        where: { id: templateId },
        data: template,
      });
    } catch (error) {
      logger.error('Error updating WhatsApp template:', error);
      throw error;
    }
  }

  /**
   * Delete WhatsApp template
   * @param templateId Template ID
   */
  async deleteTemplate(templateId: string) {
    try {
      await prisma.whatsAppTemplate.delete({
        where: { id: templateId },
      });

      return { success: true };
    } catch (error) {
      logger.error('Error deleting WhatsApp template:', error);
      throw error;
    }
  }

  /**
   * Send WhatsApp message using template
   * @param tenantId Tenant ID
   * @param to Recipient phone number
   * @param templateName Template name
   * @param templateData Template data
   */
  async sendTemplateMessage(
    tenantId: string,
    to: string,
    templateName: string,
    templateData: Record<string, any>
  ) {
    try {
      // Get tenant settings
      const settings = await this.getSettings(tenantId);

      if (!settings.enabled) {
        throw new Error('WhatsApp integration is not enabled');
      }

      // Get template
      const template = await prisma.whatsAppTemplate.findFirst({
        where: {
          tenantId,
          name: templateName,
        },
      });

      if (!template) {
        throw new Error(`Template "${templateName}" not found`);
      }

      // Create message record
      const message = await prisma.whatsAppMessage.create({
        data: {
          tenantId,
          to,
          templateName,
          templateData,
          status: 'QUEUED',
        },
      });

      // Send message based on provider
      let messageId: string | undefined;

      if (settings.provider === 'TWILIO') {
        messageId = await this.sendViaTwilio(to, template, templateData, settings);
      } else if (settings.provider === 'META') {
        messageId = await this.sendViaMeta(to, template, templateData, settings);
      } else {
        throw new Error(`Unsupported provider: ${settings.provider}`);
      }

      // Update message with provider ID
      const updatedMessage = await prisma.whatsAppMessage.update({
        where: { id: message.id },
        data: {
          providerMessageId: messageId,
          status: 'SENT',
        },
      });

      return updatedMessage;
    } catch (error) {
      logger.error('Error sending WhatsApp message:', error);
      throw error;
    }
  }

  /**
   * Send invoice via WhatsApp
   * @param tenantId Tenant ID
   * @param invoiceId Invoice ID
   * @param phoneNumber Recipient phone number
   */
  async sendInvoice(tenantId: string, invoiceId: string, phoneNumber: string) {
    try {
      // Get invoice
      const invoice = await prisma.invoice.findUnique({
        where: { id: invoiceId },
        include: {
          customer: true,
        },
      });

      if (!invoice) {
        throw new Error('Invoice not found');
      }

      // Generate invoice PDF
      const pdfPath = await this.generateInvoicePdf(invoice);

      // Send message with invoice details and PDF
      return await this.sendTemplateMessage(
        tenantId,
        phoneNumber,
        'invoice_notification',
        {
          invoiceNumber: invoice.invoiceNumber,
          amount: invoice.totalAmount.toFixed(2),
          dueDate: new Date(invoice.dueDate).toLocaleDateString(),
          customerName: invoice.customer.name,
          pdfUrl: pdfPath,
        }
      );
    } catch (error) {
      logger.error('Error sending invoice via WhatsApp:', error);
      throw error;
    }
  }

  /**
   * Send payment reminder via WhatsApp
   * @param tenantId Tenant ID
   * @param invoiceId Invoice ID
   * @param phoneNumber Recipient phone number
   */
  async sendPaymentReminder(tenantId: string, invoiceId: string, phoneNumber: string) {
    try {
      // Get invoice
      const invoice = await prisma.invoice.findUnique({
        where: { id: invoiceId },
        include: {
          customer: true,
        },
      });

      if (!invoice) {
        throw new Error('Invoice not found');
      }

      // Send payment reminder message
      return await this.sendTemplateMessage(
        tenantId,
        phoneNumber,
        'payment_reminder',
        {
          invoiceNumber: invoice.invoiceNumber,
          amount: invoice.totalAmount.toFixed(2),
          dueDate: new Date(invoice.dueDate).toLocaleDateString(),
          customerName: invoice.customer.name,
          paymentLink: `https://yourdomain.com/pay/${invoiceId}`,
        }
      );
    } catch (error) {
      logger.error('Error sending payment reminder via WhatsApp:', error);
      throw error;
    }
  }

  /**
   * Send payment receipt via WhatsApp
   * @param tenantId Tenant ID
   * @param paymentId Payment ID
   * @param phoneNumber Recipient phone number
   */
  async sendPaymentReceipt(tenantId: string, paymentId: string, phoneNumber: string) {
    try {
      // Get payment
      const payment = await prisma.payment.findUnique({
        where: { id: paymentId },
        include: {
          invoice: {
            include: {
              customer: true,
            },
          },
        },
      });

      if (!payment) {
        throw new Error('Payment not found');
      }

      // Generate receipt PDF
      const pdfPath = await this.generateReceiptPdf(payment);

      // Send message with receipt details and PDF
      return await this.sendTemplateMessage(
        tenantId,
        phoneNumber,
        'payment_receipt',
        {
          invoiceNumber: payment.invoice.invoiceNumber,
          amount: payment.amount.toFixed(2),
          paymentDate: new Date(payment.createdAt).toLocaleDateString(),
          customerName: payment.invoice.customer.name,
          pdfUrl: pdfPath,
        }
      );
    } catch (error) {
      logger.error('Error sending payment receipt via WhatsApp:', error);
      throw error;
    }
  }

  /**
   * Get all WhatsApp messages for a tenant
   * @param tenantId Tenant ID
   * @param params Query parameters
   */
  async getMessages(
    tenantId: string,
    params: {
      page?: number;
      limit?: number;
      status?: string;
      startDate?: Date;
      endDate?: Date;
    }
  ) {
    try {
      const { page = 1, limit = 10, status, startDate, endDate } = params;
      const skip = (page - 1) * limit;

      // Build where clause
      const where: any = { tenantId };

      if (status) {
        where.status = status;
      }

      if (startDate || endDate) {
        where.createdAt = {};
        if (startDate) {
          where.createdAt.gte = startDate;
        }
        if (endDate) {
          where.createdAt.lte = endDate;
        }
      }

      // Get messages
      const messages = await prisma.whatsAppMessage.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
      });

      // Get total count
      const total = await prisma.whatsAppMessage.count({ where });

      return {
        data: messages,
        total,
        page,
        limit,
      };
    } catch (error) {
      logger.error('Error getting WhatsApp messages:', error);
      throw error;
    }
  }

  /**
   * Get WhatsApp message by ID
   * @param messageId Message ID
   */
  async getMessage(messageId: string) {
    try {
      return await prisma.whatsAppMessage.findUnique({
        where: { id: messageId },
      });
    } catch (error) {
      logger.error('Error getting WhatsApp message:', error);
      throw error;
    }
  }

  /**
   * Test WhatsApp connection
   * @param tenantId Tenant ID
   */
  async testConnection(tenantId: string) {
    try {
      // Get settings
      const settings = await this.getSettings(tenantId);

      if (!settings.enabled) {
        return {
          success: false,
          message: 'WhatsApp integration is not enabled',
        };
      }

      if (settings.provider === 'TWILIO') {
        // Test Twilio connection
        if (!settings.accountSid || !settings.authToken || !settings.phoneNumber) {
          return {
            success: false,
            message: 'Twilio credentials are incomplete',
          };
        }

        // Create Twilio client with tenant credentials
        const client = twilio(settings.accountSid, settings.authToken);

        // Verify credentials by fetching account info
        await client.api.accounts(settings.accountSid).fetch();

        return {
          success: true,
          message: 'Twilio connection successful',
        };
      } else if (settings.provider === 'META') {
        // Test Meta connection
        if (!settings.apiKey || !settings.phoneNumber) {
          return {
            success: false,
            message: 'Meta API credentials are incomplete',
          };
        }

        // Verify credentials by making a test API call
        // In a real implementation, this would call the Meta WhatsApp Business API
        // For now, we'll just simulate a successful connection

        return {
          success: true,
          message: 'Meta API connection successful',
        };
      } else {
        return {
          success: false,
          message: `Unsupported provider: ${settings.provider}`,
        };
      }
    } catch (error: any) {
      logger.error('Error testing WhatsApp connection:', error);
      return {
        success: false,
        message: error.message || 'Connection test failed',
      };
    }
  }

  /**
   * Send message via Twilio
   * @private
   * @param to Recipient phone number
   * @param template Template
   * @param templateData Template data
   * @param settings WhatsApp settings
   */
  private async sendViaTwilio(
    to: string,
    template: any,
    templateData: Record<string, any>,
    settings: any
  ): Promise<string> {
    try {
      // Create Twilio client with tenant credentials
      const client = twilio(settings.accountSid, settings.authToken);

      // Format phone number
      const formattedTo = this.formatPhoneNumber(to);

      // Replace template variables with data
      const body = this.replaceTemplateVariables(template.content, templateData);

      // Send message
      const message = await client.messages.create({
        body,
        from: `whatsapp:${settings.phoneNumber}`,
        to: `whatsapp:${formattedTo}`,
        // If there's a PDF URL, add it as a media attachment
        ...(templateData.pdfUrl && { mediaUrl: [templateData.pdfUrl] }),
      });

      return message.sid;
    } catch (error) {
      logger.error('Error sending message via Twilio:', error);
      throw error;
    }
  }

  /**
   * Send message via Meta API
   * @private
   * @param to Recipient phone number
   * @param template Template
   * @param templateData Template data
   * @param settings WhatsApp settings
   */
  private async sendViaMeta(
    to: string,
    template: any,
    templateData: Record<string, any>,
    settings: any
  ): Promise<string> {
    try {
      // Format phone number
      const formattedTo = this.formatPhoneNumber(to);

      // Prepare message payload
      const payload = {
        messaging_product: 'whatsapp',
        recipient_type: 'individual',
        to: formattedTo,
        type: 'template',
        template: {
          name: template.name,
          language: {
            code: template.language || 'en_US',
          },
          components: this.formatMetaTemplateComponents(template, templateData),
        },
      };

      // Send message via Meta API
      const response = await axios.post(
        `https://graph.facebook.com/v17.0/${settings.phoneNumberId}/messages`,
        payload,
        {
          headers: {
            'Authorization': `Bearer ${settings.apiKey}`,
            'Content-Type': 'application/json',
          },
        }
      );

      return response.data.messages[0].id;
    } catch (error) {
      logger.error('Error sending message via Meta API:', error);
      throw error;
    }
  }

  /**
   * Format phone number for WhatsApp
   * @private
   * @param phoneNumber Phone number
   */
  private formatPhoneNumber(phoneNumber: string): string {
    // Remove any non-digit characters
    let formatted = phoneNumber.replace(/\D/g, '');

    // Ensure it has the country code
    if (!formatted.startsWith('60')) {
      // Add Malaysia country code if not present
      formatted = `60${formatted}`;
    }

    return formatted;
  }

  /**
   * Replace template variables with data
   * @private
   * @param template Template string
   * @param data Template data
   */
  private replaceTemplateVariables(template: string, data: Record<string, any>): string {
    return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return data[key] !== undefined ? data[key] : match;
    });
  }

  /**
   * Format template components for Meta API
   * @private
   * @param template Template
   * @param data Template data
   */
  private formatMetaTemplateComponents(template: any, data: Record<string, any>): any[] {
    // This is a simplified implementation
    // In a real app, you would need to format the components according to Meta's API specs
    const components = [];

    // Add header component if there's a PDF URL
    if (data.pdfUrl) {
      components.push({
        type: 'header',
        parameters: [
          {
            type: 'document',
            document: {
              link: data.pdfUrl,
              filename: data.pdfUrl.split('/').pop() || 'document.pdf',
            },
          },
        ],
      });
    }

    // Add body component with parameters
    const bodyParameters = Object.keys(data).map(key => ({
      type: 'text',
      text: data[key],
    }));

    if (bodyParameters.length > 0) {
      components.push({
        type: 'body',
        parameters: bodyParameters,
      });
    }

    return components;
  }

  /**
   * Generate invoice PDF
   * @private
   * @param invoice Invoice data
   */
  private async generateInvoicePdf(invoice: any): Promise<string> {
    // In a real implementation, this would generate a PDF
    // For now, we'll just return a mock URL
    return `https://yourdomain.com/invoices/${invoice.id}/pdf`;
  }

  /**
   * Generate receipt PDF
   * @private
   * @param payment Payment data
   */
  private async generateReceiptPdf(payment: any): Promise<string> {
    // In a real implementation, this would generate a PDF
    // For now, we'll just return a mock URL
    return `https://yourdomain.com/receipts/${payment.id}/pdf`;
  }
}

export const whatsappService = new WhatsAppService();
export default whatsappService;
