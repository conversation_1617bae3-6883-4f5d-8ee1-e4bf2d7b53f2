import express from 'express';
import { authenticateJWT } from '../middleware/auth';
import {
  createFixedAsset,
  getFixedAssets,
  getFixedAsset,
  updateFixedAsset,
  deleteFixedAsset,
  createAssetDepreciation,
  getAssetDepreciations,
  createAssetMaintenance,
  getAssetMaintenances,
  updateAssetMaintenance,
  deleteAssetMaintenance
} from '../controllers/assets.controller';

const router = express.Router();

// Apply authentication middleware to all asset routes
router.use(authenticateJWT);

// Fixed Asset routes
router.post('/fixed-assets', createFixedAsset);
router.get('/fixed-assets', getFixedAssets);
router.get('/fixed-assets/:id', getFixedAsset);
router.put('/fixed-assets/:id', updateFixedAsset);
router.delete('/fixed-assets/:id', deleteFixedAsset);

// Asset Depreciation routes
router.post('/depreciations', createAssetDepreciation);
router.get('/depreciations', getAssetDepreciations);

// Asset Maintenance routes
router.post('/maintenance', createAssetMaintenance);
router.get('/maintenance', getAssetMaintenances);
router.put('/maintenance/:id', updateAssetMaintenance);
router.delete('/maintenance/:id', deleteAssetMaintenance);

export default router;
