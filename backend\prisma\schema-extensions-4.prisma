// Project Management Module
model Project {
  id                String              @id @default(uuid())
  name              String
  description       String?
  startDate         DateTime
  endDate           DateTime?
  status            ProjectStatus       @default(PLANNING)
  budget            Float?
  actualCost        Float?              @default(0)
  completionPercentage Float            @default(0)
  clientId          String?             // Optional link to Customer
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  tasks             ProjectTask[]
  timeEntries       TimeEntry[]
  expenses          ProjectExpense[]
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([tenantId])
  @@index([clientId])
}

model ProjectTask {
  id                String              @id @default(uuid())
  projectId         String
  project           Project             @relation(fields: [projectId], references: [id], onDelete: Cascade)
  name              String
  description       String?
  startDate         DateTime?
  dueDate           DateTime?
  completionDate    DateTime?
  status            TaskStatus          @default(TODO)
  priority          Priority            @default(MEDIUM)
  assignedTo        String?             // Employee ID if assigned
  estimatedHours    Float?
  actualHours       Float               @default(0)
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  timeEntries       TimeEntry[]
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([projectId])
  @@index([tenantId])
}

model TimeEntry {
  id                String              @id @default(uuid())
  projectId         String
  project           Project             @relation(fields: [projectId], references: [id])
  taskId            String?
  task              ProjectTask?        @relation(fields: [taskId], references: [id])
  employeeId        String              // Employee who logged time
  description       String?
  startTime         DateTime
  endTime           DateTime?
  duration          Float               // Hours
  billable          Boolean             @default(true)
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([projectId])
  @@index([taskId])
  @@index([tenantId])
}

model ProjectExpense {
  id                String              @id @default(uuid())
  projectId         String
  project           Project             @relation(fields: [projectId], references: [id], onDelete: Cascade)
  description       String
  amount            Float
  date              DateTime
  category          String?
  receiptUrl        String?
  reimbursable      Boolean             @default(false)
  status            ExpenseStatus       @default(PENDING)
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([projectId])
  @@index([tenantId])
}

enum ProjectStatus {
  PLANNING
  ACTIVE
  ON_HOLD
  COMPLETED
  CANCELLED
}

enum TaskStatus {
  TODO
  IN_PROGRESS
  REVIEW
  DONE
  CANCELLED
}

enum Priority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum ExpenseStatus {
  PENDING
  APPROVED
  REJECTED
  REIMBURSED
}
