'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface StatCardProps {
  title: string;
  value: string | number;
  description?: string;
  icon?: React.ReactNode;
  trend?: {
    value: string;
    direction: 'up' | 'down' | 'neutral';
  };
  className?: string;
  variant?: 'default' | 'outline' | 'elevated' | 'glass' | 'gradient';
}

export function StatCard({
  title,
  value,
  description,
  icon,
  trend,
  className,
  variant = 'default',
}: StatCardProps) {
  return (
    <Card variant={variant} className={cn('overflow-hidden', className)}>
      <CardContent className="p-6">
        <div className="flex justify-between items-start">
          <div>
            <p className="text-sm font-medium text-neutral-700">{title}</p>
            <h4 className="text-2xl font-bold mt-1">{value}</h4>
            {description && (
              <p className="text-sm text-neutral-700 mt-1">{description}</p>
            )}
          </div>
          {icon && (
            <div className="p-2 bg-primary-50 rounded-lg text-primary-500">
              {icon}
            </div>
          )}
        </div>
        {trend && (
          <div className="mt-4">
            <Badge
              variant={
                trend.direction === 'up'
                  ? 'success'
                  : trend.direction === 'down'
                  ? 'destructive'
                  : 'secondary'
              }
              className="font-medium"
            >
              {trend.direction === 'up' && (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  className="w-3 h-3 mr-1"
                >
                  <path
                    fillRule="evenodd"
                    d="M12.577 4.878a.75.75 0 01.919-.53l4.78 1.281a.75.75 0 01.531.919l-1.281 4.78a.75.75 0 01-1.449-.387l.81-3.022a19.407 19.407 0 00-5.594 *********** 0 01-1.139.093L7 10.06l-4.72 4.72a.75.75 0 01-1.06-1.061l5.25-5.25a.75.75 0 011.06 0l3.074 3.073a20.923 20.923 0 015.545-4.931l-3.042-.815a.75.75 0 01-.53-.919z"
                    clipRule="evenodd"
                  />
                </svg>
              )}
              {trend.direction === 'down' && (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  className="w-3 h-3 mr-1"
                >
                  <path
                    fillRule="evenodd"
                    d="M1.22 5.222a.75.75 0 011.06 0L7 9.942l3.768-3.769a.75.75 0 011.113.058 20.908 20.908 0 013.813 7.254l1.574-2.727a.75.75 0 011.3.75l-2.475 4.286a.75.75 0 01-1.025.275l-4.287-2.475a.75.75 0 01.75-1.3l2.71 1.565a19.422 19.422 0 00-3.013-6.024L7.53 11.533a.75.75 0 01-1.06 0l-5.25-5.25a.75.75 0 010-1.06z"
                    clipRule="evenodd"
                  />
                </svg>
              )}
              {trend.value}
            </Badge>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

interface MetricGroupProps {
  title?: string;
  metrics: {
    label: string;
    value: string | number;
    change?: string;
    changeType?: 'positive' | 'negative' | 'neutral';
  }[];
  className?: string;
  columns?: 1 | 2 | 3 | 4;
}

export function MetricGroup({
  title,
  metrics,
  className,
  columns = 3,
}: MetricGroupProps) {
  const columnsClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 sm:grid-cols-2',
    3: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3',
    4: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-4',
  };

  return (
    <div className={cn('space-y-4', className)}>
      {title && <h3 className="text-lg font-medium">{title}</h3>}
      <div className={cn('grid gap-4', columnsClasses[columns])}>
        {metrics.map((metric, index) => (
          <div
            key={index}
            className="bg-white border border-neutral-200 rounded-lg p-4 shadow-sm"
          >
            <div className="text-sm font-medium text-neutral-700">
              {metric.label}
            </div>
            <div className="mt-1 flex items-baseline justify-between">
              <div className="text-2xl font-semibold">{metric.value}</div>
              {metric.change && (
                <div
                  className={cn(
                    'text-xs font-medium rounded-full px-2 py-0.5',
                    metric.changeType === 'positive' &&
                      'bg-green-100 text-green-800',
                    metric.changeType === 'negative' &&
                      'bg-red-100 text-red-800',
                    metric.changeType === 'neutral' &&
                      'bg-neutral-100 text-neutral-800'
                  )}
                >
                  {metric.change}
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

interface InfoCardProps {
  title: string;
  content: React.ReactNode;
  icon?: React.ReactNode;
  actions?: React.ReactNode;
  variant?: 'default' | 'info' | 'success' | 'warning' | 'error';
  className?: string;
}

export function InfoCard({
  title,
  content,
  icon,
  actions,
  variant = 'default',
  className,
}: InfoCardProps) {
  const variantClasses = {
    default: 'bg-white border-neutral-200',
    info: 'bg-blue-50 border-blue-200',
    success: 'bg-green-50 border-green-200',
    warning: 'bg-amber-50 border-amber-200',
    error: 'bg-red-50 border-red-200',
  };

  const iconClasses = {
    default: 'text-neutral-500',
    info: 'text-blue-500',
    success: 'text-green-500',
    warning: 'text-amber-500',
    error: 'text-red-500',
  };

  return (
    <div
      className={cn(
        'rounded-lg border p-4',
        variantClasses[variant],
        className
      )}
    >
      <div className="flex">
        {icon && (
          <div className={cn('mr-3 flex-shrink-0', iconClasses[variant])}>
            {icon}
          </div>
        )}
        <div className="flex-grow">
          <h3 className="text-sm font-medium">{title}</h3>
          <div className="mt-1 text-sm text-neutral-800">{content}</div>
          {actions && <div className="mt-3">{actions}</div>}
        </div>
      </div>
    </div>
  );
}
