'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { useToast } from '@/components/ui/use-toast';
import aiService from '@/lib/api/ai.service';
import { Badge } from '@/components/ui/badge';

export default function DocumentIntelligence() {
  const [file, setFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [results, setResults] = useState<any>(null);
  const { toast } = useToast();

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0]);
    }
  };

  const processDocument = async () => {
    if (!file) return;

    setIsProcessing(true);
    try {
      const formData = new FormData();
      formData.append('document', file);

      const response = await aiService.processDocument(formData);
      setResults(response);
      
      toast({
        title: 'Document Processed',
        description: 'Successfully extracted data from your document.',
        variant: 'default',
      });
    } catch (error: any) {
      toast({
        title: 'Processing Failed',
        description: error.message || 'Failed to process document',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const createInvoice = () => {
    // In a real implementation, this would create an invoice from the extracted data
    toast({
      title: 'Invoice Created',
      description: 'Invoice created from extracted data.',
      variant: 'default',
    });
    
    // Reset the form
    setFile(null);
    setResults(null);
  };

  return (
    <Card className="shadow-md">
      <CardHeader className="bg-white border-b pb-3">
        <CardTitle className="text-lg font-semibold flex items-center">
          <svg 
            className="w-5 h-5 mr-2 text-indigo-600" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24" 
            xmlns="http://www.w3.org/2000/svg"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" 
            />
          </svg>
          Document Intelligence
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-4">
        <div className="space-y-4">
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <Input
              type="file"
              onChange={handleFileChange}
              accept="image/*, application/pdf"
              className="hidden"
              id="document-upload"
            />
            <label
              htmlFor="document-upload"
              className="cursor-pointer flex flex-col items-center justify-center"
            >
              <svg
                className="w-12 h-12 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                />
              </svg>
              <p className="mt-2 text-sm text-gray-500">
                Click to upload or drag and drop
              </p>
              <p className="text-xs text-gray-500">
                PDF, PNG, JPG, or JPEG (max. 10MB)
              </p>
            </label>
            {file && (
              <div className="mt-4 text-sm text-gray-700">
                <div className="flex items-center justify-center">
                  <svg 
                    className="w-4 h-4 mr-1.5 text-indigo-600" 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24" 
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path 
                      strokeLinecap="round" 
                      strokeLinejoin="round" 
                      strokeWidth={2} 
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" 
                    />
                  </svg>
                  <span className="font-medium">{file.name}</span>
                </div>
              </div>
            )}
          </div>

          <Button
            onClick={processDocument}
            disabled={!file || isProcessing}
            className="w-full"
          >
            {isProcessing ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Processing Document...
              </>
            ) : (
              <>
                <svg 
                  className="w-4 h-4 mr-2" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24" 
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" 
                  />
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" 
                  />
                </svg>
                Process Document
              </>
            )}
          </Button>

          {results && (
            <div className="mt-6 space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Extracted Data</h3>
                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                  AI Processed
                </Badge>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Invoice Number</p>
                    <p className="text-sm font-semibold">{results.invoiceNumber}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Date</p>
                    <p className="text-sm font-semibold">{results.date}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Vendor</p>
                    <p className="text-sm font-semibold">{results.vendor}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Total Amount</p>
                    <p className="text-sm font-semibold">{results.totalAmount}</p>
                  </div>
                </div>
                
                <div className="mt-4">
                  <p className="text-sm font-medium text-gray-500 mb-2">Line Items</p>
                  <div className="bg-white rounded-md border border-gray-200">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left py-2 px-3 font-medium text-gray-500">Description</th>
                          <th className="text-right py-2 px-3 font-medium text-gray-500">Quantity</th>
                          <th className="text-right py-2 px-3 font-medium text-gray-500">Unit Price</th>
                          <th className="text-right py-2 px-3 font-medium text-gray-500">Amount</th>
                        </tr>
                      </thead>
                      <tbody>
                        {results.lineItems.map((item: any, index: number) => (
                          <tr key={index} className={index < results.lineItems.length - 1 ? "border-b" : ""}>
                            <td className="py-2 px-3">{item.description}</td>
                            <td className="py-2 px-3 text-right">{item.quantity}</td>
                            <td className="py-2 px-3 text-right">{item.unitPrice}</td>
                            <td className="py-2 px-3 text-right">{item.amount}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
                
                <div className="mt-4 flex justify-end">
                  <Button
                    onClick={createInvoice}
                    className="bg-indigo-600 hover:bg-indigo-700"
                  >
                    <svg 
                      className="w-4 h-4 mr-2" 
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24" 
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path 
                        strokeLinecap="round" 
                        strokeLinejoin="round" 
                        strokeWidth={2} 
                        d="M12 6v6m0 0v6m0-6h6m-6 0H6" 
                      />
                    </svg>
                    Create Invoice
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
