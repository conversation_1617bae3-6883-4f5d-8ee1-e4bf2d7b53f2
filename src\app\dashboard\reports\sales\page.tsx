'use client';

import { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { DatePicker } from '@/components/ui/date-picker';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { useToast } from '@/components/ui/use-toast';
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { Download, FileText, Printer, Search } from 'lucide-react';
import { format, subMonths } from 'date-fns';
import reportService from '@/lib/api/report.service';

// Mock data for demonstration
const mockSalesByCustomer = [
  {
    customerId: '1',
    customerName: 'Acme Corporation',
    invoiceCount: 12,
    totalAmount: 45000,
    paidAmount: 42000,
    outstandingAmount: 3000,
  },
  {
    customerId: '2',
    customerName: 'TechStart Inc.',
    invoiceCount: 8,
    totalAmount: 32000,
    paidAmount: 28000,
    outstandingAmount: 4000,
  },
  {
    customerId: '3',
    customerName: 'Global Enterprises',
    invoiceCount: 15,
    totalAmount: 60000,
    paidAmount: 55000,
    outstandingAmount: 5000,
  },
  {
    customerId: '4',
    customerName: 'Local Business',
    invoiceCount: 6,
    totalAmount: 18000,
    paidAmount: 15000,
    outstandingAmount: 3000,
  },
  {
    customerId: '5',
    customerName: 'Mega Industries',
    invoiceCount: 10,
    totalAmount: 40000,
    paidAmount: 38000,
    outstandingAmount: 2000,
  },
];

const mockSalesByProduct = [
  {
    productId: '1',
    productName: 'Premium Package',
    quantity: 45,
    totalAmount: 67500,
  },
  {
    productId: '2',
    productName: 'Standard Service',
    quantity: 120,
    totalAmount: 60000,
  },
  {
    productId: '3',
    productName: 'Basic Plan',
    quantity: 85,
    totalAmount: 25500,
  },
  {
    productId: '4',
    productName: 'Consulting Hours',
    quantity: 200,
    totalAmount: 30000,
  },
  {
    productId: '5',
    productName: 'Custom Development',
    quantity: 15,
    totalAmount: 45000,
  },
];

const mockSalesByMonth = [
  {
    month: '2023-01',
    monthName: 'January 2023',
    invoiceCount: 18,
    totalAmount: 15000,
    paidAmount: 14000,
  },
  {
    month: '2023-02',
    monthName: 'February 2023',
    invoiceCount: 20,
    totalAmount: 18000,
    paidAmount: 17000,
  },
  {
    month: '2023-03',
    monthName: 'March 2023',
    invoiceCount: 22,
    totalAmount: 20000,
    paidAmount: 19000,
  },
  {
    month: '2023-04',
    monthName: 'April 2023',
    invoiceCount: 25,
    totalAmount: 22000,
    paidAmount: 20000,
  },
  {
    month: '2023-05',
    monthName: 'May 2023',
    invoiceCount: 28,
    totalAmount: 25000,
    paidAmount: 23000,
  },
  {
    month: '2023-06',
    monthName: 'June 2023',
    invoiceCount: 30,
    totalAmount: 28000,
    paidAmount: 25000,
  },
  {
    month: '2023-07',
    monthName: 'July 2023',
    invoiceCount: 32,
    totalAmount: 30000,
    paidAmount: 27000,
  },
  {
    month: '2023-08',
    monthName: 'August 2023',
    invoiceCount: 35,
    totalAmount: 32000,
    paidAmount: 29000,
  },
  {
    month: '2023-09',
    monthName: 'September 2023',
    invoiceCount: 30,
    totalAmount: 28000,
    paidAmount: 25000,
  },
  {
    month: '2023-10',
    monthName: 'October 2023',
    invoiceCount: 28,
    totalAmount: 25000,
    paidAmount: 22000,
  },
  {
    month: '2023-11',
    monthName: 'November 2023',
    invoiceCount: 25,
    totalAmount: 22000,
    paidAmount: 20000,
  },
  {
    month: '2023-12',
    monthName: 'December 2023',
    invoiceCount: 22,
    totalAmount: 20000,
    paidAmount: 18000,
  },
];

export default function SalesReportsPage() {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('by-customer');
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [dateRange, setDateRange] = useState({
    startDate: subMonths(new Date(), 12),
    endDate: new Date(),
  });
  const [exportFormat, setExportFormat] = useState('pdf');
  const [searchQuery, setSearchQuery] = useState('');

  // State for report data
  const [salesByCustomer, setSalesByCustomer] = useState(mockSalesByCustomer);
  const [salesByProduct, setSalesByProduct] = useState(mockSalesByProduct);
  const [salesByMonth, setSalesByMonth] = useState(mockSalesByMonth);

  // Load report data
  useEffect(() => {
    loadReportData();
  }, [activeTab, dateRange]);

  const loadReportData = async () => {
    setIsLoading(true);
    try {
      // In a real app, these would be API calls
      // For now, we'll use mock data

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      switch (activeTab) {
        case 'by-customer':
          // const customerData = await reportService.getSalesReport('by_customer', {
          //   startDate: format(dateRange.startDate, 'yyyy-MM-dd'),
          //   endDate: format(dateRange.endDate, 'yyyy-MM-dd'),
          // });
          // setSalesByCustomer(customerData.salesByCustomer);
          setSalesByCustomer(mockSalesByCustomer);
          break;

        case 'by-product':
          // const productData = await reportService.getSalesReport('by_product', {
          //   startDate: format(dateRange.startDate, 'yyyy-MM-dd'),
          //   endDate: format(dateRange.endDate, 'yyyy-MM-dd'),
          // });
          // setSalesByProduct(productData.salesByProduct);
          setSalesByProduct(mockSalesByProduct);
          break;

        case 'by-time':
          // const timeData = await reportService.getSalesReport('by_time', {
          //   startDate: format(dateRange.startDate, 'yyyy-MM-dd'),
          //   endDate: format(dateRange.endDate, 'yyyy-MM-dd'),
          // });
          // setSalesByMonth(timeData.salesByMonth);
          setSalesByMonth(mockSalesByMonth);
          break;
      }
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to load report data',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleExport = async () => {
    setIsLoading(true);
    try {
      // In a real app, this would call the API to export the report
      // const response = await reportService.exportReport(
      //   'sales',
      //   activeTab.replace('-', '_'),
      //   exportFormat as any,
      //   {
      //     startDate: format(dateRange.startDate, 'yyyy-MM-dd'),
      //     endDate: format(dateRange.endDate, 'yyyy-MM-dd'),
      //   }
      // );

      // Simulate export
      await new Promise(resolve => setTimeout(resolve, 1000));

      toast({
        title: 'Export Successful',
        description: `Report has been exported as ${exportFormat.toUpperCase()}`,
        variant: 'default',
      });
    } catch (error: any) {
      toast({
        title: 'Export Failed',
        description: error.message || 'Failed to export report',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-MY', {
      style: 'currency',
      currency: 'MYR',
      minimumFractionDigits: 2,
    }).format(value);
  };

  // Filter data based on search query
  const filteredSalesByCustomer = salesByCustomer.filter(customer =>
    customer.customerName.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredSalesByProduct = salesByProduct.filter(product =>
    product.productName.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between bg-white p-6 rounded-lg shadow-sm mb-6">
        <div>
          <h1 className="text-2xl font-bold">Sales Reports</h1>
          <p className="text-text-secondary">Analyze your sales performance</p>
        </div>
        <div className="mt-4 md:mt-0 flex flex-col sm:flex-row gap-2">
          <Select value={exportFormat} onValueChange={setExportFormat}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="Export As" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="pdf">PDF</SelectItem>
              <SelectItem value="xlsx">Excel</SelectItem>
              <SelectItem value="csv">CSV</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={handleExport} disabled={isLoading}>
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" onClick={() => window.print()} disabled={isLoading}>
            <Printer className="w-4 h-4 mr-2" />
            Print
          </Button>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="flex-1">
          <label className="block text-sm font-medium mb-1">Start Date</label>
          <DatePicker
            date={dateRange.startDate}
            setDate={(date) => setDateRange(prev => ({ ...prev, startDate: date || prev.startDate }))}
          />
        </div>
        <div className="flex-1">
          <label className="block text-sm font-medium mb-1">End Date</label>
          <DatePicker
            date={dateRange.endDate}
            setDate={(date) => setDateRange(prev => ({ ...prev, endDate: date || prev.endDate }))}
          />
        </div>
      </div>

      <Tabs defaultValue="by-customer" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-6 bg-white p-1 rounded-lg border">
          <TabsTrigger value="by-customer" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">By Customer</TabsTrigger>
          <TabsTrigger value="by-product" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">By Product</TabsTrigger>
          <TabsTrigger value="by-time" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">By Time</TabsTrigger>
        </TabsList>

        <div className="mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
            <Input
              placeholder={`Search ${activeTab === 'by-customer' ? 'customers' : activeTab === 'by-product' ? 'products' : 'months'}`}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <TabsContent value="by-customer">
          <Card className="mb-6">
            <CardHeader className="pb-2 border-b">
              <CardTitle className="text-lg font-bold text-text-primary">Sales by Customer</CardTitle>
              <CardDescription className="text-text-secondary">
                For the period {format(dateRange.startDate, 'MMMM d, yyyy')} to {format(dateRange.endDate, 'MMMM d, yyyy')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={filteredSalesByCustomer}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="customerName" />
                    <YAxis />
                    <Tooltip formatter={(value) => formatCurrency(value as number)} />
                    <Legend />
                    <Bar dataKey="totalAmount" name="Total Sales" fill="#8884d8" />
                    <Bar dataKey="paidAmount" name="Paid Amount" fill="#82ca9d" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          <Card className="border-none shadow-md">
            <CardHeader className="pb-2 border-b">
              <CardTitle className="text-lg font-bold text-text-primary">Customer Sales Details</CardTitle>
              <CardDescription className="text-text-secondary">
                Detailed breakdown of sales by customer
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full text-sm text-left" className="w-full border-collapse">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-3 px-4 font-medium text-text-secondary">Customer</th>
                      <th className="text-right py-3 px-4 font-medium text-text-secondary">Invoices</th>
                      <th className="text-right py-3 px-4 font-medium text-text-secondary">Total Amount</th>
                      <th className="text-right py-3 px-4 font-medium text-text-secondary">Paid Amount</th>
                      <th className="text-right py-3 px-4 font-medium text-text-secondary">Outstanding</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredSalesByCustomer.map((customer) => (
                      <tr key={customer.customerId} className="border-b ">
                        <td className="py-3 px-4">{customer.customerName}</td>
                        <td className="py-3 px-4 text-right">{customer.invoiceCount}</td>
                        <td className="py-3 px-4 text-right">{formatCurrency(customer.totalAmount)}</td>
                        <td className="py-3 px-4 text-right">{formatCurrency(customer.paidAmount)}</td>
                        <td className="py-3 px-4 text-right">{formatCurrency(customer.outstandingAmount)}</td>
                      </tr>
                    ))}
                  </tbody>
                  <tfoot>
                    <tr className="border-t bg-gray-50">
                      <td className="py-3 px-4 font-medium">Total</td>
                      <td className="py-3 px-4 text-right font-medium">
                        {filteredSalesByCustomer.reduce((sum, customer) => sum + customer.invoiceCount, 0)}
                      </td>
                      <td className="py-3 px-4 text-right font-medium">
                        {formatCurrency(filteredSalesByCustomer.reduce((sum, customer) => sum + customer.totalAmount, 0))}
                      </td>
                      <td className="py-3 px-4 text-right font-medium">
                        {formatCurrency(filteredSalesByCustomer.reduce((sum, customer) => sum + customer.paidAmount, 0))}
                      </td>
                      <td className="py-3 px-4 text-right font-medium">
                        {formatCurrency(filteredSalesByCustomer.reduce((sum, customer) => sum + customer.outstandingAmount, 0))}
                      </td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="by-product">
          <Card className="mb-6">
            <CardHeader className="pb-2 border-b">
              <CardTitle className="text-lg font-bold text-text-primary">Sales by Product</CardTitle>
              <CardDescription className="text-text-secondary">
                For the period {format(dateRange.startDate, 'MMMM d, yyyy')} to {format(dateRange.endDate, 'MMMM d, yyyy')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={filteredSalesByProduct}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="totalAmount"
                      nameKey="productName"
                    >
                      {filteredSalesByProduct.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={`#${Math.floor(Math.random() * 16777215).toString(16)}`} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => formatCurrency(value as number)} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          <Card className="border-none shadow-md">
            <CardHeader className="pb-2 border-b">
              <CardTitle className="text-lg font-bold text-text-primary">Product Sales Details</CardTitle>
              <CardDescription className="text-text-secondary">
                Detailed breakdown of sales by product
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full text-sm text-left" className="w-full border-collapse">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-3 px-4 font-medium text-text-secondary">Product</th>
                      <th className="text-right py-3 px-4 font-medium text-text-secondary">Quantity</th>
                      <th className="text-right py-3 px-4 font-medium text-text-secondary">Total Amount</th>
                      <th className="text-right py-3 px-4 font-medium text-text-secondary">Average Price</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredSalesByProduct.map((product) => (
                      <tr key={product.productId} className="border-b ">
                        <td className="py-3 px-4">{product.productName}</td>
                        <td className="py-3 px-4 text-right">{product.quantity}</td>
                        <td className="py-3 px-4 text-right">{formatCurrency(product.totalAmount)}</td>
                        <td className="py-3 px-4 text-right">
                          {formatCurrency(product.totalAmount / product.quantity)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                  <tfoot>
                    <tr className="border-t bg-gray-50">
                      <td className="py-3 px-4 font-medium">Total</td>
                      <td className="py-3 px-4 text-right font-medium">
                        {filteredSalesByProduct.reduce((sum, product) => sum + product.quantity, 0)}
                      </td>
                      <td className="py-3 px-4 text-right font-medium">
                        {formatCurrency(filteredSalesByProduct.reduce((sum, product) => sum + product.totalAmount, 0))}
                      </td>
                      <td className="py-3 px-4 text-right font-medium"></td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="by-time">
          <Card className="mb-6">
            <CardHeader className="pb-2 border-b">
              <CardTitle className="text-lg font-bold text-text-primary">Sales Trend</CardTitle>
              <CardDescription className="text-text-secondary">
                For the period {format(dateRange.startDate, 'MMMM d, yyyy')} to {format(dateRange.endDate, 'MMMM d, yyyy')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={salesByMonth}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="monthName" />
                    <YAxis />
                    <Tooltip formatter={(value) => formatCurrency(value as number)} />
                    <Legend />
                    <Line type="monotone" dataKey="totalAmount" name="Total Sales" stroke="#8884d8" activeDot={{ r: 8 }} />
                    <Line type="monotone" dataKey="paidAmount" name="Paid Amount" stroke="#82ca9d" />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          <Card className="border-none shadow-md">
            <CardHeader className="pb-2 border-b">
              <CardTitle className="text-lg font-bold text-text-primary">Monthly Sales Details</CardTitle>
              <CardDescription className="text-text-secondary">
                Detailed breakdown of sales by month
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full text-sm text-left" className="w-full border-collapse">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-3 px-4 font-medium text-text-secondary">Month</th>
                      <th className="text-right py-3 px-4 font-medium text-text-secondary">Invoices</th>
                      <th className="text-right py-3 px-4 font-medium text-text-secondary">Total Amount</th>
                      <th className="text-right py-3 px-4 font-medium text-text-secondary">Paid Amount</th>
                      <th className="text-right py-3 px-4 font-medium text-text-secondary">Collection Rate</th>
                    </tr>
                  </thead>
                  <tbody>
                    {salesByMonth.map((month) => (
                      <tr key={month.month} className="border-b ">
                        <td className="py-3 px-4">{month.monthName}</td>
                        <td className="py-3 px-4 text-right">{month.invoiceCount}</td>
                        <td className="py-3 px-4 text-right">{formatCurrency(month.totalAmount)}</td>
                        <td className="py-3 px-4 text-right">{formatCurrency(month.paidAmount)}</td>
                        <td className="py-3 px-4 text-right">
                          {((month.paidAmount / month.totalAmount) * 100).toFixed(2)}%
                        </td>
                      </tr>
                    ))}
                  </tbody>
                  <tfoot>
                    <tr className="border-t bg-gray-50">
                      <td className="py-3 px-4 font-medium">Total</td>
                      <td className="py-3 px-4 text-right font-medium">
                        {salesByMonth.reduce((sum, month) => sum + month.invoiceCount, 0)}
                      </td>
                      <td className="py-3 px-4 text-right font-medium">
                        {formatCurrency(salesByMonth.reduce((sum, month) => sum + month.totalAmount, 0))}
                      </td>
                      <td className="py-3 px-4 text-right font-medium">
                        {formatCurrency(salesByMonth.reduce((sum, month) => sum + month.paidAmount, 0))}
                      </td>
                      <td className="py-3 px-4 text-right font-medium">
                        {(salesByMonth.reduce((sum, month) => sum + month.paidAmount, 0) /
                          salesByMonth.reduce((sum, month) => sum + month.totalAmount, 0) * 100).toFixed(2)}%
                      </td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
