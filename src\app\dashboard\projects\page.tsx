'use client';

import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { PlusCircle } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useRouter } from 'next/navigation';

// Mock data for demonstration
const mockProjects = [
  {
    id: '1',
    name: 'Website Redesign',
    description: 'Redesign company website',
    startDate: '2023-05-01',
    endDate: '2023-07-31',
    status: 'ACTIVE',
    budget: 15000.00,
    actualCost: 5000.00,
    completionPercentage: 35,
    clientName: 'ABC Corporation',
  },
  {
    id: '2',
    name: 'Mobile App Development',
    description: 'Develop iOS and Android apps',
    startDate: '2023-06-01',
    endDate: '2023-09-30',
    status: 'PLANNING',
    budget: 50000.00,
    actualCost: 0.00,
    completionPercentage: 0,
    clientName: 'XYZ Tech',
  },
  {
    id: '3',
    name: 'Office Renovation',
    description: 'Renovate main office space',
    startDate: '2023-04-15',
    endDate: '2023-05-30',
    status: 'COMPLETED',
    budget: 25000.00,
    actualCost: 27500.00,
    completionPercentage: 100,
    clientName: 'Internal',
  },
];

const mockTasks = [
  {
    id: '1',
    projectId: '1',
    projectName: 'Website Redesign',
    name: 'Design Homepage',
    description: 'Create new homepage design',
    startDate: '2023-05-01',
    dueDate: '2023-05-15',
    status: 'DONE',
    priority: 'HIGH',
    assignedTo: 'Jane Smith',
    estimatedHours: 20,
    actualHours: 18,
  },
  {
    id: '2',
    projectId: '1',
    projectName: 'Website Redesign',
    name: 'Develop Frontend',
    description: 'Implement frontend using React',
    startDate: '2023-05-16',
    dueDate: '2023-06-15',
    status: 'IN_PROGRESS',
    priority: 'HIGH',
    assignedTo: 'John Doe',
    estimatedHours: 80,
    actualHours: 30,
  },
  {
    id: '3',
    projectId: '1',
    projectName: 'Website Redesign',
    name: 'Backend Integration',
    description: 'Connect frontend to API',
    startDate: '2023-06-16',
    dueDate: '2023-07-15',
    status: 'TODO',
    priority: 'MEDIUM',
    assignedTo: 'John Doe',
    estimatedHours: 40,
    actualHours: 0,
  },
];

const mockTimeEntries = [
  {
    id: '1',
    projectId: '1',
    projectName: 'Website Redesign',
    taskId: '1',
    taskName: 'Design Homepage',
    employeeName: 'Jane Smith',
    startTime: '2023-05-01T09:00:00',
    endTime: '2023-05-01T17:00:00',
    duration: 8,
    description: 'Working on homepage mockups',
  },
  {
    id: '2',
    projectId: '1',
    projectName: 'Website Redesign',
    taskId: '2',
    taskName: 'Develop Frontend',
    employeeName: 'John Doe',
    startTime: '2023-05-16T09:00:00',
    endTime: '2023-05-16T17:00:00',
    duration: 8,
    description: 'Setting up React project',
  },
];

const mockExpenses = [
  {
    id: '1',
    projectId: '1',
    projectName: 'Website Redesign',
    description: 'Design software license',
    amount: 500.00,
    date: '2023-05-02',
    category: 'Software',
    status: 'APPROVED',
  },
  {
    id: '2',
    projectId: '3',
    projectName: 'Office Renovation',
    description: 'Furniture purchase',
    amount: 5000.00,
    date: '2023-04-20',
    category: 'Equipment',
    status: 'APPROVED',
  },
];

// Column definitions for tables
const projectColumns: ColumnDef<any>[] = [
  {
    accessorKey: 'name',
    header: 'Name',
  },
  {
    accessorKey: 'clientName',
    header: 'Client',
  },
  {
    accessorKey: 'startDate',
    header: 'Start Date',
    cell: ({ row }) => {
      return new Date(row.getValue('startDate')).toLocaleDateString();
    },
  },
  {
    accessorKey: 'endDate',
    header: 'End Date',
    cell: ({ row }) => {
      return new Date(row.getValue('endDate')).toLocaleDateString();
    },
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.getValue('status');
      return status === 'ACTIVE' ? (
        <Badge className="bg-green-500">Active</Badge>
      ) : status === 'PLANNING' ? (
        <Badge className="bg-blue-500">Planning</Badge>
      ) : status === 'COMPLETED' ? (
        <Badge className="bg-purple-500">Completed</Badge>
      ) : status === 'ON_HOLD' ? (
        <Badge className="bg-yellow-500">On Hold</Badge>
      ) : (
        <Badge variant="destructive">Cancelled</Badge>
      );
    },
  },
  {
    accessorKey: 'completionPercentage',
    header: 'Progress',
    cell: ({ row }) => {
      const progress = row.getValue('completionPercentage');
      return (
        <div className="w-full">
          <Progress value={progress} className="h-2" />
          <div className="text-xs text-right mt-1">{progress}%</div>
        </div>
      );
    },
  },
  {
    id: 'actions',
    cell: ({ row }) => {
      return (
        <div className="flex items-center justify-end gap-2">
          <Button variant="ghost" size="sm">
            View
          </Button>
          <Button variant="ghost" size="sm">
            Edit
          </Button>
        </div>
      );
    },
  },
];

const taskColumns: ColumnDef<any>[] = [
  {
    accessorKey: 'name',
    header: 'Task',
  },
  {
    accessorKey: 'projectName',
    header: 'Project',
  },
  {
    accessorKey: 'dueDate',
    header: 'Due Date',
    cell: ({ row }) => {
      return new Date(row.getValue('dueDate')).toLocaleDateString();
    },
  },
  {
    accessorKey: 'assignedTo',
    header: 'Assigned To',
  },
  {
    accessorKey: 'priority',
    header: 'Priority',
    cell: ({ row }) => {
      const priority = row.getValue('priority');
      return priority === 'HIGH' ? (
        <Badge className="bg-red-500">High</Badge>
      ) : priority === 'MEDIUM' ? (
        <Badge className="bg-yellow-500">Medium</Badge>
      ) : (
        <Badge className="bg-blue-500">Low</Badge>
      );
    },
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.getValue('status');
      return status === 'DONE' ? (
        <Badge className="bg-green-500">Done</Badge>
      ) : status === 'IN_PROGRESS' ? (
        <Badge className="bg-blue-500">In Progress</Badge>
      ) : status === 'REVIEW' ? (
        <Badge className="bg-purple-500">Review</Badge>
      ) : status === 'TODO' ? (
        <Badge variant="outline">To Do</Badge>
      ) : (
        <Badge variant="destructive">Cancelled</Badge>
      );
    },
  },
  {
    id: 'actions',
    cell: ({ row }) => {
      return (
        <div className="flex items-center justify-end gap-2">
          <Button variant="ghost" size="sm">
            View
          </Button>
          <Button variant="ghost" size="sm">
            Edit
          </Button>
        </div>
      );
    },
  },
];

const timeEntryColumns: ColumnDef<any>[] = [
  {
    accessorKey: 'employeeName',
    header: 'Employee',
  },
  {
    accessorKey: 'projectName',
    header: 'Project',
  },
  {
    accessorKey: 'taskName',
    header: 'Task',
  },
  {
    accessorKey: 'startTime',
    header: 'Date',
    cell: ({ row }) => {
      return new Date(row.getValue('startTime')).toLocaleDateString();
    },
  },
  {
    accessorKey: 'duration',
    header: 'Hours',
    cell: ({ row }) => {
      return `${row.getValue('duration')} hrs`;
    },
  },
  {
    accessorKey: 'description',
    header: 'Description',
  },
  {
    id: 'actions',
    cell: ({ row }) => {
      return (
        <div className="flex items-center justify-end gap-2">
          <Button variant="ghost" size="sm">
            Edit
          </Button>
          <Button variant="ghost" size="sm" className="text-red-500">
            Delete
          </Button>
        </div>
      );
    },
  },
];

const expenseColumns: ColumnDef<any>[] = [
  {
    accessorKey: 'projectName',
    header: 'Project',
  },
  {
    accessorKey: 'description',
    header: 'Description',
  },
  {
    accessorKey: 'category',
    header: 'Category',
  },
  {
    accessorKey: 'date',
    header: 'Date',
    cell: ({ row }) => {
      return new Date(row.getValue('date')).toLocaleDateString();
    },
  },
  {
    accessorKey: 'amount',
    header: 'Amount',
    cell: ({ row }) => {
      return `RM ${row.getValue('amount').toFixed(2)}`;
    },
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.getValue('status');
      return status === 'APPROVED' ? (
        <Badge className="bg-green-500">Approved</Badge>
      ) : status === 'PENDING' ? (
        <Badge variant="outline">Pending</Badge>
      ) : status === 'REJECTED' ? (
        <Badge variant="destructive">Rejected</Badge>
      ) : (
        <Badge className="bg-blue-500">Reimbursed</Badge>
      );
    },
  },
  {
    id: 'actions',
    cell: ({ row }) => {
      return (
        <div className="flex items-center justify-end gap-2">
          <Button variant="ghost" size="sm">
            View
          </Button>
          <Button variant="ghost" size="sm">
            Edit
          </Button>
        </div>
      );
    },
  },
];

export default function ProjectsPage() {
  const { toast } = useToast();
  const router = useRouter();

  return (
    <div className="space-y-4">
      {/* Page header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between bg-white p-3 md:p-4 rounded-lg shadow-sm mb-3">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <svg className="w-6 h-6 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            Project Management
          </h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage projects, tasks, time tracking, and expenses
          </p>
        </div>
        <div className="mt-4 md:mt-0 flex space-x-3">
          <Button variant="outline" size="sm">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
            </svg>
            Filter
          </Button>
          <Button size="sm" className="bg-indigo-600 hover:bg-indigo-700">
            <PlusCircle className="mr-2 h-4 w-4" /> Create Project
          </Button>
        </div>
      </div>


      {/* Projects Table */}
      <Card className="border-none shadow-md">
        <CardHeader className="pb-2 border-b">
          <CardTitle className="text-lg font-bold">Projects</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full text-sm text-left">
              <thead className="text-xs font-medium text-gray-500 bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-6 py-3">Name</th>
                  <th className="px-6 py-3">Client</th>
                  <th className="px-6 py-3">Start Date</th>
                  <th className="px-6 py-3">End Date</th>
                  <th className="px-6 py-3">Status</th>
                  <th className="px-6 py-3">Progress</th>
                  <th className="px-6 py-3 text-right">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {mockProjects.map((project) => (
                  <tr key={project.id} className="bg-white hover:bg-gray-50 transition-colors">
                    <td className="px-6 py-4 font-medium text-indigo-600">
                      {project.name}
                    </td>
                    <td className="px-6 py-4">
                      {project.clientName}
                    </td>
                    <td className="px-6 py-4 text-gray-500">
                      {new Date(project.startDate).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 text-gray-500">
                      {new Date(project.endDate).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        project.status === 'ACTIVE' ? 'bg-green-100 text-green-800' :
                        project.status === 'PLANNING' ? 'bg-blue-100 text-blue-800' :
                        project.status === 'COMPLETED' ? 'bg-purple-100 text-purple-800' :
                        project.status === 'ON_HOLD' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {project.status === 'ACTIVE' && (
                          <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        )}
                        {project.status === 'PLANNING' && (
                          <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                          </svg>
                        )}
                        {project.status === 'ACTIVE' ? 'Active' :
                         project.status === 'PLANNING' ? 'Planning' :
                         project.status === 'COMPLETED' ? 'Completed' :
                         project.status === 'ON_HOLD' ? 'On Hold' : 'Cancelled'}
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      <div className="w-full">
                        <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                          <div
                            className={`h-full ${
                              project.completionPercentage >= 100 ? 'bg-green-500' :
                              project.completionPercentage > 50 ? 'bg-blue-500' :
                              project.completionPercentage > 25 ? 'bg-yellow-500' :
                              'bg-red-500'
                            }`}
                            style={{ width: `${project.completionPercentage}%` }}
                          ></div>
                        </div>
                        <div className="text-xs text-right mt-1">{project.completionPercentage}%</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 text-right">
                      <div className="flex justify-end space-x-2">
                        <Button variant="ghost" size="sm" className="text-indigo-600 hover:text-indigo-800 hover:bg-indigo-50">
                          View
                        </Button>
                        <Button variant="ghost" size="sm" className="text-gray-600 hover:text-gray-800 hover:bg-gray-50">
                          Edit
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          <div className="flex items-center justify-between p-6 border-t border-gray-200">
            <div className="text-sm text-gray-500">
              Showing <span className="font-medium">1</span> to <span className="font-medium">{mockProjects.length}</span> of <span className="font-medium">{mockProjects.length}</span> results
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm" disabled>
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                Previous
              </Button>
              <Button variant="outline" size="sm">
                Next
                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
