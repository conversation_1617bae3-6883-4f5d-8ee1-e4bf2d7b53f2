import { Router } from 'express';
import authRoutes from './auth.routes';
import tenantRoutes from './tenant.routes';
import invoiceRoutes from './invoice.routes';
import whatsappRoutes from './whatsapp.routes';
import lhdnRoutes from './lhdn.routes';
import lhdnConfigRoutes from './lhdn-config.routes';
import validationRoutes from './validation.routes';
import certificateRoutes from './certificate.routes';
import documentRoutes from './document.routes';
import healthRoutes from './health.routes';
import aiRoutes from './ai.routes';
import pdfRoutes from './pdf.routes';
import templateRoutes from './template.routes';
import inventoryRoutes from './inventory.routes';
import hrRoutes from './hr.routes';
import procurementRoutes from './procurement.routes';
import assetsRoutes from './assets.routes';
import projectRoutes from './project.routes';
import notificationRoutes from './notification.routes';

const router = Router();

// Register all routes
router.use('/auth', authRoutes);
router.use('/tenants', tenantRoutes);
router.use('/invoices', invoiceRoutes);
router.use('/whatsapp', whatsappRoutes);
router.use('/lhdn', lhdnRoutes);
router.use('/lhdn-config', lhdnConfigRoutes);
router.use('/validation', validationRoutes);
router.use('/certificates', certificateRoutes);
router.use('/documents', documentRoutes);
router.use('/health', healthRoutes);
router.use('/ai', aiRoutes);
router.use('/pdf', pdfRoutes);
router.use('/templates', templateRoutes);
router.use('/notifications', notificationRoutes);

// ERP module routes
router.use('/inventory', inventoryRoutes);
router.use('/hr', hrRoutes);
router.use('/procurement', procurementRoutes);
router.use('/assets', assetsRoutes);
router.use('/projects', projectRoutes);

export default router;
