import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';

const prisma = new PrismaClient();

/**
 * Get all templates for a tenant
 * @param req Request
 * @param res Response
 */
export const getTemplates = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId } = req.user;

    const templates = await prisma.invoiceTemplate.findMany({
      where: { tenantId },
      orderBy: { updatedAt: 'desc' },
    });

    return res.status(200).json({ templates });
  } catch (error: any) {
    logger.error('Error fetching templates', { error: error.message, stack: error.stack });
    return res.status(500).json({ message: 'Failed to fetch templates', error: error.message });
  }
};

/**
 * Get a template by ID
 * @param req Request
 * @param res Response
 */
export const getTemplateById = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId } = req.user;
    const { id } = req.params;

    const template = await prisma.invoiceTemplate.findFirst({
      where: {
        id,
        tenantId,
      },
    });

    if (!template) {
      return res.status(404).json({ message: 'Template not found or does not belong to your tenant' });
    }

    return res.status(200).json({ template });
  } catch (error: any) {
    logger.error('Error fetching template', { error: error.message, stack: error.stack });
    return res.status(500).json({ message: 'Failed to fetch template', error: error.message });
  }
};

/**
 * Create a new template
 * @param req Request
 * @param res Response
 */
export const createTemplate = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId } = req.user;
    const { name, template } = req.body;

    if (!name || !template) {
      return res.status(400).json({ message: 'Name and template are required' });
    }

    const newTemplate = await prisma.invoiceTemplate.create({
      data: {
        name,
        template,
        tenant: {
          connect: { id: tenantId },
        },
      },
    });

    logger.info('Template created successfully', { templateId: newTemplate.id, tenantId });
    return res.status(201).json({ template: newTemplate });
  } catch (error: any) {
    logger.error('Error creating template', { error: error.message, stack: error.stack });
    return res.status(500).json({ message: 'Failed to create template', error: error.message });
  }
};

/**
 * Update a template
 * @param req Request
 * @param res Response
 */
export const updateTemplate = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId } = req.user;
    const { id } = req.params;
    const { name, template } = req.body;

    if (!name && !template) {
      return res.status(400).json({ message: 'At least one field (name or template) is required' });
    }

    // Check if the template exists and belongs to the tenant
    const existingTemplate = await prisma.invoiceTemplate.findFirst({
      where: {
        id,
        tenantId,
      },
    });

    if (!existingTemplate) {
      return res.status(404).json({ message: 'Template not found or does not belong to your tenant' });
    }

    // Update the template
    const updatedTemplate = await prisma.invoiceTemplate.update({
      where: { id },
      data: {
        ...(name && { name }),
        ...(template && { template }),
      },
    });

    logger.info('Template updated successfully', { templateId: id, tenantId });
    return res.status(200).json({ template: updatedTemplate });
  } catch (error: any) {
    logger.error('Error updating template', { error: error.message, stack: error.stack });
    return res.status(500).json({ message: 'Failed to update template', error: error.message });
  }
};

/**
 * Delete a template
 * @param req Request
 * @param res Response
 */
export const deleteTemplate = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId } = req.user;
    const { id } = req.params;

    // Check if the template exists and belongs to the tenant
    const existingTemplate = await prisma.invoiceTemplate.findFirst({
      where: {
        id,
        tenantId,
      },
    });

    if (!existingTemplate) {
      return res.status(404).json({ message: 'Template not found or does not belong to your tenant' });
    }

    // Delete the template
    await prisma.invoiceTemplate.delete({
      where: { id },
    });

    logger.info('Template deleted successfully', { templateId: id, tenantId });
    return res.status(200).json({ message: 'Template deleted successfully' });
  } catch (error: any) {
    logger.error('Error deleting template', { error: error.message, stack: error.stack });
    return res.status(500).json({ message: 'Failed to delete template', error: error.message });
  }
};
