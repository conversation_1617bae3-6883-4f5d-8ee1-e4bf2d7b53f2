version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:16-alpine
    container_name: invoix-postgres
    restart: unless-stopped
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: invoix
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - invoix-network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: invoix-backend
    restart: unless-stopped
    depends_on:
      - postgres
    environment:
      - NODE_ENV=development
      - PORT=5000
      - DATABASE_URL=********************************************/invoix?schema=public
      - JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
      - JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
    ports:
      - "5000:5000"
    volumes:
      - ./backend:/app
      - /app/node_modules
    networks:
      - invoix-network
    command: npm run dev

  # Frontend Next.js App
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: invoix-frontend
    restart: unless-stopped
    depends_on:
      - backend
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:5000/api
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    networks:
      - invoix-network
    command: npm run dev

networks:
  invoix-network:
    driver: bridge

volumes:
  postgres-data:
