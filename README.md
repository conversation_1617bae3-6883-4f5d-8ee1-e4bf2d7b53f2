# Invoix - Multi-Tenant ERP SaaS Platform

Invoix is a comprehensive ERP SaaS platform designed specifically for Malaysian businesses. It offers a robust set of features including invoice management, inventory control, HR management, procurement, asset management, LHDN MyInvois validation, and AI-powered business insights.

## Key Features

### Multi-Tenant System
- Secure tenant isolation with schema-based database separation
- Customizable dashboard for each tenant
- Role-based access control
- Consistent design across all modules

### Invoice Management
- Automated invoice generation with customizable templates
- Manual invoice creation and editing
- Comprehensive invoice history and reporting
- LHDN MyInvois validation for tax compliance

### Inventory Management
- Product catalog management
- Warehouse management
- Stock level tracking
- Inventory transactions and reporting

### Human Resources
- Employee management
- Attendance tracking
- Leave management
- Payroll processing

### Procurement
- Supplier management
- Purchase order processing
- Goods receipt handling
- Supplier performance tracking

### Fixed Asset Management
- Asset tracking
- Depreciation calculation
- Maintenance scheduling
- Asset reporting

### WhatsApp Integration
- Send invoices directly via WhatsApp
- Automated payment reminders
- Customer invoice retrieval via WhatsApp
- Payment links for easy collection

### AI-Powered Features
- Predictive financial insights
- Fraud detection and prevention
- Spending insights and recommendations
- Customer payment behavior analysis

### Payment Processing
- Integration with Stripe/Razorpay
- Multiple payment methods
- Automated payment tracking
- Reconciliation with invoices

## Tech Stack

### Frontend
- React with Next.js
- Tailwind CSS for styling
- TypeScript for type safety

### Backend
- Node.js with Express.js
- PostgreSQL database with Prisma ORM
- JWT for authentication
- RESTful API architecture

### Integrations
- Twilio/360dialog for WhatsApp Business API
- Stripe/Razorpay for payment processing
- LHDN MyInvois API for tax compliance

### AI/ML
- TensorFlow.js for client-side predictions
- Server-side ML models for advanced analytics

## Project Structure

```
invoix-app/
├── src/                  # Frontend Next.js application
│   ├── app/              # Next.js app directory
│   │   ├── (auth)/       # Authentication routes
│   │   ├── (dashboard)/  # Dashboard routes
│   │   ├── api/          # API routes
│   │   └── ...
│   ├── components/       # Reusable React components
│   ├── lib/              # Utility functions and hooks
│   └── ...
├── backend/              # Backend Express.js application
│   ├── src/              # Source code
│   │   ├── controllers/  # API controllers
│   │   ├── routes/       # API routes
│   │   ├── middleware/   # Middleware functions
│   │   ├── services/     # Business logic
│   │   ├── utils/        # Utility functions
│   │   └── config/       # Configuration files
│   ├── prisma/           # Prisma schema and migrations
│   └── ...
└── ...
```

## Getting Started

### Prerequisites
- Node.js (v18 or higher)
- PostgreSQL database
- Twilio account (for WhatsApp integration)
- Stripe account (for payment processing)

### Installation

1. Clone the repository
```bash
git clone https://github.com/yourusername/invoix.git
cd invoix
```

2. Install frontend dependencies
```bash
npm install
```

3. Install backend dependencies
```bash
cd backend
npm install
```

4. Set up environment variables
```bash
# In backend/.env
DATABASE_URL="postgresql://username:password@localhost:5432/invoix?schema=public"
JWT_SECRET="your-super-secret-jwt-key"
```

5. Run database migrations
```bash
cd backend
npm run prisma:migrate
```

6. Start the development servers
```bash
# In one terminal (backend)
cd backend
npm run dev

# In another terminal (frontend)
npm run dev
```

7. Open your browser and navigate to `http://localhost:3000`

## License

This project is licensed under the MIT License - see the LICENSE file for details.
