'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/Badge';
import { Spinner } from '@/components/ui/Spinner';
import Link from 'next/link';

interface Document {
  documentId: string;
  documentNumber: string;
  documentType: string;
  issueDate: string;
  status: string;
  customerName: string;
  totalAmount: number;
}

interface SearchParams {
  startDate: string;
  endDate: string;
  status: string;
  documentType: string;
  page: number;
  limit: number;
}

export default function DocumentSearchPage() {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  });
  
  const [searchParams, setSearchParams] = useState<SearchParams>({
    startDate: '',
    endDate: '',
    status: '',
    documentType: '',
    page: 1,
    limit: 10,
  });

  const searchDocuments = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // In a real implementation, this would call your API
      // const queryParams = new URLSearchParams();
      // if (searchParams.startDate) queryParams.append('startDate', searchParams.startDate);
      // if (searchParams.endDate) queryParams.append('endDate', searchParams.endDate);
      // if (searchParams.status) queryParams.append('status', searchParams.status);
      // if (searchParams.documentType) queryParams.append('documentType', searchParams.documentType);
      // queryParams.append('page', searchParams.page.toString());
      // queryParams.append('limit', searchParams.limit.toString());
      // 
      // const response = await fetch(`/api/documents/search?${queryParams.toString()}`);
      // const data = await response.json();
      // 
      // setDocuments(data.documents);
      // setPagination(data.pagination);
      
      // For development, use mock data
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockDocuments: Document[] = [
        {
          documentId: 'DOC-001',
          documentNumber: 'INV-2025-001',
          documentType: 'INVOICE',
          issueDate: '2025-01-15',
          status: 'VALID',
          customerName: 'Acme Corporation',
          totalAmount: 2500.00,
        },
        {
          documentId: 'DOC-002',
          documentNumber: 'INV-2025-002',
          documentType: 'INVOICE',
          issueDate: '2025-01-20',
          status: 'VALID',
          customerName: 'Wayne Enterprises',
          totalAmount: 4200.00,
        },
        {
          documentId: 'DOC-003',
          documentNumber: 'INV-2025-003',
          documentType: 'INVOICE',
          issueDate: '2025-01-25',
          status: 'CANCELLED',
          customerName: 'Stark Industries',
          totalAmount: 1800.00,
        },
      ];
      
      setDocuments(mockDocuments);
      setPagination({
        page: searchParams.page,
        limit: searchParams.limit,
        total: mockDocuments.length,
        totalPages: 1,
      });
    } catch (err: any) {
      console.error('Error searching documents:', err);
      setError(err.message || 'Failed to search documents');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    searchDocuments();
  }, [searchParams.page, searchParams.limit]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    searchDocuments();
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handlePageChange = (newPage: number) => {
    if (newPage < 1 || newPage > pagination.totalPages) return;
    
    setSearchParams(prev => ({
      ...prev,
      page: newPage,
    }));
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'VALID':
        return <Badge variant="success">Valid</Badge>;
      case 'CANCELLED':
        return <Badge variant="destructive">Cancelled</Badge>;
      case 'REJECTED':
        return <Badge variant="destructive">Rejected</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow-sm mb-6">
        <h1 className="text-2xl font-bold text-text-primary flex items-center">
          <svg className="w-6 h-6 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          Search LHDN Documents
        </h1>
        <p className="mt-1 text-sm text-text-secondary">
          Search for documents submitted to LHDN MyInvois
        </p>
      </div>

      <Card className="border-none shadow-md">
        <CardHeader className="pb-2 border-b">
          <CardTitle className="text-lg font-bold text-text-primary">Search Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSearch} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-text-primary mb-1">
                  Start Date
                </label>
                <input
                  type="date"
                  name="startDate"
                  value={searchParams.startDate}
                  onChange={handleInputChange}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-text-primary mb-1">
                  End Date
                </label>
                <input
                  type="date"
                  name="endDate"
                  value={searchParams.endDate}
                  onChange={handleInputChange}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-text-primary mb-1">
                  Status
                </label>
                <select
                  name="status"
                  value={searchParams.status}
                  onChange={handleInputChange}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                >
                  <option value="">All Statuses</option>
                  <option value="VALID">Valid</option>
                  <option value="CANCELLED">Cancelled</option>
                  <option value="REJECTED">Rejected</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-text-primary mb-1">
                  Document Type
                </label>
                <select
                  name="documentType"
                  value={searchParams.documentType}
                  onChange={handleInputChange}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                >
                  <option value="">All Types</option>
                  <option value="INVOICE">Invoice</option>
                  <option value="CREDIT_NOTE">Credit Note</option>
                  <option value="DEBIT_NOTE">Debit Note</option>
                </select>
              </div>
            </div>
            
            <div className="flex justify-end">
              <Button
                type="submit"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Spinner size="sm" className="mr-2" />
                    Searching...
                  </>
                ) : (
                  'Search Documents'
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      <Card className="border-none shadow-md">
        <CardHeader className="pb-2 border-b">
          <CardTitle className="text-lg font-bold text-text-primary">Search Results</CardTitle>
        </CardHeader>
        <CardContent>
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4 mb-4">
              <p>{error}</p>
            </div>
          )}
          
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <Spinner size="lg" />
              <span className="ml-2 text-text-secondary">Searching...</span>
            </div>
          ) : documents.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full text-sm text-left" className="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr className="bg-white hover:bg-gray-50 transition-colors">
                    <th className="px-3 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">Document</th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">Type</th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">Customer</th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">Issue Date</th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">Status</th>
                    <th className="px-3 py-3 text-right text-xs font-medium text-text-secondary uppercase tracking-wider">Amount</th>
                    <th className="px-3 py-3 text-right text-xs font-medium text-text-secondary uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {documents.map((doc) => (
                    <tr key={doc.documentId} className="bg-white  transition-colors">
                      <td className="px-3 py-4 font-medium text-text-primary">{doc.documentNumber}</td>
                      <td className="px-3 py-4 text-text-secondary">{doc.documentType}</td>
                      <td className="px-3 py-4 text-text-primary">{doc.customerName}</td>
                      <td className="px-3 py-4 text-text-secondary">{doc.issueDate}</td>
                      <td className="px-3 py-4">{getStatusBadge(doc.status)}</td>
                      <td className="px-3 py-4 text-right font-medium">{doc.totalAmount.toFixed(2)}</td>
                      <td className="px-3 py-4 text-right">
                        <Link href={`/dashboard/invoices/${doc.documentId}`} className="text-indigo-600 hover:text-indigo-900 font-medium">
                          View
                        </Link>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              
              {pagination.totalPages > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <div className="text-sm text-text-secondary">
                    Showing {(pagination.page - 1) * pagination.limit + 1} to {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} results
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(pagination.page - 1)}
                      disabled={pagination.page === 1}
                    >
                      Previous
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(pagination.page + 1)}
                      disabled={pagination.page === pagination.totalPages}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-8">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-text-primary">No documents found</h3>
              <p className="mt-1 text-sm text-text-secondary">Try adjusting your search filters.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
