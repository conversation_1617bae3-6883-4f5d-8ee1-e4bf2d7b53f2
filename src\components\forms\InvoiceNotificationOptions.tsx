'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';

interface NotificationOptions {
  autoSend: boolean;
  channels: {
    email: boolean;
    whatsapp: boolean;
  };
  reminders: {
    enabled: boolean;
    beforeDue: number;
    afterDue: number;
  };
  escalation: {
    enabled: boolean;
    days: number;
  };
}

interface InvoiceNotificationOptionsProps {
  options: NotificationOptions;
  onChange: (options: NotificationOptions) => void;
  customerHasEmail: boolean;
  customerHasPhone: boolean;
}

export default function InvoiceNotificationOptions({
  options,
  onChange,
  customerHasEmail,
  customerHasPhone
}: InvoiceNotificationOptionsProps) {
  const [localOptions, setLocalOptions] = useState<NotificationOptions>(options);

  const handleChange = (updatedOptions: Partial<NotificationOptions>) => {
    const newOptions = {
      ...localOptions,
      ...updatedOptions
    };
    setLocalOptions(newOptions);
    onChange(newOptions);
  };

  const handleChannelChange = (channel: 'email' | 'whatsapp', value: boolean) => {
    const newChannels = {
      ...localOptions.channels,
      [channel]: value
    };
    handleChange({ channels: newChannels });
  };

  const handleReminderChange = (field: keyof NotificationOptions['reminders'], value: any) => {
    const newReminders = {
      ...localOptions.reminders,
      [field]: value
    };
    handleChange({ reminders: newReminders });
  };

  const handleEscalationChange = (field: keyof NotificationOptions['escalation'], value: any) => {
    const newEscalation = {
      ...localOptions.escalation,
      [field]: value
    };
    handleChange({ escalation: newEscalation });
  };

  return (
    <Card className="border-none shadow-md">
      <CardHeader className="pb-2 border-b">
        <CardTitle className="text-lg font-bold">Notification Options</CardTitle>
        <CardDescription>Configure how this invoice will be sent to the customer</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6 pt-4">
        {/* Auto-send toggle */}
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="auto-send">Automatically send invoice</Label>
            <p className="text-sm text-muted-foreground">
              Send this invoice to the customer as soon as it's finalized
            </p>
          </div>
          <Switch
            id="auto-send"
            checked={localOptions.autoSend}
            onCheckedChange={(checked) => handleChange({ autoSend: checked })}
          />
        </div>

        <Separator />

        {/* Notification channels */}
        <div className="space-y-3">
          <h3 className="text-sm font-medium">Notification Channels</h3>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Label htmlFor="email-channel" className="flex items-center space-x-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-mail">
                  <rect width="20" height="16" x="2" y="4" rx="2" />
                  <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7" />
                </svg>
                <span>Email</span>
              </Label>
              {!customerHasEmail && (
                <span className="text-xs text-red-500 ml-2">Customer has no email</span>
              )}
            </div>
            <Switch
              id="email-channel"
              checked={localOptions.channels.email}
              onCheckedChange={(checked) => handleChannelChange('email', checked)}
              disabled={!customerHasEmail}
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Label htmlFor="whatsapp-channel" className="flex items-center space-x-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-message-circle">
                  <path d="M7.9 20A9 9 0 1 0 4 16.1L2 22Z" />
                </svg>
                <span>WhatsApp</span>
              </Label>
              {!customerHasPhone && (
                <span className="text-xs text-red-500 ml-2">Customer has no phone</span>
              )}
            </div>
            <Switch
              id="whatsapp-channel"
              checked={localOptions.channels.whatsapp}
              onCheckedChange={(checked) => handleChannelChange('whatsapp', checked)}
              disabled={!customerHasPhone}
            />
          </div>
        </div>

        <Separator />

        {/* Payment reminders */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="reminders-enabled">Payment Reminders</Label>
              <p className="text-sm text-muted-foreground">
                Send reminders before and after the due date
              </p>
            </div>
            <Switch
              id="reminders-enabled"
              checked={localOptions.reminders.enabled}
              onCheckedChange={(checked) => handleReminderChange('enabled', checked)}
            />
          </div>

          {localOptions.reminders.enabled && (
            <div className="grid grid-cols-2 gap-4 mt-2">
              <div className="space-y-2">
                <Label htmlFor="before-due" className="text-sm">Days before due date</Label>
                <Input
                  id="before-due"
                  type="number"
                  min="0"
                  max="30"
                  value={localOptions.reminders.beforeDue}
                  onChange={(e) => handleReminderChange('beforeDue', parseInt(e.target.value) || 0)}
                  className="h-8"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="after-due" className="text-sm">Days after due date</Label>
                <Input
                  id="after-due"
                  type="number"
                  min="0"
                  max="30"
                  value={localOptions.reminders.afterDue}
                  onChange={(e) => handleReminderChange('afterDue', parseInt(e.target.value) || 0)}
                  className="h-8"
                />
              </div>
            </div>
          )}
        </div>

        <Separator />

        {/* Escalation options */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="escalation-enabled">Escalation</Label>
              <p className="text-sm text-muted-foreground">
                Escalate to alternative channels if payment is overdue
              </p>
            </div>
            <Switch
              id="escalation-enabled"
              checked={localOptions.escalation.enabled}
              onCheckedChange={(checked) => handleEscalationChange('enabled', checked)}
            />
          </div>

          {localOptions.escalation.enabled && (
            <div className="space-y-2 mt-2">
              <Label htmlFor="escalation-days" className="text-sm">Days after due date to escalate</Label>
              <Input
                id="escalation-days"
                type="number"
                min="1"
                max="30"
                value={localOptions.escalation.days}
                onChange={(e) => handleEscalationChange('days', parseInt(e.target.value) || 1)}
                className="h-8"
              />
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
