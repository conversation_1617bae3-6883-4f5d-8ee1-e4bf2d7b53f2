// This is the complete Prisma schema file for the Invoix ERP platform
// It includes all models for all modules in a single file

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Multi-tenant models
model Tenant {
  id                String              @id @default(uuid())
  name              String
  businessName      String
  businessRegNo     String?             // Business registration number for LHDN
  businessRegValid  Boolean             @default(false) // Whether the business registration is validated
  email             String              @unique
  phone             String?
  address           String?
  logo              String?
  isActive          Boolean             @default(true)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  // Core modules
  users             User[]
  customers         Customer[]
  invoices          Invoice[]
  invoiceItems      InvoiceItem[]
  payments          Payment[]
  invoiceTemplates  InvoiceTemplate[]
  paymentSettings   PaymentSetting[]
  whatsappSettings  WhatsappSetting?

  // Inventory Management
  products          Product[]
  warehouses        Warehouse[]
  inventoryItems    InventoryItem[]
  inventoryTransactions InventoryTransaction[]

  // HR Management
  employees         Employee[]
  salaryRecords     SalaryRecord[]
  attendanceRecords AttendanceRecord[]
  leaveRequests     LeaveRequest[]

  // Procurement
  suppliers         Supplier[]
  purchaseOrders    PurchaseOrder[]
  purchaseOrderItems PurchaseOrderItem[]
  goodsReceipts     GoodsReceipt[]
  goodsReceiptItems GoodsReceiptItem[]

  // Fixed Assets
  fixedAssets       FixedAsset[]
  assetDepreciations AssetDepreciation[]
  assetMaintenances AssetMaintenance[]

  // Project Management
  projects          Project[]
  projectTasks      ProjectTask[]
  timeEntries       TimeEntry[]
  projectExpenses   ProjectExpense[]

  @@index([businessRegNo])
}

model User {
  id                String              @id @default(uuid())
  email             String              @unique
  password          String
  name              String
  role              UserRole            @default(USER)
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  refreshToken      String?             // For JWT refresh token
  lastLogin         DateTime?
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([tenantId])
}

enum UserRole {
  ADMIN
  MANAGER
  USER
}

// Core modules
model Customer {
  id                String              @id @default(uuid())
  name              String
  email             String
  phone             String?
  address           String?
  taxId             String?             // Tax Identification Number
  taxIdValidated    Boolean             @default(false)
  creditLimit       Float?
  notes             String?
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  invoices          Invoice[]
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@unique([email, tenantId])
  @@index([tenantId])
  @@index([taxId])
}

model Invoice {
  id                String              @id @default(uuid())
  invoiceNumber     String
  issueDate         DateTime            @default(now())
  dueDate           DateTime
  status            InvoiceStatus       @default(DRAFT)
  totalAmount       Float
  tax               Float               @default(0)
  notes             String?
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  customerId        String
  customer          Customer            @relation(fields: [customerId], references: [id])
  items             InvoiceItem[]
  payments          Payment[]
  validationId      String?             // LHDN validation ID
  validationStatus  ValidationStatus?
  validationDate    DateTime?
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@unique([invoiceNumber, tenantId])
  @@index([tenantId])
  @@index([customerId])
  @@index([status])
}

enum InvoiceStatus {
  DRAFT
  SENT
  PAID
  OVERDUE
  CANCELLED
  REJECTED
}

enum ValidationStatus {
  PENDING
  VALID
  INVALID
  FAILED
}

model InvoiceItem {
  id                String              @id @default(uuid())
  description       String
  quantity          Float
  unitPrice         Float
  amount            Float
  invoiceId         String
  invoice           Invoice             @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  productId         String?
  product           Product?            @relation(fields: [productId], references: [id])
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([invoiceId])
  @@index([tenantId])
  @@index([productId])
}

model Payment {
  id                String              @id @default(uuid())
  amount            Float
  paymentDate       DateTime
  paymentMethod     PaymentMethod
  referenceNumber   String?
  notes             String?
  invoiceId         String
  invoice           Invoice             @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([invoiceId])
  @@index([tenantId])
}

enum PaymentMethod {
  BANK_TRANSFER
  CREDIT_CARD
  CASH
  CHEQUE
  ONLINE
}

model InvoiceTemplate {
  id                String              @id @default(uuid())
  name              String
  template          String              @db.Text
  isDefault         Boolean             @default(false)
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([tenantId])
}

model PaymentSetting {
  id                String              @id @default(uuid())
  provider          String              // Stripe, Razorpay, etc.
  apiKey            String              @db.Text
  apiSecret         String              @db.Text
  isActive          Boolean             @default(true)
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([tenantId])
}

model WhatsappSetting {
  id                String              @id @default(uuid())
  provider          String              // Twilio, 360dialog, etc.
  apiKey            String              @db.Text
  phoneNumber       String
  isActive          Boolean             @default(true)
  tenantId          String              @unique
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
}

// LHDN MyInvois platform-level configuration
model LHDNConfig {
  id                String              @id @default(uuid())
  apiBaseUrl        String              @default("https://sandbox.myinvois.hasil.gov.my/einvoicing")
  certificatePath   String?
  certificatePassword String?           @db.Text
  isActive          Boolean             @default(false)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
}

model LHDNCertificate {
  id                String              @id @default(uuid())
  name              String
  filename          String
  encryptedData     String              @db.Text
  isActive          Boolean             @default(false)
  expiryDate        DateTime
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
}

// Inventory Management Module
model Product {
  id                String              @id @default(uuid())
  sku               String
  name              String
  description       String?
  category          String?
  unitPrice         Float
  costPrice         Float
  taxRate           Float               @default(0)
  minStockLevel     Int?                // Minimum stock level for alerts
  isActive          Boolean             @default(true)
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  inventoryItems    InventoryItem[]
  invoiceItems      InvoiceItem[]
  purchaseItems     PurchaseOrderItem[]
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@unique([sku, tenantId])
  @@index([tenantId])
  @@index([category])
}

model Warehouse {
  id                String              @id @default(uuid())
  name              String
  location          String?
  description       String?
  isActive          Boolean             @default(true)
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  inventoryItems    InventoryItem[]
  inventoryTransactions InventoryTransaction[]
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@unique([name, tenantId])
  @@index([tenantId])
}

model InventoryItem {
  id                String              @id @default(uuid())
  productId         String
  product           Product             @relation(fields: [productId], references: [id], onDelete: Cascade)
  warehouseId       String
  warehouse         Warehouse           @relation(fields: [warehouseId], references: [id], onDelete: Cascade)
  quantity          Float
  location          String?             // Bin/shelf location within warehouse
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  transactions      InventoryTransaction[]
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@unique([productId, warehouseId, tenantId])
  @@index([tenantId])
  @@index([productId])
  @@index([warehouseId])
}

model InventoryTransaction {
  id                String              @id @default(uuid())
  type              TransactionType
  productId         String
  warehouseId       String
  warehouse         Warehouse           @relation(fields: [warehouseId], references: [id])
  inventoryItemId   String
  inventoryItem     InventoryItem       @relation(fields: [inventoryItemId], references: [id])
  quantity          Float
  referenceType     String?             // Invoice, PO, etc.
  referenceId       String?             // ID of the reference document
  notes             String?
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([tenantId])
  @@index([inventoryItemId])
  @@index([warehouseId])
  @@index([type])
  @@index([referenceId])
}

enum TransactionType {
  PURCHASE
  SALE
  ADJUSTMENT
  TRANSFER_IN
  TRANSFER_OUT
  RETURN
}

// Human Resources Module
model Employee {
  id                String              @id @default(uuid())
  employeeId        String              // Employee ID/Number
  name              String
  email             String
  phone             String?
  address           String?
  position          String?
  department        String?
  joinDate          DateTime
  terminationDate   DateTime?
  isActive          Boolean             @default(true)
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  salaryRecords     SalaryRecord[]
  attendanceRecords AttendanceRecord[]
  leaveRequests     LeaveRequest[]
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@unique([email, tenantId])
  @@unique([employeeId, tenantId])
  @@index([tenantId])
  @@index([department])
}

model SalaryRecord {
  id                String              @id @default(uuid())
  employeeId        String
  employee          Employee            @relation(fields: [employeeId], references: [id])
  month             Int
  year              Int
  basicSalary       Float
  allowances        Float               @default(0)
  deductions        Float               @default(0)
  tax               Float               @default(0)
  epf               Float               @default(0)      // Malaysian Employees Provident Fund
  socso             Float               @default(0)      // Malaysian Social Security
  netSalary         Float
  paymentDate       DateTime?
  paymentStatus     PaymentStatus       @default(PENDING)
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@unique([employeeId, month, year, tenantId])
  @@index([tenantId])
  @@index([employeeId])
}

enum PaymentStatus {
  PENDING
  PAID
  CANCELLED
}

model AttendanceRecord {
  id                String              @id @default(uuid())
  employeeId        String
  employee          Employee            @relation(fields: [employeeId], references: [id])
  date              DateTime
  checkIn           DateTime?
  checkOut          DateTime?
  status            AttendanceStatus    @default(PRESENT)
  notes             String?
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@unique([employeeId, date, tenantId])
  @@index([tenantId])
  @@index([employeeId])
}

enum AttendanceStatus {
  PRESENT
  ABSENT
  LATE
  HALF_DAY
  ON_LEAVE
}

model LeaveRequest {
  id                String              @id @default(uuid())
  employeeId        String
  employee          Employee            @relation(fields: [employeeId], references: [id])
  leaveType         LeaveType
  startDate         DateTime
  endDate           DateTime
  totalDays         Float
  reason            String?
  status            ApprovalStatus      @default(PENDING)
  approvedBy        String?             // User ID who approved/rejected
  approvalDate      DateTime?
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([tenantId])
  @@index([employeeId])
}

enum LeaveType {
  ANNUAL
  SICK
  MATERNITY
  PATERNITY
  UNPAID
  OTHER
}

enum ApprovalStatus {
  PENDING
  APPROVED
  REJECTED
}

// Supplier and Procurement Module
model Supplier {
  id                String              @id @default(uuid())
  name              String
  contactPerson     String?
  email             String
  phone             String?
  address           String?
  taxId             String?             // For Malaysian GST/SST
  paymentTerms      Int                 @default(30)     // Days
  isActive          Boolean             @default(true)
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  purchaseOrders    PurchaseOrder[]
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@unique([email, tenantId])
  @@index([tenantId])
  @@index([taxId])
}

model PurchaseOrder {
  id                String              @id @default(uuid())
  poNumber          String
  orderDate         DateTime
  expectedDelivery  DateTime?
  status            PurchaseOrderStatus @default(DRAFT)
  totalAmount       Float
  notes             String?
  supplierId        String
  supplier          Supplier            @relation(fields: [supplierId], references: [id])
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  items             PurchaseOrderItem[]
  goodsReceipts     GoodsReceipt[]
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@unique([poNumber, tenantId])
  @@index([tenantId])
  @@index([supplierId])
  @@index([status])
}

enum PurchaseOrderStatus {
  DRAFT
  SENT
  CONFIRMED
  RECEIVED
  CANCELLED
  CLOSED
}

model PurchaseOrderItem {
  id                String              @id @default(uuid())
  purchaseOrderId   String
  purchaseOrder     PurchaseOrder       @relation(fields: [purchaseOrderId], references: [id], onDelete: Cascade)
  productId         String
  product           Product             @relation(fields: [productId], references: [id])
  description       String
  quantity          Float
  unitPrice         Float
  amount            Float
  receivedQuantity  Float               @default(0)
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  goodsReceiptItems GoodsReceiptItem[]
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([purchaseOrderId])
  @@index([productId])
  @@index([tenantId])
}

model GoodsReceipt {
  id                String              @id @default(uuid())
  receiptNumber     String
  receiptDate       DateTime
  purchaseOrderId   String
  purchaseOrder     PurchaseOrder       @relation(fields: [purchaseOrderId], references: [id])
  notes             String?
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  items             GoodsReceiptItem[]
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@unique([receiptNumber, tenantId])
  @@index([tenantId])
  @@index([purchaseOrderId])
}

model GoodsReceiptItem {
  id                String              @id @default(uuid())
  goodsReceiptId    String
  goodsReceipt      GoodsReceipt        @relation(fields: [goodsReceiptId], references: [id], onDelete: Cascade)
  purchaseOrderItemId String
  purchaseOrderItem PurchaseOrderItem   @relation(fields: [purchaseOrderItemId], references: [id])
  quantity          Float
  notes             String?
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([goodsReceiptId])
  @@index([purchaseOrderItemId])
  @@index([tenantId])
}

// Fixed Asset Management Module
model FixedAsset {
  id                String              @id @default(uuid())
  assetNumber       String
  name              String
  description       String?
  category          AssetCategory
  purchaseDate      DateTime
  purchasePrice     Float
  currentValue      Float
  location          String?
  assignedTo        String?             // Employee ID if assigned
  status            AssetStatus         @default(ACTIVE)
  disposalDate      DateTime?
  disposalValue     Float?
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  depreciations     AssetDepreciation[]
  maintenances      AssetMaintenance[]
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@unique([assetNumber, tenantId])
  @@index([tenantId])
  @@index([category])
  @@index([status])
}

enum AssetCategory {
  LAND
  BUILDING
  EQUIPMENT
  VEHICLE
  FURNITURE
  COMPUTER
  SOFTWARE
  OTHER
}

enum AssetStatus {
  ACTIVE
  MAINTENANCE
  DISPOSED
  INACTIVE
}

model AssetDepreciation {
  id                String              @id @default(uuid())
  assetId           String
  asset             FixedAsset          @relation(fields: [assetId], references: [id], onDelete: Cascade)
  depreciationDate  DateTime
  depreciationAmount Float
  bookValue         Float               // Value after depreciation
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([assetId])
  @@index([tenantId])
}

model AssetMaintenance {
  id                String              @id @default(uuid())
  assetId           String
  asset             FixedAsset          @relation(fields: [assetId], references: [id], onDelete: Cascade)
  maintenanceDate   DateTime
  description       String
  cost              Float
  provider          String?
  nextMaintenanceDate DateTime?
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([assetId])
  @@index([tenantId])
}

// Project Management Module
model Project {
  id                String              @id @default(uuid())
  name              String
  description       String?
  startDate         DateTime
  endDate           DateTime?
  status            ProjectStatus       @default(PLANNING)
  budget            Float?
  actualCost        Float?              @default(0)
  completionPercentage Float            @default(0)
  clientId          String?             // Optional link to Customer
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  tasks             ProjectTask[]
  timeEntries       TimeEntry[]
  expenses          ProjectExpense[]
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([tenantId])
  @@index([status])
  @@index([clientId])
}

enum ProjectStatus {
  PLANNING
  ACTIVE
  ON_HOLD
  COMPLETED
  CANCELLED
}

model ProjectTask {
  id                String              @id @default(uuid())
  projectId         String
  project           Project             @relation(fields: [projectId], references: [id], onDelete: Cascade)
  name              String
  description       String?
  startDate         DateTime?
  dueDate           DateTime?
  completionDate    DateTime?
  status            TaskStatus          @default(TODO)
  priority          Priority            @default(MEDIUM)
  assignedTo        String?             // Employee ID if assigned
  estimatedHours    Float?
  actualHours       Float               @default(0)
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  timeEntries       TimeEntry[]
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([projectId])
  @@index([tenantId])
  @@index([status])
  @@index([assignedTo])
}

enum TaskStatus {
  TODO
  IN_PROGRESS
  REVIEW
  DONE
  CANCELLED
}

enum Priority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

model TimeEntry {
  id                String              @id @default(uuid())
  projectId         String
  project           Project             @relation(fields: [projectId], references: [id])
  taskId            String?
  task              ProjectTask?        @relation(fields: [taskId], references: [id])
  employeeId        String              // Employee who logged time
  description       String?
  startTime         DateTime
  endTime           DateTime?
  duration          Float               // Hours
  billable          Boolean             @default(true)
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([projectId])
  @@index([taskId])
  @@index([tenantId])
  @@index([employeeId])
}

model ProjectExpense {
  id                String              @id @default(uuid())
  projectId         String
  project           Project             @relation(fields: [projectId], references: [id], onDelete: Cascade)
  description       String
  amount            Float
  date              DateTime
  category          String?
  receiptUrl        String?
  reimbursable      Boolean             @default(false)
  status            ExpenseStatus       @default(PENDING)
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([projectId])
  @@index([tenantId])
}

enum ExpenseStatus {
  PENDING
  APPROVED
  REJECTED
  REIMBURSED
}