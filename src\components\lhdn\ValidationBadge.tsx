'use client';

import { cn } from '@/lib/utils';
import { useState } from 'react';

interface ValidationBadgeProps {
  invoiceId: string;
  isValidated?: boolean;
  validationId?: string;
  className?: string;
  showDetails?: boolean;
}

export default function ValidationBadge({
  invoiceId,
  isValidated = false,
  validationId,
  className,
  showDetails = false,
}: ValidationBadgeProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [validationStatus, setValidationStatus] = useState({
    isValidated,
    validationId,
  });

  const checkStatus = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/lhdn/status/${invoiceId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setValidationStatus({
          isValidated: data.isValidated,
          validationId: data.validationId,
        });
      }
    } catch (error) {
      console.error('Error checking validation status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const validateInvoice = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/lhdn/validate/${invoiceId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setValidationStatus({
          isValidated: true,
          validationId: data.validationId,
        });
      } else {
        const errorData = await response.json();
        alert(`Validation failed: ${errorData.message}`);
      }
    } catch (error) {
      console.error('Error validating invoice:', error);
      alert('An error occurred while validating the invoice');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={cn("relative", className)}>
      <div
        className={cn(
          "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",
          validationStatus.isValidated
            ? "bg-green-100 text-green-800"
            : "bg-yellow-100 text-yellow-800",
          isLoading && "opacity-50"
        )}
        onClick={() => showDetails && setIsOpen(!isOpen)}
      >
        {isLoading ? (
          <svg className="w-4 h-4 mr-1 animate-spin" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
        ) : (
          <span className="mr-1">
            {validationStatus.isValidated ? (
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            ) : (
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            )}
          </span>
        )}
        <span>
          {validationStatus.isValidated
            ? 'LHDN Validated'
            : 'Not Validated'}
        </span>
      </div>

      {showDetails && isOpen && (
        <div className="absolute z-10 mt-2 w-64 bg-white rounded-md shadow-lg p-4 border border-gray-200">
          <h3 className="text-sm font-medium text-gray-900">LHDN Validation Status</h3>
          <div className="mt-2 text-xs text-gray-600">
            <p>
              <span className="font-semibold">Status:</span>{' '}
              {validationStatus.isValidated ? 'Validated' : 'Not Validated'}
            </p>
            {validationStatus.validationId && (
              <p className="mt-1">
                <span className="font-semibold">Validation ID:</span>{' '}
                {validationStatus.validationId}
              </p>
            )}
          </div>
          <div className="mt-3 flex space-x-2">
            <button
              onClick={(e) => {
                e.stopPropagation();
                checkStatus();
              }}
              className="text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded"
              disabled={isLoading}
            >
              Refresh Status
            </button>
            {!validationStatus.isValidated && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  validateInvoice();
                }}
                className="text-xs px-2 py-1 bg-indigo-100 hover:bg-indigo-200 text-indigo-700 rounded"
                disabled={isLoading}
              >
                Validate Now
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
