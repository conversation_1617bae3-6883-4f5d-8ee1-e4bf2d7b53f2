const request = require('supertest');
const app = require('../src/app');
const { prisma } = require('../src/index');
const { hashPassword } = require('../src/utils/auth');

// Mock data
const testUser = {
  email: '<EMAIL>',
  password: 'Password123!',
  name: 'Test User',
};

const testTenant = {
  name: 'Test Tenant',
  businessName: 'Test Business',
  email: '<EMAIL>',
};

// Setup and teardown
beforeAll(async () => {
  // Create test tenant
  const tenant = await prisma.tenant.create({
    data: testTenant,
  });

  // Create test user
  const hashedPassword = await hashPassword(testUser.password);
  await prisma.user.create({
    data: {
      email: testUser.email,
      password: hashedPassword,
      name: testUser.name,
      role: 'USER',
      tenantId: tenant.id,
    },
  });
});

afterAll(async () => {
  // Clean up test data
  const user = await prisma.user.findUnique({
    where: { email: testUser.email },
  });
  
  if (user) {
    await prisma.user.delete({
      where: { id: user.id },
    });
  }
  
  const tenant = await prisma.tenant.findUnique({
    where: { email: testTenant.email },
  });
  
  if (tenant) {
    await prisma.tenant.delete({
      where: { id: tenant.id },
    });
  }
  
  await prisma.$disconnect();
});

// Tests
describe('Authentication API', () => {
  let accessToken;
  let refreshToken;

  describe('Login', () => {
    it('should login successfully with valid credentials', async () => {
      const res = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password,
        });

      expect(res.statusCode).toEqual(200);
      expect(res.body).toHaveProperty('accessToken');
      expect(res.body).toHaveProperty('refreshToken');
      expect(res.body).toHaveProperty('user');
      expect(res.body.user.email).toEqual(testUser.email);

      // Save tokens for later tests
      accessToken = res.body.accessToken;
      refreshToken = res.body.refreshToken;
    });

    it('should fail with invalid credentials', async () => {
      const res = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: 'wrongpassword',
        });

      expect(res.statusCode).toEqual(401);
      expect(res.body).toHaveProperty('message');
    });
  });

  describe('Protected Routes', () => {
    it('should access profile with valid token', async () => {
      const res = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${accessToken}`);

      expect(res.statusCode).toEqual(200);
      expect(res.body).toHaveProperty('email');
      expect(res.body.email).toEqual(testUser.email);
    });

    it('should fail without token', async () => {
      const res = await request(app).get('/api/auth/profile');

      expect(res.statusCode).toEqual(401);
    });
  });

  describe('Refresh Token', () => {
    it('should get new access token with valid refresh token', async () => {
      const res = await request(app)
        .post('/api/auth/refresh-token')
        .send({ refreshToken });

      expect(res.statusCode).toEqual(200);
      expect(res.body).toHaveProperty('accessToken');
      
      // Update access token for later tests
      accessToken = res.body.accessToken;
    });

    it('should fail with invalid refresh token', async () => {
      const res = await request(app)
        .post('/api/auth/refresh-token')
        .send({ refreshToken: 'invalid-token' });

      expect(res.statusCode).toEqual(401);
    });
  });

  describe('Logout', () => {
    it('should logout successfully', async () => {
      const res = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${accessToken}`);

      expect(res.statusCode).toEqual(200);
      expect(res.body).toHaveProperty('message');
    });

    it('should not be able to use refresh token after logout', async () => {
      const res = await request(app)
        .post('/api/auth/refresh-token')
        .send({ refreshToken });

      expect(res.statusCode).toEqual(401);
    });
  });
});
