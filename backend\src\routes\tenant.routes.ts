import { Router, Request, Response } from 'express';
import { createTenant, getTenantById, updateTenant, deleteTenant } from '../controllers/tenant.controller';
import { uploadTenantLogo, deleteTenantLogo } from '../controllers/tenant.logo.controller';
import { authenticate, authorize, UserRole } from '../middleware/auth.middleware';
import { ExpressHandler } from '../types/express';
import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';
import path from 'path';
import fs from 'fs';

const prisma = new PrismaClient();

const router = Router();

// Public route for creating a new tenant
router.post('/', createTenant as ExpressHandler);

// Protected routes
router.get('/current', authenticate as ExpressHandler, (async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId } = req.user;

    // Get the current tenant
    try {
      const tenant = await prisma.tenant.findUnique({
        where: { id: tenantId }
      });

      if (!tenant) {
        return res.status(404).json({ message: 'Tenant not found' });
      }

      // Return the tenant data (excluding sensitive information)
      return res.status(200).json({
        tenant: {
          id: tenant.id,
          name: tenant.name,
          businessName: tenant.businessName,
          // businessRegNo and businessRegValid removed from schema
          // businessRegNo: tenant.businessRegNo,
          // businessRegValid: tenant.businessRegValid,
          email: tenant.email,
          phone: tenant.phone,
          address: tenant.address,
          logo: tenant.logo,
          createdAt: tenant.createdAt,
          updatedAt: tenant.updatedAt
        }
      });
    } catch (error: any) {
      logger.error('Error fetching current tenant', { error });
      return res.status(500).json({ message: 'Failed to fetch tenant data', error: error.message });
    }
  } catch (error: any) {
    logger.error('Error in getCurrentTenant', { error: error.message, stack: error.stack });
    return res.status(500).json({ message: 'Failed to fetch tenant data', error: error.message });
  }
}) as unknown as ExpressHandler);

router.get('/:id', authenticate as ExpressHandler, getTenantById as ExpressHandler);
router.put('/:id', authenticate as ExpressHandler, (authorize([UserRole.ADMIN]) as ExpressHandler), updateTenant as ExpressHandler);
router.delete('/:id', authenticate as ExpressHandler, (authorize([UserRole.ADMIN]) as ExpressHandler), deleteTenant as ExpressHandler);

// Logo upload routes
router.post('/logo', authenticate as ExpressHandler, uploadTenantLogo as ExpressHandler);
router.delete('/logo', authenticate as ExpressHandler, deleteTenantLogo as ExpressHandler);

// Serve logo images
router.get('/logo/:filename', authenticate as ExpressHandler, (async (req: Request, res: Response) => {
  try {
    const { filename } = req.params;
    const logoPath = path.join(process.cwd(), 'uploads', 'logos', filename);

    // Check if the file exists
    if (!fs.existsSync(logoPath)) {
      return res.status(404).json({ message: 'Logo not found' });
    }

    // Determine the content type based on the file extension
    const ext = path.extname(filename).toLowerCase();
    let contentType = 'image/jpeg'; // Default

    if (ext === '.png') contentType = 'image/png';
    else if (ext === '.gif') contentType = 'image/gif';
    else if (ext === '.svg') contentType = 'image/svg+xml';

    // Set the content type header
    res.setHeader('Content-Type', contentType);

    // Stream the file to the response
    const fileStream = fs.createReadStream(logoPath);
    fileStream.pipe(res);
  } catch (error) {
    console.error('Error serving logo image:', error);
    res.status(500).json({ message: 'Failed to serve logo image' });
  }
}) as unknown as ExpressHandler);

export default router;
