#!/usr/bin/env node

/**
 * Script to generate secure JWT secrets for access and refresh tokens
 * Run with: node scripts/generate-jwt-secrets.js
 */

const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

// Generate random secrets
const accessTokenSecret = crypto.randomBytes(64).toString('hex');
const refreshTokenSecret = crypto.randomBytes(64).toString('hex');

// Create or update .env file
const envPath = path.join(__dirname, '..', '.env');
let envContent = '';

// Read existing .env file if it exists
if (fs.existsSync(envPath)) {
  envContent = fs.readFileSync(envPath, 'utf8');
}

// Check if secrets already exist in .env
const hasAccessSecret = envContent.includes('ACCESS_TOKEN_SECRET=');
const hasRefreshSecret = envContent.includes('REFRESH_TOKEN_SECRET=');

// Update or append secrets
if (hasAccessSecret) {
  envContent = envContent.replace(
    /ACCESS_TOKEN_SECRET=.*/,
    `ACCESS_TOKEN_SECRET=${accessTokenSecret}`
  );
} else {
  envContent += `\nACCESS_TOKEN_SECRET=${accessTokenSecret}`;
}

if (hasRefreshSecret) {
  envContent = envContent.replace(
    /REFRESH_TOKEN_SECRET=.*/,
    `REFRESH_TOKEN_SECRET=${refreshTokenSecret}`
  );
} else {
  envContent += `\nREFRESH_TOKEN_SECRET=${refreshTokenSecret}`;
}

// Write updated content to .env file
fs.writeFileSync(envPath, envContent.trim());

console.log('JWT secrets generated and saved to .env file:');
console.log(`ACCESS_TOKEN_SECRET=${accessTokenSecret.substring(0, 10)}...`);
console.log(`REFRESH_TOKEN_SECRET=${refreshTokenSecret.substring(0, 10)}...`);
