import { <PERSON><PERSON><PERSON><PERSON> } from 'cron';
import { prisma } from '../index';
import { logger } from '../utils/logger';
import { notificationService } from '../services/notification.service';
import { emailService } from '../services/email.service';
import { whatsappService } from '../services/whatsapp.service';
import { addDays, subDays, isWithinInterval, parseISO, format } from 'date-fns';

/**
 * Notification Jobs
 * Scheduled jobs for processing notifications
 */
export class NotificationJobs {
  private processQueueJob: CronJob;
  private invoiceReminderJob: CronJob;
  private overdueInvoiceJob: CronJob;

  constructor() {
    // Process notification queue every 5 minutes
    this.processQueueJob = new CronJob('*/5 * * * *', this.processNotificationQueue.bind(this));

    // Check for invoice reminders every day at 8:00 AM
    this.invoiceReminderJob = new CronJob('0 8 * * *', this.processInvoiceReminders.bind(this));

    // Check for overdue invoices every day at 9:00 AM
    this.overdueInvoiceJob = new CronJob('0 9 * * *', this.processOverdueInvoices.bind(this));
  }

  /**
   * Start all jobs
   */
  startJobs() {
    this.processQueueJob.start();
    this.invoiceReminderJob.start();
    this.overdueInvoiceJob.start();
    logger.info('Notification jobs started');
  }

  /**
   * Stop all jobs
   */
  stopJobs() {
    this.processQueueJob.stop();
    this.invoiceReminderJob.stop();
    this.overdueInvoiceJob.stop();
    logger.info('Notification jobs stopped');
  }

  /**
   * Process notification queue
   * @private
   */
  private async processNotificationQueue() {
    try {
      logger.info('Processing notification queue');

      // Get pending notifications that are due
      const pendingNotifications = await prisma.notificationQueue.findMany({
        where: {
          status: 'PENDING',
          scheduledFor: {
            lte: new Date(),
          },
        },
        take: 50, // Process in batches
      });

      logger.info(`Found ${pendingNotifications.length} pending notifications`);

      // Process each notification
      for (const notification of pendingNotifications) {
        try {
          // Update status to processing
          await prisma.notificationQueue.update({
            where: { id: notification.id },
            data: {
              status: 'PROCESSING',
              attempts: { increment: 1 },
              lastAttempt: new Date(),
            },
          });

          // Parse notification data
          const data = JSON.parse(notification.data);

          // Check if notification should be sent during business hours
          if (data.restrictToBusinessHours) {
            const shouldSend = await this.isWithinBusinessHours(notification.tenantId);
            if (!shouldSend) {
              // Reschedule for next business hour
              const nextBusinessHour = await this.getNextBusinessHour(notification.tenantId);
              await prisma.notificationQueue.update({
                where: { id: notification.id },
                data: {
                  status: 'PENDING',
                  scheduledFor: nextBusinessHour,
                },
              });
              logger.info(`Notification ${notification.id} rescheduled for business hours`);
              continue;
            }
          }

          // Send notification
          const result = await notificationService.sendNotification({
            type: notification.type,
            channel: notification.channel,
            recipient: data.recipient,
            recipientName: data.recipientName,
            subject: data.subject,
            content: data.content,
            templateName: data.templateName,
            templateData: data.templateData,
            tenantId: notification.tenantId,
            relatedId: notification.relatedId,
            relatedType: notification.relatedType,
            attachments: data.attachments,
          });

          // Update notification status
          await prisma.notificationQueue.update({
            where: { id: notification.id },
            data: {
              status: result.success ? 'COMPLETED' : 'FAILED',
              error: result.error,
              lastAttempt: new Date(),
            },
          });

          logger.info(`Notification ${notification.id} processed: ${result.success ? 'success' : 'failed'}`);
        } catch (error: any) {
          logger.error(`Error processing notification ${notification.id}:`, error);

          // Update notification status
          await prisma.notificationQueue.update({
            where: { id: notification.id },
            data: {
              status: notification.attempts >= 3 ? 'FAILED' : 'PENDING', // Retry up to 3 times
              error: error.message,
              lastAttempt: new Date(),
              // If failed, reschedule for 30 minutes later
              ...(notification.attempts < 3 && {
                scheduledFor: new Date(Date.now() + 30 * 60 * 1000),
              }),
            },
          });
        }
      }
    } catch (error) {
      logger.error('Error processing notification queue:', error);
    }
  }

  /**
   * Process invoice reminders
   * @private
   */
  private async processInvoiceReminders() {
    try {
      logger.info('Processing invoice reminders');

      // Get all tenants
      const tenants = await prisma.tenant.findMany();

      for (const tenant of tenants) {
        // Get tenant notification settings
        const settings = await notificationService.getSettings(tenant.id);

        // Skip if payment reminders are disabled
        if (!settings.paymentReminder.enabled) {
          continue;
        }

        // Get all unpaid invoices
        const invoices = await prisma.invoice.findMany({
          where: {
            tenantId: tenant.id,
            status: 'UNPAID',
          },
          include: {
            customer: true,
            notification: true,
          },
        });

        // Process each invoice
        for (const invoice of invoices) {
          try {
            // Skip if invoice doesn't have notification settings
            if (!invoice.notification) {
              continue;
            }

            // Skip if reminders are disabled for this invoice
            if (!invoice.notification.reminders.enabled) {
              continue;
            }

            const dueDate = new Date(invoice.dueDate);
            const today = new Date();
            const daysToDue = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

            // Check if reminder should be sent before due date
            if (daysToDue > 0 && daysToDue === invoice.notification.reminders.beforeDue) {
              // Schedule reminder notification
              await this.scheduleInvoiceReminder(invoice, tenant.id, 'before_due');
            }

            // Check if reminder should be sent after due date
            if (daysToDue < 0 && Math.abs(daysToDue) === invoice.notification.reminders.afterDue) {
              // Schedule reminder notification
              await this.scheduleInvoiceReminder(invoice, tenant.id, 'after_due');
            }
          } catch (error) {
            logger.error(`Error processing reminder for invoice ${invoice.id}:`, error);
          }
        }
      }
    } catch (error) {
      logger.error('Error processing invoice reminders:', error);
    }
  }

  /**
   * Process overdue invoices
   * @private
   */
  private async processOverdueInvoices() {
    try {
      logger.info('Processing overdue invoices');

      // Get all tenants
      const tenants = await prisma.tenant.findMany();

      for (const tenant of tenants) {
        // Get tenant notification settings
        const settings = await notificationService.getSettings(tenant.id);

        // Skip if payment overdue notifications are disabled
        if (!settings.paymentOverdue.enabled) {
          continue;
        }

        // Get all overdue invoices
        const invoices = await prisma.invoice.findMany({
          where: {
            tenantId: tenant.id,
            status: 'UNPAID',
            dueDate: {
              lt: new Date(),
            },
          },
          include: {
            customer: true,
            notification: true,
          },
        });

        // Process each invoice
        for (const invoice of invoices) {
          try {
            // Skip if invoice doesn't have notification settings
            if (!invoice.notification) {
              continue;
            }

            // Skip if escalation is disabled for this invoice
            if (!invoice.notification.escalation.enabled) {
              continue;
            }

            const dueDate = new Date(invoice.dueDate);
            const today = new Date();
            const daysOverdue = Math.ceil((today.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24));

            // Check if escalation should be triggered
            if (daysOverdue === invoice.notification.escalation.days) {
              // Schedule escalation notification
              await this.scheduleInvoiceEscalation(invoice, tenant.id);
            }

            // Check if invoice is in escalation schedule
            if (settings.paymentOverdue.escalation) {
              const escalationSchedule = settings.paymentOverdue.escalationSchedule as any[];
              for (const step of escalationSchedule) {
                if (daysOverdue === step.days) {
                  // Schedule escalation notification based on schedule
                  await this.scheduleInvoiceEscalation(invoice, tenant.id, step.method);
                }
              }
            }
          } catch (error) {
            logger.error(`Error processing escalation for invoice ${invoice.id}:`, error);
          }
        }
      }
    } catch (error) {
      logger.error('Error processing overdue invoices:', error);
    }
  }

  /**
   * Schedule invoice reminder
   * @private
   * @param invoice Invoice
   * @param tenantId Tenant ID
   * @param reminderType Reminder type
   */
  private async scheduleInvoiceReminder(invoice: any, tenantId: string, reminderType: 'before_due' | 'after_due') {
    try {
      // Get customer notification preferences
      const customerPreferences = await notificationService.getCustomerPreferences(invoice.customer.id);

      // Skip if customer has opted out
      if (customerPreferences.optOut) {
        return;
      }

      // Determine notification channel
      let channel = 'email';
      if (customerPreferences.preferredChannel !== 'default') {
        channel = customerPreferences.preferredChannel;
      } else if (invoice.notification.channels.whatsapp && !invoice.notification.channels.email) {
        channel = 'whatsapp';
      } else if (invoice.notification.channels.email && invoice.notification.channels.whatsapp) {
        channel = 'both';
      }

      // Prepare template data
      const templateData = {
        customer_name: invoice.customer.name,
        invoice_number: invoice.invoiceNumber,
        total_amount: invoice.totalAmount.toFixed(2),
        due_date: format(new Date(invoice.dueDate), 'MMMM dd, yyyy'),
        days_to_due: reminderType === 'before_due' ? invoice.notification.reminders.beforeDue : invoice.notification.reminders.afterDue,
        invoice_link: `${process.env.FRONTEND_URL}/invoices/${invoice.id}`,
      };

      // Schedule email notification
      if (channel === 'email' || channel === 'both') {
        await notificationService.scheduleNotification({
          type: reminderType === 'before_due' ? 'payment_reminder_before' : 'payment_reminder_after',
          channel: 'email',
          data: {
            recipient: invoice.customer.email,
            recipientName: invoice.customer.name,
            templateName: reminderType === 'before_due' ? 'payment_reminder_before' : 'payment_reminder_after',
            templateData,
            restrictToBusinessHours: true,
          },
          scheduledFor: new Date(),
          tenantId,
          relatedId: invoice.id,
          relatedType: 'invoice',
        });
      }

      // Schedule WhatsApp notification
      if ((channel === 'whatsapp' || channel === 'both') && invoice.customer.phone) {
        await notificationService.scheduleNotification({
          type: reminderType === 'before_due' ? 'payment_reminder_before' : 'payment_reminder_after',
          channel: 'whatsapp',
          data: {
            recipient: invoice.customer.phone,
            recipientName: invoice.customer.name,
            templateName: reminderType === 'before_due' ? 'payment_reminder_before' : 'payment_reminder_after',
            templateData,
            restrictToBusinessHours: true,
          },
          scheduledFor: new Date(),
          tenantId,
          relatedId: invoice.id,
          relatedType: 'invoice',
        });
      }
    } catch (error) {
      logger.error(`Error scheduling reminder for invoice ${invoice.id}:`, error);
      throw error;
    }
  }

  /**
   * Schedule invoice escalation
   * @private
   * @param invoice Invoice
   * @param tenantId Tenant ID
   * @param method Notification method
   */
  private async scheduleInvoiceEscalation(invoice: any, tenantId: string, method: string = 'both') {
    try {
      // Get customer notification preferences
      const customerPreferences = await notificationService.getCustomerPreferences(invoice.customer.id);

      // Skip if customer has opted out
      if (customerPreferences.optOut) {
        return;
      }

      // Prepare template data
      const templateData = {
        customer_name: invoice.customer.name,
        invoice_number: invoice.invoiceNumber,
        total_amount: invoice.totalAmount.toFixed(2),
        due_date: format(new Date(invoice.dueDate), 'MMMM dd, yyyy'),
        days_overdue: Math.ceil((new Date().getTime() - new Date(invoice.dueDate).getTime()) / (1000 * 60 * 60 * 24)),
        invoice_link: `${process.env.FRONTEND_URL}/invoices/${invoice.id}`,
      };

      // Schedule email notification
      if (method === 'email' || method === 'both') {
        await notificationService.scheduleNotification({
          type: 'payment_overdue',
          channel: 'email',
          data: {
            recipient: invoice.customer.email,
            recipientName: invoice.customer.name,
            templateName: 'payment_overdue',
            templateData,
            restrictToBusinessHours: true,
          },
          scheduledFor: new Date(),
          tenantId,
          relatedId: invoice.id,
          relatedType: 'invoice',
        });
      }

      // Schedule WhatsApp notification
      if ((method === 'whatsapp' || method === 'both') && invoice.customer.phone) {
        await notificationService.scheduleNotification({
          type: 'payment_overdue',
          channel: 'whatsapp',
          data: {
            recipient: invoice.customer.phone,
            recipientName: invoice.customer.name,
            templateName: 'payment_overdue',
            templateData,
            restrictToBusinessHours: true,
          },
          scheduledFor: new Date(),
          tenantId,
          relatedId: invoice.id,
          relatedType: 'invoice',
        });
      }
    } catch (error) {
      logger.error(`Error scheduling escalation for invoice ${invoice.id}:`, error);
      throw error;
    }
  }

  /**
   * Check if current time is within business hours
   * @private
   * @param tenantId Tenant ID
   * @returns Whether current time is within business hours
   */
  private async isWithinBusinessHours(tenantId: string): Promise<boolean> {
    try {
      // Get tenant notification settings
      const settings = await notificationService.getSettings(tenantId);

      // If business hours restriction is disabled, always return true
      if (!settings.businessHours.restrictToBusinessHours) {
        return true;
      }

      const now = new Date();
      const dayOfWeek = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
      const workDays = settings.businessHours.workDays as number[];

      // Check if today is a work day
      if (!workDays.includes(dayOfWeek)) {
        return false;
      }

      // Parse business hours
      const startTime = parseISO(`2000-01-01T${settings.businessHours.startTime}:00`);
      const endTime = parseISO(`2000-01-01T${settings.businessHours.endTime}:00`);

      // Create comparison times for today
      const nowTime = parseISO(`2000-01-01T${format(now, 'HH:mm')}:00`);

      // Check if current time is within business hours
      return isWithinInterval(nowTime, { start: startTime, end: endTime });
    } catch (error) {
      logger.error('Error checking business hours:', error);
      return true; // Default to true in case of error
    }
  }

  /**
   * Get next business hour
   * @private
   * @param tenantId Tenant ID
   * @returns Next business hour
   */
  private async getNextBusinessHour(tenantId: string): Promise<Date> {
    try {
      // Get tenant notification settings
      const settings = await notificationService.getSettings(tenantId);

      const now = new Date();
      const dayOfWeek = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
      const workDays = settings.businessHours.workDays as number[];

      // Parse business hours
      const startTime = settings.businessHours.startTime.split(':');
      const startHour = parseInt(startTime[0], 10);
      const startMinute = parseInt(startTime[1], 10);

      // If today is a work day and it's before start time, schedule for today's start time
      if (workDays.includes(dayOfWeek)) {
        const todayStart = new Date(now);
        todayStart.setHours(startHour, startMinute, 0, 0);

        if (now < todayStart) {
          return todayStart;
        }
      }

      // Find next work day
      let nextWorkDay = dayOfWeek;
      let daysToAdd = 1;

      while (daysToAdd <= 7) {
        nextWorkDay = (nextWorkDay + 1) % 7;
        if (workDays.includes(nextWorkDay)) {
          break;
        }
        daysToAdd++;
      }

      // Create date for next work day at start time
      const nextBusinessDay = addDays(now, daysToAdd);
      nextBusinessDay.setHours(startHour, startMinute, 0, 0);

      return nextBusinessDay;
    } catch (error) {
      logger.error('Error calculating next business hour:', error);
      // Default to tomorrow at 9 AM in case of error
      const tomorrow = addDays(new Date(), 1);
      tomorrow.setHours(9, 0, 0, 0);
      return tomorrow;
    }
  }
}

// Export singleton instance
export const notificationJobs = new NotificationJobs();
export default notificationJobs;
