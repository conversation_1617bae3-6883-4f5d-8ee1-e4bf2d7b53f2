'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { apiClient } from '@/lib/api/client';
import { useToast } from '@/components/ui/use-toast';
import Image from 'next/image';

export default function SecuritySettingsPage() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [is2FAEnabled, setIs2FAEnabled] = useState(false);
  const [is2FASetupMode, setIs2FASetupMode] = useState(false);
  const [verificationCode, setVerificationCode] = useState('');
  const [qrCodeUrl, setQrCodeUrl] = useState('');
  const [setupKey, setSetupKey] = useState('');
  const [recoveryCodesVisible, setRecoveryCodesVisible] = useState(false);
  const [recoveryCodes, setRecoveryCodes] = useState<string[]>([]);

  // Mock function to generate QR code and setup key
  const setupTwoFactor = async () => {
    setIsLoading(true);
    try {
      // In a real app, this would call the API to generate a QR code and setup key
      // const response = await apiClient.post('/auth/2fa/setup');
      // setQrCodeUrl(response.qrCodeUrl);
      // setSetupKey(response.setupKey);
      
      // Mock response
      setTimeout(() => {
        setQrCodeUrl('https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=otpauth://totp/Invoix:<EMAIL>?secret=JBSWY3DPEHPK3PXP&issuer=Invoix');
        setSetupKey('JBSWY3 DPEHPK 3PXP');
        setIs2FASetupMode(true);
        setIsLoading(false);
      }, 1000);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to setup two-factor authentication',
        variant: 'destructive',
      });
      setIsLoading(false);
    }
  };

  // Mock function to verify the setup
  const verifySetup = async () => {
    setIsLoading(true);
    try {
      // In a real app, this would call the API to verify the code
      // const response = await apiClient.post('/auth/2fa/verify', { code: verificationCode });
      
      // Mock verification
      setTimeout(() => {
        if (verificationCode.length === 6 && /^\d+$/.test(verificationCode)) {
          setIs2FAEnabled(true);
          setIs2FASetupMode(false);
          setRecoveryCodes([
            'ABCD-EFGH-IJKL-MNOP',
            'QRST-UVWX-YZ12-3456',
            'ABCD-EFGH-IJKL-MNOP',
            'QRST-UVWX-YZ12-3456',
            'ABCD-EFGH-IJKL-MNOP',
            'QRST-UVWX-YZ12-3456',
            'ABCD-EFGH-IJKL-MNOP',
            'QRST-UVWX-YZ12-3456',
          ]);
          setRecoveryCodesVisible(true);
          toast({
            title: 'Success',
            description: 'Two-factor authentication has been enabled',
            variant: 'default',
          });
        } else {
          toast({
            title: 'Error',
            description: 'Invalid verification code',
            variant: 'destructive',
          });
        }
        setIsLoading(false);
      }, 1000);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to verify code',
        variant: 'destructive',
      });
      setIsLoading(false);
    }
  };

  // Mock function to disable 2FA
  const disableTwoFactor = async () => {
    setIsLoading(true);
    try {
      // In a real app, this would call the API to disable 2FA
      // await apiClient.post('/auth/2fa/disable');
      
      // Mock disabling
      setTimeout(() => {
        setIs2FAEnabled(false);
        toast({
          title: 'Success',
          description: 'Two-factor authentication has been disabled',
          variant: 'default',
        });
        setIsLoading(false);
      }, 1000);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to disable two-factor authentication',
        variant: 'destructive',
      });
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-2xl font-bold">Security Settings</h1>
        <p className="text-text-secondary">Manage your account security settings</p>
      </div>

      {/* Two-Factor Authentication */}
      <Card className="border-none shadow-md">
        <CardHeader className="pb-2 border-b">
          <CardTitle className="text-lg font-bold text-text-primary">Two-Factor Authentication</CardTitle>
          <CardDescription className="text-text-secondary">
            Add an extra layer of security to your account by requiring a verification code in addition to your password.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {!is2FAEnabled && !is2FASetupMode && (
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <h4 className="text-sm font-medium">Two-Factor Authentication</h4>
                <p className="text-sm text-text-secondary">
                  Protect your account with an authenticator app
                </p>
              </div>
              <Button onClick={setupTwoFactor} disabled={isLoading}>
                {isLoading ? 'Setting up...' : 'Set up'}
              </Button>
            </div>
          )}

          {is2FASetupMode && (
            <div className="space-y-6">
              <Alert>
                <AlertDescription>
                  Scan the QR code with your authenticator app or enter the setup key manually.
                </AlertDescription>
              </Alert>
              
              <div className="flex flex-col md:flex-row gap-6 items-center">
                <div className="flex-shrink-0 bg-white p-4 rounded-lg border">
                  {qrCodeUrl && (
                    <Image 
                      src={qrCodeUrl} 
                      alt="QR Code" 
                      width={200} 
                      height={200} 
                      className="mx-auto"
                    />
                  )}
                </div>
                
                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium mb-1">Setup Key</h4>
                    <p className="font-mono bg-gray-100 p-2 rounded text-sm">{setupKey}</p>
                    <p className="text-xs text-text-secondary mt-1">
                      If you can't scan the QR code, enter this setup key manually in your app.
                    </p>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="verificationCode">Verification Code</Label>
                    <Input
                      id="verificationCode"
                      placeholder="Enter 6-digit code"
                      value={verificationCode}
                      onChange={(e) => setVerificationCode(e.target.value)}
                      maxLength={6}
                    />
                  </div>
                  
                  <div className="flex space-x-2">
                    <Button onClick={verifySetup} disabled={isLoading}>
                      {isLoading ? 'Verifying...' : 'Verify'}
                    </Button>
                    <Button 
                      variant="outline" 
                      onClick={() => setIs2FASetupMode(false)}
                      disabled={isLoading}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {is2FAEnabled && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <h4 className="text-sm font-medium">Two-Factor Authentication</h4>
                  <p className="text-sm text-text-secondary">
                    Two-factor authentication is enabled
                  </p>
                </div>
                <Button 
                  variant="destructive" 
                  onClick={disableTwoFactor}
                  disabled={isLoading}
                >
                  {isLoading ? 'Disabling...' : 'Disable'}
                </Button>
              </div>
              
              {recoveryCodesVisible && (
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Recovery Codes</h4>
                  <p className="text-sm text-text-secondary mb-2">
                    Save these recovery codes in a secure place. You can use them to access your account if you lose your authenticator device.
                  </p>
                  <div className="bg-gray-100 p-4 rounded-md">
                    <div className="grid grid-cols-2 gap-2">
                      {recoveryCodes.map((code, index) => (
                        <div key={index} className="font-mono text-sm">{code}</div>
                      ))}
                    </div>
                  </div>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => setRecoveryCodesVisible(false)}
                    className="mt-2"
                  >
                    Hide Recovery Codes
                  </Button>
                </div>
              )}
              
              {!recoveryCodesVisible && (
                <Button 
                  variant="outline" 
                  onClick={() => setRecoveryCodesVisible(true)}
                >
                  Show Recovery Codes
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Login Sessions */}
      <Card className="border-none shadow-md">
        <CardHeader className="pb-2 border-b">
          <CardTitle className="text-lg font-bold text-text-primary">Active Sessions</CardTitle>
          <CardDescription className="text-text-secondary">
            Manage your active login sessions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start justify-between p-4 border rounded-md">
              <div>
                <h4 className="text-sm font-medium">Current Session</h4>
                <p className="text-xs text-text-secondary">Chrome on Windows • IP: ***********</p>
                <p className="text-xs text-text-secondary">Started: Today at 10:30 AM</p>
              </div>
              <div className="bg-green-100 text-green-700 px-2 py-1 rounded text-xs">
                Current
              </div>
            </div>
            
            <Button variant="outline" className="w-full">
              Sign Out All Other Sessions
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
