import { Router } from 'express';
import { createInvoice, getInvoiceById, getInvoices, updateInvoice, deleteInvoice } from '../controllers/invoice.controller';
import { authenticate } from '../middleware/auth.middleware';
import { ExpressHandler } from '../types/express';

const router = Router();

// All routes are protected
router.post('/', authenticate as ExpressHandler, createInvoice as ExpressHandler);
router.get('/', authenticate as ExpressHandler, getInvoices as ExpressHandler);
router.get('/:id', authenticate as ExpressHandler, getInvoiceById as ExpressHandler);
router.put('/:id', authenticate as ExpressHandler, updateInvoice as ExpressHandler);
router.delete('/:id', authenticate as ExpressHandler, deleteInvoice as ExpressHandler);

export default router;
