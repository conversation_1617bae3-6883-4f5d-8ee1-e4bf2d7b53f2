// Inventory Management Module
model Product {
  id                String              @id @default(uuid())
  sku               String              
  name              String
  description       String?
  category          String?
  unitPrice         Float
  costPrice         Float
  taxRate           Float               @default(0)
  isActive          Boolean             @default(true)
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  inventoryItems    InventoryItem[]
  invoiceItems      InvoiceItem[]       // Link to existing invoice items
  purchaseItems     PurchaseOrderItem[]
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@unique([sku, tenantId])
  @@index([tenantId])
}

model InventoryItem {
  id                String              @id @default(uuid())
  productId         String
  product           Product             @relation(fields: [productId], references: [id])
  warehouseId       String
  warehouse         Warehouse           @relation(fields: [warehouseId], references: [id])
  quantity          Int                 @default(0)
  reorderLevel      Int                 @default(10)
  lastStockTake     DateTime?
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  transactions      InventoryTransaction[]
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@unique([productId, warehouseId, tenantId])
  @@index([tenantId])
  @@index([productId])
  @@index([warehouseId])
}

model Warehouse {
  id                String              @id @default(uuid())
  name              String
  location          String?
  isDefault         Boolean             @default(false)
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  inventoryItems    InventoryItem[]
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@unique([name, tenantId])
  @@index([tenantId])
}

model InventoryTransaction {
  id                String              @id @default(uuid())
  type              TransactionType
  quantity          Int
  notes             String?
  inventoryItemId   String
  inventoryItem     InventoryItem       @relation(fields: [inventoryItemId], references: [id])
  referenceId       String?             // Could be invoice ID, purchase order ID, etc.
  referenceType     String?             // Type of reference (Invoice, PurchaseOrder, etc.)
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdAt         DateTime            @default(now())
  createdBy         String?             // User ID who created the transaction

  @@index([tenantId])
  @@index([inventoryItemId])
  @@index([referenceId])
}

enum TransactionType {
  PURCHASE
  SALE
  ADJUSTMENT
  TRANSFER
  RETURN
  STOCK_TAKE
}

// Human Resources Module
model Employee {
  id                String              @id @default(uuid())
  employeeId        String              // Employee ID/Number
  name              String
  email             String
  phone             String?
  address           String?
  position          String?
  department        String?
  joinDate          DateTime
  terminationDate   DateTime?
  isActive          Boolean             @default(true)
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  salaryRecords     SalaryRecord[]
  attendanceRecords AttendanceRecord[]
  leaveRequests     LeaveRequest[]
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@unique([employeeId, tenantId])
  @@unique([email, tenantId])
  @@index([tenantId])
}

model SalaryRecord {
  id                String              @id @default(uuid())
  employeeId        String
  employee          Employee            @relation(fields: [employeeId], references: [id])
  month             Int
  year              Int
  basicSalary       Float
  allowances        Float               @default(0)
  deductions        Float               @default(0)
  tax               Float               @default(0)
  epf               Float               @default(0)      // Malaysian Employees Provident Fund
  socso             Float               @default(0)      // Malaysian Social Security
  netSalary         Float
  paymentDate       DateTime?
  paymentStatus     PaymentStatus       @default(PENDING)
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@unique([employeeId, month, year, tenantId])
  @@index([tenantId])
  @@index([employeeId])
}

enum PaymentStatus {
  PENDING
  PAID
  CANCELLED
}
