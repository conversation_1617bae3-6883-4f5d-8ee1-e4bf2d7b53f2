import { Request, Response } from 'express';
import { prisma } from '../index';
import fs from 'fs';
import path from 'path';

/**
 * Get the current LHDN configuration
 */
export const getLHDNConfig = async (req: Request, res: Response) => {
  try {
    // Check if user is admin
    if (!req.user || req.user.role !== 'ADMIN') {
      return res.status(403).json({ message: 'Unauthorized. Admin access required.' });
    }

    // Get the current configuration
    const config = await prisma.lHDNConfig.findFirst();

    if (!config) {
      return res.status(404).json({ message: 'LHDN configuration not found' });
    }

    // Don't return the certificate password
    const { certificatePassword, ...configWithoutPassword } = config;

    return res.status(200).json(configWithoutPassword);
  } catch (error: any) {
    console.error('Error getting LHDN configuration:', error);
    return res.status(500).json({ message: 'Failed to get LHDN configuration', error: error.message });
  }
};

/**
 * Update the LHDN configuration
 */
export const updateLHDNConfig = async (req: Request, res: Response) => {
  try {
    // Check if user is admin
    if (!req.user || req.user.role !== 'ADMIN') {
      return res.status(403).json({ message: 'Unauthorized. Admin access required.' });
    }

    const { apiBaseUrl, certificatePath, certificatePassword, isActive } = req.body;

    // Validate required fields
    if (!apiBaseUrl) {
      return res.status(400).json({ message: 'API base URL is required' });
    }

    // Check if configuration exists
    const existingConfig = await prisma.lHDNConfig.findFirst();

    let config;
    if (existingConfig) {
      // Update existing configuration
      config = await prisma.lHDNConfig.update({
        where: { id: existingConfig.id },
        data: {
          apiBaseUrl,
          certificatePath: certificatePath || existingConfig.certificatePath,
          certificatePassword: certificatePassword || existingConfig.certificatePassword,
          isActive: isActive !== undefined ? isActive : existingConfig.isActive,
        },
      });
    } else {
      // Create new configuration
      config = await prisma.lHDNConfig.create({
        data: {
          apiBaseUrl,
          certificatePath,
          certificatePassword,
          isActive: isActive !== undefined ? isActive : false,
        },
      });
    }

    // Don't return the certificate password
    const { certificatePassword: _, ...configWithoutPassword } = config;

    return res.status(200).json({
      message: 'LHDN configuration updated successfully',
      config: configWithoutPassword,
    });
  } catch (error: any) {
    console.error('Error updating LHDN configuration:', error);
    return res.status(500).json({ message: 'Failed to update LHDN configuration', error: error.message });
  }
};

/**
 * Test the LHDN configuration
 */
export const testLHDNConfig = async (req: Request, res: Response) => {
  try {
    // Check if user is admin
    if (!req.user || req.user.role !== 'ADMIN') {
      return res.status(403).json({ message: 'Unauthorized. Admin access required.' });
    }

    // Get the current configuration
    const config = await prisma.lHDNConfig.findFirst();

    if (!config) {
      return res.status(404).json({ message: 'LHDN configuration not found' });
    }

    // Check if certificate exists
    if (config.certificatePath) {
      try {
        // Check if the certificate file exists
        const certificateExists = fs.existsSync(config.certificatePath);
        
        if (!certificateExists) {
          return res.status(400).json({ 
            success: false,
            message: 'Certificate file not found at the specified path' 
          });
        }
      } catch (error) {
        console.error('Error checking certificate file:', error);
        return res.status(500).json({ 
          success: false,
          message: 'Error checking certificate file' 
        });
      }
    } else {
      return res.status(400).json({ 
        success: false,
        message: 'Certificate path not configured' 
      });
    }

    // In a real implementation, we would test the connection to the LHDN API
    // For now, we'll just simulate a successful test
    
    return res.status(200).json({
      success: true,
      message: 'Successfully connected to LHDN MyInvois API',
    });
  } catch (error: any) {
    console.error('Error testing LHDN configuration:', error);
    return res.status(500).json({ 
      success: false,
      message: 'Failed to test LHDN configuration', 
      error: error.message 
    });
  }
};
