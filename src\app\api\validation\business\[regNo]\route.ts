import { NextRequest, NextResponse } from 'next/server';

// Mock data for development
const mockValidBusinesses = [
  { regNo: '*********', name: 'ABC Company Sdn Bhd', type: 'COMPANY' },
  { regNo: '*********', name: 'XYZ Enterprise Sdn Bhd', type: 'COMPANY' },
  { regNo: '*********', name: 'Local Business Sdn Bhd', type: 'COMPANY' },
  { regNo: '*********', name: 'Global Services Sdn Bhd', type: 'COMPANY' },
  { regNo: '*********', name: 'Tech Solutions Sdn Bhd', type: 'COMPANY' },
];

export async function GET(
  request: NextRequest,
  { params }: { params: { regNo: string } }
) {
  const { regNo } = params;

  try {
    // In production, this would call your backend API
    // const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/validation/business/${regNo}`);
    // const data = await response.json();
    
    // For development, use mock data
    const mockBusiness = mockValidBusinesses.find(b => b.regNo === regNo);
    
    if (mockBusiness) {
      return NextResponse.json({
        regNo,
        isValid: true,
        businessName: mockBusiness.name,
        businessType: mockBusiness.type,
      });
    } else {
      return NextResponse.json(
        { 
          regNo,
          isValid: false,
          message: 'Business registration number not found' 
        },
        { status: 404 }
      );
    }
  } catch (error) {
    console.error('Error validating business registration number:', error);
    return NextResponse.json(
      { message: 'Failed to validate business registration number' },
      { status: 500 }
    );
  }
}
