'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import { PaymentMethod, PaymentGateway, createStripePaymentIntent, createPayPalPayment, createBillPlzPayment, recordManualPayment } from '@/lib/api/payment';
import { loadStripe } from '@stripe/stripe-js';
import { Elements, PaymentElement, useStripe, useElements } from '@stripe/react-stripe-js';

// Initialize Stripe
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLIC_KEY || '');

interface PaymentFormProps {
  invoiceId: string;
  invoiceNumber: string;
  amount: number;
  onPaymentComplete?: (paymentId: string) => void;
  onCancel?: () => void;
}

// Stripe Payment Form Component
const StripePaymentForm = ({ 
  clientSecret, 
  onSuccess, 
  onError 
}: { 
  clientSecret: string; 
  onSuccess: (paymentId: string) => void; 
  onError: (error: string) => void; 
}) => {
  const stripe = useStripe();
  const elements = useElements();
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsLoading(true);

    try {
      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${window.location.origin}/payment/success`,
        },
        redirect: 'if_required',
      });

      if (error) {
        onError(error.message || 'Payment failed');
      } else if (paymentIntent && paymentIntent.status === 'succeeded') {
        onSuccess(paymentIntent.id);
      } else {
        onError('Payment status unknown. Please check your email for confirmation.');
      }
    } catch (error: any) {
      onError(error.message || 'Payment processing error');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <PaymentElement />
      <div className="mt-6">
        <Button type="submit" disabled={!stripe || isLoading} className="w-full">
          {isLoading ? 'Processing...' : 'Pay Now'}
        </Button>
      </div>
    </form>
  );
};

// Main Payment Form Component
export default function PaymentForm({ invoiceId, invoiceNumber, amount, onPaymentComplete, onCancel }: PaymentFormProps) {
  const { toast } = useToast();
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [paymentGateways, setPaymentGateways] = useState<PaymentGateway[]>([]);
  const [selectedMethod, setSelectedMethod] = useState<string>('');
  const [selectedGateway, setSelectedGateway] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [paymentUrl, setPaymentUrl] = useState<string | null>(null);
  const [reference, setReference] = useState('');
  const [notes, setNotes] = useState('');

  // Manual payment fields
  const [showManualFields, setShowManualFields] = useState(false);

  // Fetch payment methods and gateways
  useEffect(() => {
    const fetchPaymentData = async () => {
      try {
        // In a real app, these would be API calls
        // const methodsResponse = await getPaymentMethods();
        // const gatewaysResponse = await getPaymentGateways();

        // Mock data
        const mockMethods: PaymentMethod[] = [
          { id: 'credit-card', type: 'CREDIT_CARD', name: 'Credit Card', isDefault: true },
          { id: 'bank-transfer', type: 'BANK_TRANSFER', name: 'Bank Transfer', isDefault: false },
          { id: 'cash', type: 'CASH', name: 'Cash', isDefault: false },
        ];

        const mockGateways: PaymentGateway[] = [
          { id: 'stripe', name: 'Stripe', provider: 'STRIPE', isActive: true, supportedMethods: ['CREDIT_CARD'] },
          { id: 'paypal', name: 'PayPal', provider: 'PAYPAL', isActive: true, supportedMethods: ['CREDIT_CARD'] },
          { id: 'billplz', name: 'BillPlz', provider: 'BILLPLZ', isActive: true, supportedMethods: ['BANK_TRANSFER'] },
        ];

        setPaymentMethods(mockMethods);
        setPaymentGateways(mockGateways);
        setSelectedMethod(mockMethods[0].id);
      } catch (error: any) {
        setError(error.message || 'Failed to load payment options');
      }
    };

    fetchPaymentData();
  }, []);

  // Update gateway when method changes
  useEffect(() => {
    if (selectedMethod && paymentGateways.length > 0) {
      const method = paymentMethods.find(m => m.id === selectedMethod);
      if (method) {
        const supportedGateways = paymentGateways.filter(g => 
          g.supportedMethods.includes(method.type) && g.isActive
        );
        
        if (supportedGateways.length > 0) {
          setSelectedGateway(supportedGateways[0].id);
          setShowManualFields(false);
        } else {
          setSelectedGateway('manual');
          setShowManualFields(true);
        }
      }
    }
  }, [selectedMethod, paymentGateways, paymentMethods]);

  const handlePayment = async () => {
    setIsLoading(true);
    setError(null);

    try {
      if (selectedGateway === 'manual') {
        // Record manual payment
        const response = await recordManualPayment(
          invoiceId, 
          amount, 
          selectedMethod, 
          reference, 
          notes
        );
        
        toast({
          title: 'Payment Recorded',
          description: 'The payment has been recorded successfully.',
          variant: 'default',
        });
        
        if (onPaymentComplete) {
          onPaymentComplete(response.id);
        }
      } else if (selectedGateway === 'stripe') {
        // Create Stripe payment intent
        const response = await createStripePaymentIntent(invoiceId, amount);
        setClientSecret(response.clientSecret);
      } else if (selectedGateway === 'paypal') {
        // Create PayPal payment
        const returnUrl = `${window.location.origin}/payment/success?invoice=${invoiceId}`;
        const response = await createPayPalPayment(invoiceId, amount, returnUrl);
        setPaymentUrl(response.paymentUrl);
        
        // Redirect to PayPal
        window.location.href = response.paymentUrl;
      } else if (selectedGateway === 'billplz') {
        // Create BillPlz payment
        const returnUrl = `${window.location.origin}/payment/success?invoice=${invoiceId}`;
        const response = await createBillPlzPayment(invoiceId, amount, returnUrl);
        setPaymentUrl(response.billUrl);
        
        // Redirect to BillPlz
        window.location.href = response.billUrl;
      }
    } catch (error: any) {
      setError(error.message || 'Payment processing failed');
      toast({
        title: 'Payment Error',
        description: error.message || 'There was an error processing your payment.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handlePaymentSuccess = (paymentId: string) => {
    toast({
      title: 'Payment Successful',
      description: 'Your payment has been processed successfully.',
      variant: 'default',
    });
    
    if (onPaymentComplete) {
      onPaymentComplete(paymentId);
    }
  };

  const handlePaymentError = (errorMessage: string) => {
    setError(errorMessage);
    toast({
      title: 'Payment Failed',
      description: errorMessage,
      variant: 'destructive',
    });
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Payment for Invoice #{invoiceNumber}</CardTitle>
        <CardDescription>
          Total Amount: RM {amount.toFixed(2)}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        {!clientSecret ? (
          <div className="space-y-6">
            <div className="space-y-2">
              <Label>Payment Method</Label>
              <RadioGroup value={selectedMethod} onValueChange={setSelectedMethod}>
                {paymentMethods.map(method => (
                  <div key={method.id} className="flex items-center space-x-2">
                    <RadioGroupItem value={method.id} id={method.id} />
                    <Label htmlFor={method.id}>{method.name}</Label>
                  </div>
                ))}
              </RadioGroup>
            </div>
            
            {!showManualFields && (
              <div className="space-y-2">
                <Label>Payment Gateway</Label>
                <Select value={selectedGateway} onValueChange={setSelectedGateway}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select payment gateway" />
                  </SelectTrigger>
                  <SelectContent>
                    {paymentGateways
                      .filter(gateway => {
                        const method = paymentMethods.find(m => m.id === selectedMethod);
                        return method && gateway.supportedMethods.includes(method.type);
                      })
                      .map(gateway => (
                        <SelectItem key={gateway.id} value={gateway.id}>
                          {gateway.name}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>
            )}
            
            {showManualFields && (
              <div className="space-y-4">
                <Input
                  label="Reference Number"
                  placeholder="e.g., Bank transfer reference"
                  value={reference}
                  onChange={(e) => setReference(e.target.value)}
                />
                <Input
                  label="Notes"
                  placeholder="Additional payment details"
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                />
              </div>
            )}
          </div>
        ) : (
          <Elements stripe={stripePromise} options={{ clientSecret }}>
            <StripePaymentForm 
              clientSecret={clientSecret} 
              onSuccess={handlePaymentSuccess} 
              onError={handlePaymentError} 
            />
          </Elements>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={onCancel} disabled={isLoading}>
          Cancel
        </Button>
        {!clientSecret && (
          <Button onClick={handlePayment} disabled={isLoading}>
            {isLoading ? 'Processing...' : 'Continue'}
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}
