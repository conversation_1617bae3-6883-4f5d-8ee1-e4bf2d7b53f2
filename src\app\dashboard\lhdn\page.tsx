'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Spinner } from '@/components/ui/spinner';
import Link from 'next/link';

export default function LHDNDashboardPage() {
  const [stats, setStats] = useState({
    validatedInvoices: 0,
    pendingInvoices: 0,
    validatedTINs: 0,
    pendingTINs: 0,
    complianceScore: 0,
  });

  const [isLoading, setIsLoading] = useState(true);
  const [recentIssues, setRecentIssues] = useState<any[]>([]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // In a real implementation, this would fetch data from your API
        // const response = await fetch('/api/lhdn/dashboard');
        // const data = await response.json();
        // setStats(data.stats);
        // setRecentIssues(data.recentIssues);

        // For development, use mock data
        await new Promise(resolve => setTimeout(resolve, 1000));

        setStats({
          validatedInvoices: 124,
          pendingInvoices: 18,
          validatedTINs: 45,
          pendingTINs: 7,
          complianceScore: 92,
        });

        setRecentIssues([
          {
            id: '1',
            type: 'invoice',
            documentId: 'INV-2025-042',
            issue: 'Missing customer TIN',
            date: '2025-01-15',
            status: 'critical',
          },
          {
            id: '2',
            type: 'customer',
            documentId: 'CUST-089',
            issue: 'Invalid TIN format',
            date: '2025-01-12',
            status: 'warning',
          },
          {
            id: '3',
            type: 'invoice',
            documentId: 'INV-2025-038',
            issue: 'Validation failed',
            date: '2025-01-10',
            status: 'critical',
          },
        ]);
      } catch (err) {
        console.error('Error fetching LHDN dashboard data:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spinner size="lg" />
        <span className="ml-2 text-text-secondary">Loading...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow-sm mb-6">
        <h1 className="text-2xl font-bold text-text-primary flex items-center">
          <svg className="w-6 h-6 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
          </svg>
          LHDN Compliance Dashboard
        </h1>
        <p className="mt-1 text-sm text-text-secondary">
          Monitor and manage your LHDN MyInvois compliance status
        </p>
      </div>



      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="border-none shadow-md">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Validated Invoices</CardTitle>
            <svg className="h-4 w-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.validatedInvoices}</div>
            <p className="text-xs text-text-secondary">Successfully validated with LHDN</p>
          </CardContent>
        </Card>

        <Card className="border-none shadow-md">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Invoices</CardTitle>
            <svg className="h-4 w-4 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pendingInvoices}</div>
            <p className="text-xs text-text-secondary">Awaiting validation</p>
          </CardContent>
          <CardFooter>
            <Link href="/dashboard/invoices" className="text-xs text-indigo-600 hover:text-indigo-800">
              View Invoices →
            </Link>
          </CardFooter>
        </Card>

        <Card className="border-none shadow-md">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Validated TINs</CardTitle>
            <svg className="h-4 w-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.validatedTINs}</div>
            <p className="text-xs text-text-secondary">Verified customer tax IDs</p>
          </CardContent>
        </Card>

        <Card className="border-none shadow-md">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Compliance Score</CardTitle>
            <svg className="h-4 w-4 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
            </svg>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.complianceScore}%</div>
            <div className="w-full bg-gray-200 rounded-full h-2.5 mt-2">
              <div
                className={`h-2.5 rounded-full ${
                  stats.complianceScore >= 90 ? 'bg-green-500' :
                  stats.complianceScore >= 70 ? 'bg-yellow-500' :
                  'bg-red-500'
                }`}
                style={{ width: `${stats.complianceScore}%` }}
              ></div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card className="border-none shadow-md">
        <CardHeader className="pb-2 border-b">
          <CardTitle className="text-lg font-bold text-text-primary">Quick Actions</CardTitle>
          <CardDescription className="text-text-secondary">Common LHDN compliance tasks</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Link href="/dashboard/customers/validate-tins" className="block">
              <Button variant="outline" className="w-full justify-start">
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Validate Customer TINs
              </Button>
            </Link>
            <Link href="/dashboard/documents/search" className="block">
              <Button variant="outline" className="w-full justify-start">
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                Search LHDN Documents
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>

      {/* Recent Issues */}
      <Card className="border-none shadow-md">
        <CardHeader className="pb-2 border-b">
          <CardTitle className="text-lg font-bold text-text-primary">Recent Compliance Issues</CardTitle>
          <CardDescription className="text-text-secondary">Issues that need your attention</CardDescription>
        </CardHeader>
        <CardContent>
          {recentIssues.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full text-sm text-left" className="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr className="bg-white hover:bg-gray-50 transition-colors">
                    <th className="px-3 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">Type</th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">ID</th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">Issue</th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">Date</th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">Status</th>
                    <th className="px-3 py-3 text-right text-xs font-medium text-text-secondary uppercase tracking-wider">Action</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {recentIssues.map((issue) => (
                    <tr key={issue.id} className="bg-white  transition-colors">
                      <td className="px-3 py-4 whitespace-nowrap text-sm text-text-primary capitalize">{issue.type}</td>
                      <td className="px-3 py-4 whitespace-nowrap text-sm font-medium text-text-primary">{issue.documentId}</td>
                      <td className="px-3 py-4 whitespace-nowrap text-sm text-text-secondary">{issue.issue}</td>
                      <td className="px-3 py-4 whitespace-nowrap text-sm text-text-secondary">{issue.date}</td>
                      <td className="px-3 py-4 whitespace-nowrap">
                        <Badge
                          variant={issue.status === 'critical' ? 'destructive' : 'warning'}
                        >
                          {issue.status}
                        </Badge>
                      </td>
                      <td className="px-3 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <Link
                          href={issue.type === 'invoice' ? `/dashboard/invoices/${issue.id}` : `/dashboard/customers/${issue.id}`}
                          className="text-indigo-600 hover:text-indigo-900"
                        >
                          Fix
                        </Link>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-8">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-text-primary">No compliance issues</h3>
              <p className="mt-1 text-sm text-text-secondary">Great job! Your LHDN compliance is up to date.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
