import { prisma } from '../index';
import { logger } from '../utils/logger';
import { format, subMonths, startOfMonth, endOfMonth, parseISO } from 'date-fns';
import * as ExcelJS from 'exceljs';
import * as PDFDocument from 'pdfkit';
import * as fs from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';

/**
 * Report Service
 * Handles generation of various reports and analytics
 */
export class ReportService {
  /**
   * Generate profit and loss report
   * @param tenantId Tenant ID
   * @param filters Report filters
   * @param options Report options
   */
  async generateProfitLossReport(tenantId: string, filters: any, options: any) {
    try {
      // Parse date filters
      const startDate = filters.startDate ? new Date(filters.startDate) : subMonths(new Date(), 1);
      const endDate = filters.endDate ? new Date(filters.endDate) : new Date();

      // Get all invoices for the period
      const invoices = await prisma.invoice.findMany({
        where: {
          tenantId,
          invoiceDate: {
            gte: startDate,
            lte: endDate,
          },
        },
        include: {
          items: true,
          payments: true,
        },
      });

      // Get all expenses for the period
      const expenses = await prisma.expense.findMany({
        where: {
          tenantId,
          date: {
            gte: startDate,
            lte: endDate,
          },
        },
        include: {
          category: true,
        },
      });

      // Calculate revenue
      const revenue = invoices.reduce((total, invoice) => {
        return total + invoice.totalAmount;
      }, 0);

      // Calculate cost of goods sold
      const cogs = invoices.reduce((total, invoice) => {
        return total + invoice.items.reduce((itemTotal, item) => {
          return itemTotal + (item.costPrice || 0) * item.quantity;
        }, 0);
      }, 0);

      // Calculate expenses by category
      const expensesByCategory = expenses.reduce((acc, expense) => {
        const categoryName = expense.category?.name || 'Uncategorized';
        if (!acc[categoryName]) {
          acc[categoryName] = 0;
        }
        acc[categoryName] += expense.amount;
        return acc;
      }, {} as Record<string, number>);

      // Calculate total expenses
      const totalExpenses = expenses.reduce((total, expense) => {
        return total + expense.amount;
      }, 0);

      // Calculate gross profit
      const grossProfit = revenue - cogs;

      // Calculate net profit
      const netProfit = grossProfit - totalExpenses;

      // Format the report
      const report = {
        startDate: format(startDate, 'yyyy-MM-dd'),
        endDate: format(endDate, 'yyyy-MM-dd'),
        revenue,
        cogs,
        grossProfit,
        grossProfitMargin: revenue > 0 ? (grossProfit / revenue) * 100 : 0,
        expenses: expensesByCategory,
        totalExpenses,
        netProfit,
        netProfitMargin: revenue > 0 ? (netProfit / revenue) * 100 : 0,
      };

      // If format is specified, export the report
      if (options?.format && options.format !== 'json') {
        return this.exportReport('profit_loss', report, options.format, tenantId);
      }

      return report;
    } catch (error) {
      logger.error('Error generating profit and loss report:', error);
      throw error;
    }
  }

  /**
   * Generate balance sheet report
   * @param tenantId Tenant ID
   * @param filters Report filters
   * @param options Report options
   */
  async generateBalanceSheetReport(tenantId: string, filters: any, options: any) {
    try {
      // Parse date filter (balance sheet is as of a specific date)
      const asOfDate = filters.asOfDate ? new Date(filters.asOfDate) : new Date();

      // Get all assets
      const assets = await prisma.asset.findMany({
        where: {
          tenantId,
          purchaseDate: {
            lte: asOfDate,
          },
        },
      });

      // Get all unpaid invoices (accounts receivable)
      const accountsReceivable = await prisma.invoice.findMany({
        where: {
          tenantId,
          invoiceDate: {
            lte: asOfDate,
          },
          status: {
            in: ['DRAFT', 'SENT', 'PARTIALLY_PAID'],
          },
        },
        include: {
          payments: true,
        },
      });

      // Get all unpaid bills (accounts payable)
      const accountsPayable = await prisma.expense.findMany({
        where: {
          tenantId,
          date: {
            lte: asOfDate,
          },
          status: {
            in: ['PENDING', 'PARTIALLY_PAID'],
          },
        },
        include: {
          payments: true,
        },
      });

      // Get inventory value
      const inventory = await prisma.product.findMany({
        where: {
          tenantId,
        },
        include: {
          inventoryTransactions: {
            where: {
              date: {
                lte: asOfDate,
              },
            },
          },
        },
      });

      // Calculate inventory value
      const inventoryValue = inventory.reduce((total, product) => {
        const stockQuantity = product.inventoryTransactions.reduce((qty, transaction) => {
          if (transaction.type === 'STOCK_IN') {
            return qty + transaction.quantity;
          } else if (transaction.type === 'STOCK_OUT') {
            return qty - transaction.quantity;
          }
          return qty;
        }, 0);
        
        return total + (stockQuantity * (product.costPrice || 0));
      }, 0);

      // Calculate accounts receivable value
      const accountsReceivableValue = accountsReceivable.reduce((total, invoice) => {
        const paidAmount = invoice.payments.reduce((paid, payment) => {
          return paid + payment.amount;
        }, 0);
        return total + (invoice.totalAmount - paidAmount);
      }, 0);

      // Calculate accounts payable value
      const accountsPayableValue = accountsPayable.reduce((total, expense) => {
        const paidAmount = expense.payments.reduce((paid, payment) => {
          return paid + payment.amount;
        }, 0);
        return total + (expense.amount - paidAmount);
      }, 0);

      // Calculate fixed assets value
      const fixedAssetsValue = assets.reduce((total, asset) => {
        // Calculate depreciation
        const purchaseDate = new Date(asset.purchaseDate);
        const monthsDiff = (asOfDate.getFullYear() - purchaseDate.getFullYear()) * 12 + 
                          (asOfDate.getMonth() - purchaseDate.getMonth());
        
        const depreciationPerMonth = asset.depreciationRate ? 
          (asset.purchasePrice * (asset.depreciationRate / 100)) / 12 : 0;
        
        const totalDepreciation = Math.min(
          depreciationPerMonth * monthsDiff,
          asset.purchasePrice
        );
        
        const currentValue = asset.purchasePrice - totalDepreciation;
        
        return total + currentValue;
      }, 0);

      // Format the report
      const report = {
        asOfDate: format(asOfDate, 'yyyy-MM-dd'),
        assets: {
          currentAssets: {
            cash: 0, // This would come from bank accounts/cash accounts
            accountsReceivable: accountsReceivableValue,
            inventory: inventoryValue,
            totalCurrentAssets: accountsReceivableValue + inventoryValue,
          },
          fixedAssets: {
            propertyAndEquipment: fixedAssetsValue,
            totalFixedAssets: fixedAssetsValue,
          },
          totalAssets: accountsReceivableValue + inventoryValue + fixedAssetsValue,
        },
        liabilities: {
          currentLiabilities: {
            accountsPayable: accountsPayableValue,
            totalCurrentLiabilities: accountsPayableValue,
          },
          longTermLiabilities: {
            loans: 0, // This would come from loans table
            totalLongTermLiabilities: 0,
          },
          totalLiabilities: accountsPayableValue,
        },
        equity: {
          ownersEquity: 0, // This would come from equity accounts
          retainedEarnings: (accountsReceivableValue + inventoryValue + fixedAssetsValue) - accountsPayableValue,
          totalEquity: (accountsReceivableValue + inventoryValue + fixedAssetsValue) - accountsPayableValue,
        },
      };

      // If format is specified, export the report
      if (options?.format && options.format !== 'json') {
        return this.exportReport('balance_sheet', report, options.format, tenantId);
      }

      return report;
    } catch (error) {
      logger.error('Error generating balance sheet report:', error);
      throw error;
    }
  }

  /**
   * Generate cash flow report
   * @param tenantId Tenant ID
   * @param filters Report filters
   * @param options Report options
   */
  async generateCashFlowReport(tenantId: string, filters: any, options: any) {
    try {
      // Parse date filters
      const startDate = filters.startDate ? new Date(filters.startDate) : subMonths(new Date(), 1);
      const endDate = filters.endDate ? new Date(filters.endDate) : new Date();

      // Get all payments received
      const paymentsReceived = await prisma.payment.findMany({
        where: {
          tenantId,
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
          status: 'COMPLETED',
        },
        include: {
          invoice: true,
        },
      });

      // Get all expenses paid
      const expensesPaid = await prisma.expensePayment.findMany({
        where: {
          tenantId,
          date: {
            gte: startDate,
            lte: endDate,
          },
        },
        include: {
          expense: true,
        },
      });

      // Calculate total payments received
      const totalPaymentsReceived = paymentsReceived.reduce((total, payment) => {
        return total + payment.amount;
      }, 0);

      // Calculate total expenses paid
      const totalExpensesPaid = expensesPaid.reduce((total, payment) => {
        return total + payment.amount;
      }, 0);

      // Calculate net cash flow
      const netCashFlow = totalPaymentsReceived - totalExpensesPaid;

      // Format the report
      const report = {
        startDate: format(startDate, 'yyyy-MM-dd'),
        endDate: format(endDate, 'yyyy-MM-dd'),
        cashInflows: {
          paymentsReceived: totalPaymentsReceived,
          totalInflows: totalPaymentsReceived,
        },
        cashOutflows: {
          expensesPaid: totalExpensesPaid,
          totalOutflows: totalExpensesPaid,
        },
        netCashFlow,
      };

      // If format is specified, export the report
      if (options?.format && options.format !== 'json') {
        return this.exportReport('cash_flow', report, options.format, tenantId);
      }

      return report;
    } catch (error) {
      logger.error('Error generating cash flow report:', error);
      throw error;
    }
  }

  /**
   * Generate sales report
   * @param tenantId Tenant ID
   * @param type Report type
   * @param filters Report filters
   * @param options Report options
   */
  async generateSalesReport(tenantId: string, type: string, filters: any, options: any) {
    try {
      // Parse date filters
      const startDate = filters.startDate ? new Date(filters.startDate) : subMonths(new Date(), 1);
      const endDate = filters.endDate ? new Date(filters.endDate) : new Date();

      // Get all invoices for the period
      const invoices = await prisma.invoice.findMany({
        where: {
          tenantId,
          invoiceDate: {
            gte: startDate,
            lte: endDate,
          },
          ...(filters.customerId && { customerId: filters.customerId }),
        },
        include: {
          customer: true,
          items: {
            include: {
              product: true,
            },
          },
          payments: true,
        },
      });

      let report: any;

      switch (type) {
        case 'by_customer':
          // Group sales by customer
          const salesByCustomer = invoices.reduce((acc, invoice) => {
            const customerId = invoice.customerId;
            const customerName = invoice.customer?.name || 'Unknown';
            
            if (!acc[customerId]) {
              acc[customerId] = {
                customerId,
                customerName,
                invoiceCount: 0,
                totalAmount: 0,
                paidAmount: 0,
                outstandingAmount: 0,
              };
            }
            
            acc[customerId].invoiceCount += 1;
            acc[customerId].totalAmount += invoice.totalAmount;
            
            const paidAmount = invoice.payments.reduce((total, payment) => {
              return total + payment.amount;
            }, 0);
            
            acc[customerId].paidAmount += paidAmount;
            acc[customerId].outstandingAmount += (invoice.totalAmount - paidAmount);
            
            return acc;
          }, {} as Record<string, any>);
          
          report = {
            startDate: format(startDate, 'yyyy-MM-dd'),
            endDate: format(endDate, 'yyyy-MM-dd'),
            salesByCustomer: Object.values(salesByCustomer),
            totalSales: Object.values(salesByCustomer).reduce((total, customer: any) => {
              return total + customer.totalAmount;
            }, 0),
            totalPaid: Object.values(salesByCustomer).reduce((total, customer: any) => {
              return total + customer.paidAmount;
            }, 0),
            totalOutstanding: Object.values(salesByCustomer).reduce((total, customer: any) => {
              return total + customer.outstandingAmount;
            }, 0),
          };
          break;
          
        case 'by_product':
          // Group sales by product
          const salesByProduct = invoices.reduce((acc, invoice) => {
            invoice.items.forEach(item => {
              const productId = item.productId || 'custom';
              const productName = item.product?.name || item.description || 'Custom Item';
              
              if (!acc[productId]) {
                acc[productId] = {
                  productId,
                  productName,
                  quantity: 0,
                  totalAmount: 0,
                };
              }
              
              acc[productId].quantity += item.quantity;
              acc[productId].totalAmount += item.totalAmount;
            });
            
            return acc;
          }, {} as Record<string, any>);
          
          report = {
            startDate: format(startDate, 'yyyy-MM-dd'),
            endDate: format(endDate, 'yyyy-MM-dd'),
            salesByProduct: Object.values(salesByProduct),
            totalQuantity: Object.values(salesByProduct).reduce((total, product: any) => {
              return total + product.quantity;
            }, 0),
            totalSales: Object.values(salesByProduct).reduce((total, product: any) => {
              return total + product.totalAmount;
            }, 0),
          };
          break;
          
        case 'by_time':
          // Group sales by month
          const salesByMonth: Record<string, any> = {};
          
          invoices.forEach(invoice => {
            const invoiceDate = new Date(invoice.invoiceDate);
            const monthKey = format(invoiceDate, 'yyyy-MM');
            const monthName = format(invoiceDate, 'MMMM yyyy');
            
            if (!salesByMonth[monthKey]) {
              salesByMonth[monthKey] = {
                month: monthKey,
                monthName,
                invoiceCount: 0,
                totalAmount: 0,
                paidAmount: 0,
              };
            }
            
            salesByMonth[monthKey].invoiceCount += 1;
            salesByMonth[monthKey].totalAmount += invoice.totalAmount;
            
            const paidAmount = invoice.payments.reduce((total, payment) => {
              return total + payment.amount;
            }, 0);
            
            salesByMonth[monthKey].paidAmount += paidAmount;
          });
          
          // Sort by month
          const sortedMonths = Object.keys(salesByMonth).sort();
          
          report = {
            startDate: format(startDate, 'yyyy-MM-dd'),
            endDate: format(endDate, 'yyyy-MM-dd'),
            salesByMonth: sortedMonths.map(month => salesByMonth[month]),
            totalSales: Object.values(salesByMonth).reduce((total, month: any) => {
              return total + month.totalAmount;
            }, 0),
            totalPaid: Object.values(salesByMonth).reduce((total, month: any) => {
              return total + month.paidAmount;
            }, 0),
          };
          break;
          
        default:
          throw new Error(`Unsupported sales report type: ${type}`);
      }

      // If format is specified, export the report
      if (options?.format && options.format !== 'json') {
        return this.exportReport(`sales_${type}`, report, options.format, tenantId);
      }

      return report;
    } catch (error) {
      logger.error('Error generating sales report:', error);
      throw error;
    }
  }

  /**
   * Export report to file
   * @param reportType Report type
   * @param data Report data
   * @param format Export format
   * @param tenantId Tenant ID
   */
  private async exportReport(reportType: string, data: any, format: string, tenantId: string) {
    try {
      // Create directory if it doesn't exist
      const dir = path.join(__dirname, '../../uploads/reports', tenantId);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      const fileName = `${reportType}_${format(new Date(), 'yyyyMMdd_HHmmss')}.${format}`;
      const filePath = path.join(dir, fileName);

      switch (format) {
        case 'pdf':
          await this.exportToPdf(reportType, data, filePath);
          break;
        case 'xlsx':
          await this.exportToExcel(reportType, data, filePath);
          break;
        case 'csv':
          await this.exportToCsv(reportType, data, filePath);
          break;
        default:
          throw new Error(`Unsupported export format: ${format}`);
      }

      return {
        url: `/uploads/reports/${tenantId}/${fileName}`,
        fileName,
      };
    } catch (error) {
      logger.error('Error exporting report:', error);
      throw error;
    }
  }

  /**
   * Export report to PDF
   * @param reportType Report type
   * @param data Report data
   * @param filePath File path
   */
  private async exportToPdf(reportType: string, data: any, filePath: string) {
    // This is a simplified implementation
    // In a real app, you would use a proper PDF generation library
    // and create a well-formatted PDF report
    const doc = new PDFDocument();
    const stream = fs.createWriteStream(filePath);
    
    doc.pipe(stream);
    
    // Add report title
    doc.fontSize(20).text(`${this.formatReportTitle(reportType)} Report`, { align: 'center' });
    doc.moveDown();
    
    // Add date range
    if (data.startDate && data.endDate) {
      doc.fontSize(12).text(`Period: ${data.startDate} to ${data.endDate}`, { align: 'center' });
      doc.moveDown();
    } else if (data.asOfDate) {
      doc.fontSize(12).text(`As of: ${data.asOfDate}`, { align: 'center' });
      doc.moveDown();
    }
    
    // Add report data
    doc.fontSize(12).text(JSON.stringify(data, null, 2));
    
    doc.end();
    
    return new Promise((resolve, reject) => {
      stream.on('finish', resolve);
      stream.on('error', reject);
    });
  }

  /**
   * Export report to Excel
   * @param reportType Report type
   * @param data Report data
   * @param filePath File path
   */
  private async exportToExcel(reportType: string, data: any, filePath: string) {
    // This is a simplified implementation
    // In a real app, you would create a well-formatted Excel report
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet(this.formatReportTitle(reportType));
    
    // Add report title
    worksheet.mergeCells('A1:E1');
    worksheet.getCell('A1').value = `${this.formatReportTitle(reportType)} Report`;
    worksheet.getCell('A1').font = { size: 16, bold: true };
    worksheet.getCell('A1').alignment = { horizontal: 'center' };
    
    // Add date range
    worksheet.mergeCells('A2:E2');
    if (data.startDate && data.endDate) {
      worksheet.getCell('A2').value = `Period: ${data.startDate} to ${data.endDate}`;
    } else if (data.asOfDate) {
      worksheet.getCell('A2').value = `As of: ${data.asOfDate}`;
    }
    worksheet.getCell('A2').alignment = { horizontal: 'center' };
    
    // Add report data based on report type
    let rowIndex = 4;
    
    switch (reportType) {
      case 'profit_loss':
        // Add revenue section
        worksheet.getCell(`A${rowIndex}`).value = 'Revenue';
        worksheet.getCell(`A${rowIndex}`).font = { bold: true };
        worksheet.getCell(`B${rowIndex}`).value = data.revenue;
        rowIndex += 2;
        
        // Add COGS section
        worksheet.getCell(`A${rowIndex}`).value = 'Cost of Goods Sold';
        worksheet.getCell(`A${rowIndex}`).font = { bold: true };
        worksheet.getCell(`B${rowIndex}`).value = data.cogs;
        rowIndex += 2;
        
        // Add gross profit section
        worksheet.getCell(`A${rowIndex}`).value = 'Gross Profit';
        worksheet.getCell(`A${rowIndex}`).font = { bold: true };
        worksheet.getCell(`B${rowIndex}`).value = data.grossProfit;
        rowIndex += 2;
        
        // Add expenses section
        worksheet.getCell(`A${rowIndex}`).value = 'Expenses';
        worksheet.getCell(`A${rowIndex}`).font = { bold: true };
        rowIndex++;
        
        // Add expense categories
        for (const [category, amount] of Object.entries(data.expenses)) {
          worksheet.getCell(`A${rowIndex}`).value = category;
          worksheet.getCell(`B${rowIndex}`).value = amount;
          rowIndex++;
        }
        
        // Add total expenses
        worksheet.getCell(`A${rowIndex}`).value = 'Total Expenses';
        worksheet.getCell(`A${rowIndex}`).font = { bold: true };
        worksheet.getCell(`B${rowIndex}`).value = data.totalExpenses;
        rowIndex += 2;
        
        // Add net profit
        worksheet.getCell(`A${rowIndex}`).value = 'Net Profit';
        worksheet.getCell(`A${rowIndex}`).font = { bold: true };
        worksheet.getCell(`B${rowIndex}`).value = data.netProfit;
        break;
        
      case 'sales_by_customer':
        // Add headers
        worksheet.getCell(`A${rowIndex}`).value = 'Customer';
        worksheet.getCell(`B${rowIndex}`).value = 'Invoice Count';
        worksheet.getCell(`C${rowIndex}`).value = 'Total Amount';
        worksheet.getCell(`D${rowIndex}`).value = 'Paid Amount';
        worksheet.getCell(`E${rowIndex}`).value = 'Outstanding Amount';
        worksheet.getRow(rowIndex).font = { bold: true };
        rowIndex++;
        
        // Add data rows
        data.salesByCustomer.forEach((customer: any) => {
          worksheet.getCell(`A${rowIndex}`).value = customer.customerName;
          worksheet.getCell(`B${rowIndex}`).value = customer.invoiceCount;
          worksheet.getCell(`C${rowIndex}`).value = customer.totalAmount;
          worksheet.getCell(`D${rowIndex}`).value = customer.paidAmount;
          worksheet.getCell(`E${rowIndex}`).value = customer.outstandingAmount;
          rowIndex++;
        });
        
        // Add totals
        worksheet.getCell(`A${rowIndex}`).value = 'Total';
        worksheet.getCell(`A${rowIndex}`).font = { bold: true };
        worksheet.getCell(`C${rowIndex}`).value = data.totalSales;
        worksheet.getCell(`D${rowIndex}`).value = data.totalPaid;
        worksheet.getCell(`E${rowIndex}`).value = data.totalOutstanding;
        break;
        
      // Add other report types as needed
      
      default:
        // For other report types, just add the data as JSON
        worksheet.getCell(`A${rowIndex}`).value = JSON.stringify(data, null, 2);
    }
    
    await workbook.xlsx.writeFile(filePath);
  }

  /**
   * Export report to CSV
   * @param reportType Report type
   * @param data Report data
   * @param filePath File path
   */
  private async exportToCsv(reportType: string, data: any, filePath: string) {
    // This is a simplified implementation
    // In a real app, you would create a well-formatted CSV report
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet(this.formatReportTitle(reportType));
    
    // Add report data based on report type
    switch (reportType) {
      case 'profit_loss':
        // Add headers
        worksheet.addRow(['Category', 'Amount']);
        
        // Add data rows
        worksheet.addRow(['Revenue', data.revenue]);
        worksheet.addRow(['Cost of Goods Sold', data.cogs]);
        worksheet.addRow(['Gross Profit', data.grossProfit]);
        
        // Add expenses
        for (const [category, amount] of Object.entries(data.expenses)) {
          worksheet.addRow([category, amount]);
        }
        
        worksheet.addRow(['Total Expenses', data.totalExpenses]);
        worksheet.addRow(['Net Profit', data.netProfit]);
        break;
        
      case 'sales_by_customer':
        // Add headers
        worksheet.addRow(['Customer', 'Invoice Count', 'Total Amount', 'Paid Amount', 'Outstanding Amount']);
        
        // Add data rows
        data.salesByCustomer.forEach((customer: any) => {
          worksheet.addRow([
            customer.customerName,
            customer.invoiceCount,
            customer.totalAmount,
            customer.paidAmount,
            customer.outstandingAmount,
          ]);
        });
        
        // Add totals
        worksheet.addRow(['Total', '', data.totalSales, data.totalPaid, data.totalOutstanding]);
        break;
        
      // Add other report types as needed
      
      default:
        // For other report types, just add the data as JSON
        worksheet.addRow(['Report Data']);
        worksheet.addRow([JSON.stringify(data)]);
    }
    
    await workbook.csv.writeFile(filePath);
  }

  /**
   * Format report title
   * @param reportType Report type
   */
  private formatReportTitle(reportType: string): string {
    switch (reportType) {
      case 'profit_loss':
        return 'Profit and Loss';
      case 'balance_sheet':
        return 'Balance Sheet';
      case 'cash_flow':
        return 'Cash Flow';
      case 'sales_by_customer':
        return 'Sales by Customer';
      case 'sales_by_product':
        return 'Sales by Product';
      case 'sales_by_time':
        return 'Sales by Time';
      default:
        return reportType.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
    }
  }
}

export const reportService = new ReportService();
export default reportService;
