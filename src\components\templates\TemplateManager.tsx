'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Spinner } from '@/components/ui/spinner';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  createTemplate, 
  updateTemplate, 
  deleteTemplate, 
  getTemplates 
} from '@/lib/api/templates';

interface Template {
  id: string;
  name: string;
  template: string;
  createdAt: string;
  updatedAt: string;
}

export default function TemplateManager() {
  const [templates, setTemplates] = useState<Template[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    template: JSON.stringify({
      showLogo: true,
      logo: {
        x: 50,
        y: 45,
        width: 150
      },
      header: {
        title: {
          text: 'INVOICE',
          fontSize: 20,
          align: 'right'
        },
        invoiceNumber: {
          fontSize: 10,
          align: 'right'
        },
        issueDate: {
          align: 'right'
        },
        dueDate: {
          align: 'right'
        }
      },
      company: {
        fontSize: 12,
        addressFontSize: 10
      },
      customer: {
        title: 'Bill To:',
        fontSize: 12,
        detailsFontSize: 10
      },
      items: {
        fontSize: 10,
        headerBackground: true,
        headerBackgroundColor: '#f0f0f0',
        headerTextColor: '#000000',
        rowTextColor: '#000000',
        bottomLine: true,
        headers: ['Item', 'Description', 'Qty', 'Unit Price', 'Amount'],
        columnWidths: [150, 150, 50, 70, 70],
        headerAlign: ['left', 'left', 'right', 'right', 'right']
      },
      totals: {
        totalFontSize: 12
      },
      notes: {
        fontSize: 10
      },
      lhdn: {
        fontSize: 10
      },
      footer: {
        fontSize: 8,
        text: 'Generated by Invoix - {{date}}'
      }
    }, null, 2)
  });

  useEffect(() => {
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const data = await getTemplates();
      setTemplates(data.templates);
    } catch (err: any) {
      console.error('Error fetching templates:', err);
      setError(err.message || 'Failed to load templates');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const validateTemplate = (template: string): boolean => {
    try {
      JSON.parse(template);
      return true;
    } catch (err) {
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      // Validate the template JSON
      if (!validateTemplate(formData.template)) {
        throw new Error('Invalid template JSON format');
      }

      if (selectedTemplate) {
        // Update existing template
        await updateTemplate(selectedTemplate.id, formData.name, formData.template);
        setSuccess('Template updated successfully');
      } else {
        // Create new template
        await createTemplate(formData.name, formData.template);
        setSuccess('Template created successfully');
      }

      // Refresh the templates list
      await fetchTemplates();
      
      // Reset the form
      setFormData({
        name: '',
        template: JSON.stringify({
          // Default template structure
          showLogo: true,
          logo: {
            x: 50,
            y: 45,
            width: 150
          },
          header: {
            title: {
              text: 'INVOICE',
              fontSize: 20,
              align: 'right'
            },
            invoiceNumber: {
              fontSize: 10,
              align: 'right'
            },
            issueDate: {
              align: 'right'
            },
            dueDate: {
              align: 'right'
            }
          },
          company: {
            fontSize: 12,
            addressFontSize: 10
          },
          customer: {
            title: 'Bill To:',
            fontSize: 12,
            detailsFontSize: 10
          },
          items: {
            fontSize: 10,
            headerBackground: true,
            headerBackgroundColor: '#f0f0f0',
            headerTextColor: '#000000',
            rowTextColor: '#000000',
            bottomLine: true,
            headers: ['Item', 'Description', 'Qty', 'Unit Price', 'Amount'],
            columnWidths: [150, 150, 50, 70, 70],
            headerAlign: ['left', 'left', 'right', 'right', 'right']
          },
          totals: {
            totalFontSize: 12
          },
          notes: {
            fontSize: 10
          },
          lhdn: {
            fontSize: 10
          },
          footer: {
            fontSize: 8,
            text: 'Generated by Invoix - {{date}}'
          }
        }, null, 2)
      });
      
      setSelectedTemplate(null);
      setIsDialogOpen(false);
    } catch (err: any) {
      console.error('Error saving template:', err);
      setError(err.message || 'Failed to save template');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEdit = (template: Template) => {
    setSelectedTemplate(template);
    setFormData({
      name: template.name,
      template: JSON.stringify(JSON.parse(template.template), null, 2)
    });
    setIsDialogOpen(true);
  };

  const handleDelete = async (template: Template) => {
    if (!confirm(`Are you sure you want to delete the template "${template.name}"?`)) {
      return;
    }

    setIsDeleting(true);
    setError(null);
    setSuccess(null);

    try {
      await deleteTemplate(template.id);
      setSuccess('Template deleted successfully');
      
      // Refresh the templates list
      await fetchTemplates();
    } catch (err: any) {
      console.error('Error deleting template:', err);
      setError(err.message || 'Failed to delete template');
    } finally {
      setIsDeleting(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spinner size="lg" />
        <span className="ml-2 text-gray-600">Loading templates...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      
      {success && (
        <Alert variant="success">
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold">PDF Templates</h2>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => {
              setSelectedTemplate(null);
              setFormData({
                name: '',
                template: JSON.stringify({
                  // Default template structure
                  showLogo: true,
                  logo: {
                    x: 50,
                    y: 45,
                    width: 150
                  },
                  header: {
                    title: {
                      text: 'INVOICE',
                      fontSize: 20,
                      align: 'right'
                    },
                    invoiceNumber: {
                      fontSize: 10,
                      align: 'right'
                    },
                    issueDate: {
                      align: 'right'
                    },
                    dueDate: {
                      align: 'right'
                    }
                  },
                  company: {
                    fontSize: 12,
                    addressFontSize: 10
                  },
                  customer: {
                    title: 'Bill To:',
                    fontSize: 12,
                    detailsFontSize: 10
                  },
                  items: {
                    fontSize: 10,
                    headerBackground: true,
                    headerBackgroundColor: '#f0f0f0',
                    headerTextColor: '#000000',
                    rowTextColor: '#000000',
                    bottomLine: true,
                    headers: ['Item', 'Description', 'Qty', 'Unit Price', 'Amount'],
                    columnWidths: [150, 150, 50, 70, 70],
                    headerAlign: ['left', 'left', 'right', 'right', 'right']
                  },
                  totals: {
                    totalFontSize: 12
                  },
                  notes: {
                    fontSize: 10
                  },
                  lhdn: {
                    fontSize: 10
                  },
                  footer: {
                    fontSize: 8,
                    text: 'Generated by Invoix - {{date}}'
                  }
                }, null, 2)
              });
            }}>
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              New Template
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-3xl">
            <DialogHeader>
              <DialogTitle>{selectedTemplate ? 'Edit Template' : 'Create Template'}</DialogTitle>
              <DialogDescription>
                {selectedTemplate 
                  ? 'Update the template details below.' 
                  : 'Create a new PDF template for your invoices.'}
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit}>
              <div className="space-y-4 py-4">
                <div className="grid w-full items-center gap-1.5">
                  <label htmlFor="name" className="text-sm font-medium">Template Name</label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="grid w-full items-center gap-1.5">
                  <label htmlFor="template" className="text-sm font-medium">Template JSON</label>
                  <Textarea
                    id="template"
                    name="template"
                    value={formData.template}
                    onChange={handleInputChange}
                    rows={15}
                    className="font-mono text-sm"
                    required
                  />
                  <p className="text-xs text-gray-500">
                    Enter the template configuration in JSON format. This defines how your PDF will be generated.
                  </p>
                </div>
              </div>
              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <Spinner size="sm" className="mr-2" />
                      Saving...
                    </>
                  ) : (
                    'Save Template'
                  )}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {templates.length === 0 ? (
        <Card>
          <CardContent className="py-10">
            <div className="text-center">
              <svg className="w-12 h-12 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">No templates</h3>
              <p className="mt-1 text-sm text-gray-500">
                Get started by creating a new template.
              </p>
              <div className="mt-6">
                <Button onClick={() => setIsDialogOpen(true)}>
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  New Template
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Updated</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {templates.map((template) => (
                  <TableRow key={template.id}>
                    <TableCell className="font-medium">{template.name}</TableCell>
                    <TableCell>{formatDate(template.createdAt)}</TableCell>
                    <TableCell>{formatDate(template.updatedAt)}</TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEdit(template)}
                        className="mr-2"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(template)}
                        className="text-red-600 hover:text-red-800 hover:bg-red-50"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
