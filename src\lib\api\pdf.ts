/**
 * Download an invoice PDF
 * @param invoiceId The ID of the invoice to download
 */
export const downloadInvoicePDF = (invoiceId: string): void => {
  // Create a hidden anchor element
  const link = document.createElement('a');
  link.href = `/api/pdf/invoices/${invoiceId}/download`;
  link.target = '_blank';
  link.download = `invoice-${invoiceId}.pdf`;

  // Append to the document and trigger a click
  document.body.appendChild(link);
  link.click();

  // Clean up
  document.body.removeChild(link);
};

/**
 * View an invoice PDF in a new tab
 * @param invoiceId The ID of the invoice to view
 */
export const viewInvoicePDF = (invoiceId: string): void => {
  // Open in a new tab
  window.open(`/api/pdf/invoices/${invoiceId}/view`, '_blank');
};

/**
 * Preview an invoice PDF in an iframe
 * @param invoiceId The ID of the invoice to preview
 * @returns The URL for the PDF preview
 */
export const getInvoicePDFPreviewUrl = (invoiceId: string): string => {
  return `/api/pdf/invoices/${invoiceId}/view`;
};
