import { Request, Response } from 'express';
import Stripe from 'stripe';
import { logger } from '../utils/logger';
import { paymentService } from '../services/payment.service';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
  apiVersion: '2023-10-16',
});

/**
 * Webhook Controller
 * Handles webhook events from external services
 */
export class WebhookController {
  /**
   * Handle Stripe webhook events
   * @param req Express request
   * @param res Express response
   */
  async handleStripeWebhook(req: Request, res: Response) {
    const signature = req.headers['stripe-signature'] as string;

    if (!signature) {
      logger.error('Stripe webhook signature missing');
      return res.status(400).json({ error: 'Webhook signature missing' });
    }

    try {
      // Verify the event
      const event = stripe.webhooks.constructEvent(
        req.body,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET || ''
      );

      // Handle the event
      await paymentService.handleStripeWebhook(event);

      // Return a response to acknowledge receipt of the event
      res.json({ received: true });
    } catch (err) {
      logger.error('Error handling Stripe webhook:', err);
      res.status(400).json({ error: (err as Error).message });
    }
  }

  /**
   * Handle PayPal webhook events
   * @param req Express request
   * @param res Express response
   */
  async handlePayPalWebhook(req: Request, res: Response) {
    try {
      // Verify the event (implementation depends on PayPal SDK)
      const event = req.body;

      // TODO: Implement PayPal webhook handling
      logger.info('PayPal webhook received:', event);

      // Return a response to acknowledge receipt of the event
      res.json({ received: true });
    } catch (err) {
      logger.error('Error handling PayPal webhook:', err);
      res.status(400).json({ error: (err as Error).message });
    }
  }

  /**
   * Handle BillPlz webhook events
   * @param req Express request
   * @param res Express response
   */
  async handleBillPlzWebhook(req: Request, res: Response) {
    try {
      // Verify the event (implementation depends on BillPlz API)
      const event = req.body;

      // TODO: Implement BillPlz webhook handling
      logger.info('BillPlz webhook received:', event);

      // Return a response to acknowledge receipt of the event
      res.json({ received: true });
    } catch (err) {
      logger.error('Error handling BillPlz webhook:', err);
      res.status(400).json({ error: (err as Error).message });
    }
  }
}

export const webhookController = new WebhookController();
export default webhookController;
