import { apiClient } from './client';

// Types
export interface ReportFilter {
  startDate?: string;
  endDate?: string;
  customerId?: string;
  productId?: string;
  categoryId?: string;
  status?: string;
  paymentMethod?: string;
  employeeId?: string;
  departmentId?: string;
  projectId?: string;
  warehouseId?: string;
  vendorId?: string;
  assetId?: string;
  [key: string]: any;
}

export interface ReportOptions {
  format?: 'json' | 'pdf' | 'csv' | 'xlsx';
  groupBy?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  page?: number;
}

export interface ReportData {
  id: string;
  name: string;
  type: string;
  data: any;
  filters: ReportFilter;
  options: ReportOptions;
  createdAt: string;
  createdBy: string;
}

export interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  type: string;
  category: string;
  isSystem: boolean;
  filters: ReportFilter;
  options: ReportOptions;
}

export interface ChartData {
  type: 'bar' | 'line' | 'pie' | 'doughnut' | 'area';
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string | string[];
    borderWidth?: number;
  }[];
}

export interface DashboardWidget {
  id: string;
  type: 'chart' | 'metric' | 'table' | 'list';
  title: string;
  size: 'small' | 'medium' | 'large';
  position: number;
  reportId?: string;
  chartType?: 'bar' | 'line' | 'pie' | 'doughnut' | 'area';
  data?: any;
  filters?: ReportFilter;
  options?: ReportOptions;
}

export interface Dashboard {
  id: string;
  name: string;
  description?: string;
  isDefault: boolean;
  widgets: DashboardWidget[];
}

/**
 * Report API Service
 * Provides methods for interacting with reporting-related API endpoints
 */
class ReportService {
  /**
   * Get financial reports
   */
  async getFinancialReport(
    type: 'profit_loss' | 'balance_sheet' | 'cash_flow',
    filters: ReportFilter,
    options?: ReportOptions
  ): Promise<any> {
    return apiClient.get(`/reports/financial/${type}`, { params: { ...filters, ...options } });
  }

  /**
   * Get sales reports
   */
  async getSalesReport(
    type: 'by_customer' | 'by_product' | 'by_category' | 'by_time',
    filters: ReportFilter,
    options?: ReportOptions
  ): Promise<any> {
    return apiClient.get(`/reports/sales/${type}`, { params: { ...filters, ...options } });
  }

  /**
   * Get tax reports
   */
  async getTaxReport(
    type: 'summary' | 'detailed',
    filters: ReportFilter,
    options?: ReportOptions
  ): Promise<any> {
    return apiClient.get(`/reports/tax/${type}`, { params: { ...filters, ...options } });
  }

  /**
   * Get inventory reports
   */
  async getInventoryReport(
    type: 'stock_levels' | 'valuation' | 'movement' | 'low_stock',
    filters: ReportFilter,
    options?: ReportOptions
  ): Promise<any> {
    return apiClient.get(`/reports/inventory/${type}`, { params: { ...filters, ...options } });
  }

  /**
   * Get HR reports
   */
  async getHRReport(
    type: 'attendance' | 'leave' | 'payroll' | 'performance',
    filters: ReportFilter,
    options?: ReportOptions
  ): Promise<any> {
    return apiClient.get(`/reports/hr/${type}`, { params: { ...filters, ...options } });
  }

  /**
   * Get procurement reports
   */
  async getProcurementReport(
    type: 'purchases' | 'vendors' | 'expenses',
    filters: ReportFilter,
    options?: ReportOptions
  ): Promise<any> {
    return apiClient.get(`/reports/procurement/${type}`, { params: { ...filters, ...options } });
  }

  /**
   * Get project reports
   */
  async getProjectReport(
    type: 'overview' | 'tasks' | 'time' | 'budget',
    filters: ReportFilter,
    options?: ReportOptions
  ): Promise<any> {
    return apiClient.get(`/reports/projects/${type}`, { params: { ...filters, ...options } });
  }

  /**
   * Get asset reports
   */
  async getAssetReport(
    type: 'register' | 'depreciation' | 'maintenance',
    filters: ReportFilter,
    options?: ReportOptions
  ): Promise<any> {
    return apiClient.get(`/reports/assets/${type}`, { params: { ...filters, ...options } });
  }

  /**
   * Get custom report
   */
  async getCustomReport(
    reportId: string,
    filters?: ReportFilter,
    options?: ReportOptions
  ): Promise<any> {
    return apiClient.get(`/reports/custom/${reportId}`, { params: { ...filters, ...options } });
  }

  /**
   * Create custom report
   */
  async createCustomReport(data: {
    name: string;
    description?: string;
    type: string;
    filters?: ReportFilter;
    options?: ReportOptions;
  }): Promise<ReportTemplate> {
    return apiClient.post('/reports/custom', data);
  }

  /**
   * Update custom report
   */
  async updateCustomReport(
    reportId: string,
    data: Partial<ReportTemplate>
  ): Promise<ReportTemplate> {
    return apiClient.put(`/reports/custom/${reportId}`, data);
  }

  /**
   * Delete custom report
   */
  async deleteCustomReport(reportId: string): Promise<{ success: boolean }> {
    return apiClient.delete(`/reports/custom/${reportId}`);
  }

  /**
   * Get report templates
   */
  async getReportTemplates(category?: string): Promise<ReportTemplate[]> {
    return apiClient.get('/reports/templates', { params: { category } });
  }

  /**
   * Export report
   */
  async exportReport(
    reportType: string,
    reportId: string,
    format: 'pdf' | 'csv' | 'xlsx',
    filters?: ReportFilter,
    options?: ReportOptions
  ): Promise<{ url: string }> {
    return apiClient.post(`/reports/${reportType}/${reportId}/export`, {
      format,
      filters,
      options,
    });
  }

  /**
   * Get chart data
   */
  async getChartData(
    chartType: string,
    filters?: ReportFilter,
    options?: ReportOptions
  ): Promise<ChartData> {
    return apiClient.get(`/reports/charts/${chartType}`, { params: { ...filters, ...options } });
  }

  /**
   * Get dashboards
   */
  async getDashboards(): Promise<Dashboard[]> {
    return apiClient.get('/dashboards');
  }

  /**
   * Get dashboard by ID
   */
  async getDashboard(dashboardId: string): Promise<Dashboard> {
    return apiClient.get(`/dashboards/${dashboardId}`);
  }

  /**
   * Create dashboard
   */
  async createDashboard(data: {
    name: string;
    description?: string;
    isDefault?: boolean;
    widgets?: Partial<DashboardWidget>[];
  }): Promise<Dashboard> {
    return apiClient.post('/dashboards', data);
  }

  /**
   * Update dashboard
   */
  async updateDashboard(
    dashboardId: string,
    data: Partial<Dashboard>
  ): Promise<Dashboard> {
    return apiClient.put(`/dashboards/${dashboardId}`, data);
  }

  /**
   * Delete dashboard
   */
  async deleteDashboard(dashboardId: string): Promise<{ success: boolean }> {
    return apiClient.delete(`/dashboards/${dashboardId}`);
  }

  /**
   * Add widget to dashboard
   */
  async addDashboardWidget(
    dashboardId: string,
    widget: Partial<DashboardWidget>
  ): Promise<DashboardWidget> {
    return apiClient.post(`/dashboards/${dashboardId}/widgets`, widget);
  }

  /**
   * Update dashboard widget
   */
  async updateDashboardWidget(
    dashboardId: string,
    widgetId: string,
    data: Partial<DashboardWidget>
  ): Promise<DashboardWidget> {
    return apiClient.put(`/dashboards/${dashboardId}/widgets/${widgetId}`, data);
  }

  /**
   * Delete dashboard widget
   */
  async deleteDashboardWidget(
    dashboardId: string,
    widgetId: string
  ): Promise<{ success: boolean }> {
    return apiClient.delete(`/dashboards/${dashboardId}/widgets/${widgetId}`);
  }

  /**
   * Reorder dashboard widgets
   */
  async reorderDashboardWidgets(
    dashboardId: string,
    widgetIds: string[]
  ): Promise<{ success: boolean }> {
    return apiClient.put(`/dashboards/${dashboardId}/widgets/reorder`, { widgetIds });
  }
}

export const reportService = new ReportService();
export default reportService;
