'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';

export default function EmailConfig() {
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [config, setConfig] = useState({
    smtp: {
      host: 'smtp.example.com',
      port: '587',
      username: '<EMAIL>',
      password: '••••••••',
      fromEmail: '<EMAIL>',
      fromName: 'Your Business Name',
    },
    templates: [
      {
        id: 'invoice_created',
        name: 'Invoice Created',
        subject: 'Your invoice #{invoice_number} has been created',
        status: 'active',
      },
      {
        id: 'payment_reminder',
        name: 'Payment Reminder',
        subject: 'Reminder: Invoice #{invoice_number} is due soon',
        status: 'active',
      },
      {
        id: 'payment_overdue',
        name: 'Payment Overdue',
        subject: 'Invoice #{invoice_number} is overdue',
        status: 'active',
      },
      {
        id: 'payment_received',
        name: 'Payment Received',
        subject: 'Payment received for invoice #{invoice_number}',
        status: 'active',
      },
    ],
    settings: {
      enableAutoSend: true,
      enablePaymentReminders: true,
      reminderDaysBefore: 3,
      enableOverdueNotices: true,
      overdueReminderDays: [1, 7, 14], // Days after due date to send reminders
      includeInvoicePdf: true,
      includeLogo: true,
    }
  });

  const handleToggleAutoSend = (checked: boolean) => {
    setConfig({
      ...config,
      settings: {
        ...config.settings,
        enableAutoSend: checked
      }
    });
  };

  const handleTogglePaymentReminders = (checked: boolean) => {
    setConfig({
      ...config,
      settings: {
        ...config.settings,
        enablePaymentReminders: checked
      }
    });
  };

  const handleToggleOverdueNotices = (checked: boolean) => {
    setConfig({
      ...config,
      settings: {
        ...config.settings,
        enableOverdueNotices: checked
      }
    });
  };

  const handleSaveConfig = () => {
    // In a real implementation, this would call your API
    setSuccessMessage('Email configuration saved successfully');
    
    // Clear success message after 3 seconds
    setTimeout(() => {
      setSuccessMessage(null);
    }, 3000);
  };

  return (
    <div className="space-y-6">
      {successMessage && (
        <Alert className="bg-green-50 text-green-800 border-green-200">
          <AlertDescription>{successMessage}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="settings" className="space-y-6">
        <TabsList>
          <TabsTrigger value="settings">Email Settings</TabsTrigger>
          <TabsTrigger value="templates">Email Templates</TabsTrigger>
          <TabsTrigger value="smtp">SMTP Configuration</TabsTrigger>
        </TabsList>

        <TabsContent value="settings">
          <Card>
            <CardHeader>
              <CardTitle>Notification Settings</CardTitle>
              <CardDescription>
                Configure your email notification settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="autoSend">Automatic Notifications</Label>
                    <p className="text-sm text-gray-500">
                      Automatically send email notifications when invoices are created
                    </p>
                  </div>
                  <Switch
                    id="autoSend"
                    checked={config.settings.enableAutoSend}
                    onCheckedChange={handleToggleAutoSend}
                  />
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="paymentReminders">Payment Reminders</Label>
                    <p className="text-sm text-gray-500">
                      Send payment reminders before invoice due date
                    </p>
                  </div>
                  <Switch
                    id="paymentReminders"
                    checked={config.settings.enablePaymentReminders}
                    onCheckedChange={handleTogglePaymentReminders}
                  />
                </div>
                
                {config.settings.enablePaymentReminders && (
                  <div className="space-y-2 pl-6 border-l-2 border-gray-100">
                    <Label htmlFor="reminderDays">Days Before Due Date</Label>
                    <Input 
                      id="reminderDays" 
                      type="number"
                      min="1"
                      max="30"
                      value={config.settings.reminderDaysBefore.toString()} 
                      onChange={(e) => setConfig({
                        ...config,
                        settings: {
                          ...config.settings,
                          reminderDaysBefore: parseInt(e.target.value) || 0
                        }
                      })}
                    />
                  </div>
                )}

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="overdueNotices">Overdue Notices</Label>
                    <p className="text-sm text-gray-500">
                      Send notifications when invoices are past due
                    </p>
                  </div>
                  <Switch
                    id="overdueNotices"
                    checked={config.settings.enableOverdueNotices}
                    onCheckedChange={handleToggleOverdueNotices}
                  />
                </div>
                
                {config.settings.enableOverdueNotices && (
                  <div className="space-y-2 pl-6 border-l-2 border-gray-100">
                    <Label>Days After Due Date</Label>
                    <div className="flex flex-wrap gap-2">
                      {config.settings.overdueReminderDays.map((day, index) => (
                        <Badge key={index} variant="outline" className="px-3 py-1">
                          {day} {day === 1 ? 'day' : 'days'}
                        </Badge>
                      ))}
                      <Button variant="outline" size="sm">+ Add</Button>
                    </div>
                  </div>
                )}

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="includePdf">Include PDF Invoice</Label>
                    <p className="text-sm text-gray-500">
                      Attach PDF invoice to email notifications
                    </p>
                  </div>
                  <Switch
                    id="includePdf"
                    checked={config.settings.includeInvoicePdf}
                    onCheckedChange={(checked) => setConfig({
                      ...config,
                      settings: {
                        ...config.settings,
                        includeInvoicePdf: checked
                      }
                    })}
                  />
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSaveConfig}>Save Settings</Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="templates">
          <Card>
            <CardHeader>
              <CardTitle>Email Templates</CardTitle>
              <CardDescription>
                Configure your email templates for different notification types
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {config.templates.map((template) => (
                  <div key={template.id} className="p-4 border rounded-md">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-medium">{template.name}</h3>
                        <p className="text-sm text-gray-500 mt-1">{template.subject}</p>
                      </div>
                      <Button variant="outline" size="sm">
                        Edit
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSaveConfig}>Save Templates</Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="smtp">
          <Card>
            <CardHeader>
              <CardTitle>SMTP Configuration</CardTitle>
              <CardDescription>
                Configure your SMTP server for sending emails
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="smtpHost">SMTP Host</Label>
                  <Input 
                    id="smtpHost" 
                    value={config.smtp.host} 
                    onChange={(e) => setConfig({
                      ...config,
                      smtp: {
                        ...config.smtp,
                        host: e.target.value
                      }
                    })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="smtpPort">SMTP Port</Label>
                  <Input 
                    id="smtpPort" 
                    value={config.smtp.port} 
                    onChange={(e) => setConfig({
                      ...config,
                      smtp: {
                        ...config.smtp,
                        port: e.target.value
                      }
                    })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="smtpUsername">Username</Label>
                  <Input 
                    id="smtpUsername" 
                    value={config.smtp.username} 
                    onChange={(e) => setConfig({
                      ...config,
                      smtp: {
                        ...config.smtp,
                        username: e.target.value
                      }
                    })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="smtpPassword">Password</Label>
                  <Input 
                    id="smtpPassword" 
                    type="password"
                    value={config.smtp.password} 
                    onChange={(e) => setConfig({
                      ...config,
                      smtp: {
                        ...config.smtp,
                        password: e.target.value
                      }
                    })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="fromEmail">From Email</Label>
                  <Input 
                    id="fromEmail" 
                    value={config.smtp.fromEmail} 
                    onChange={(e) => setConfig({
                      ...config,
                      smtp: {
                        ...config.smtp,
                        fromEmail: e.target.value
                      }
                    })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="fromName">From Name</Label>
                  <Input 
                    id="fromName" 
                    value={config.smtp.fromName} 
                    onChange={(e) => setConfig({
                      ...config,
                      smtp: {
                        ...config.smtp,
                        fromName: e.target.value
                      }
                    })}
                  />
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline">Test Connection</Button>
              <Button onClick={handleSaveConfig}>Save SMTP Settings</Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
