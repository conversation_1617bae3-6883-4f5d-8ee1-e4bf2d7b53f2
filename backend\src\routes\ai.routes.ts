import { Router } from 'express';
import {
  getFinancialInsights,
  detectInvoiceFraud,
  getPaymentPredictions,
  processDocumentWithOCR,
  getCustomerInsights,
  getAIRecommendations,
  validateInvoice,
  upload
} from '../controllers/ai.controller';
import { authenticate } from '../middleware/auth.middleware';
import { ExpressHandler } from '../types/express';

const router = Router();

// All routes are protected
router.get('/insights', authenticate as ExpressHandler, getFinancialInsights as ExpressHandler);
router.get('/fraud/:invoiceId', authenticate as ExpressHandler, detectInvoiceFraud as ExpressHandler);
router.get('/payment-predictions/:customerId', authenticate as ExpressHandler, getPaymentPredictions as ExpressHandler);

// Document processing route
router.post('/process-document', authenticate as ExpressHandler, upload.single('document'), processDocumentWithOCR as ExpressHandler);

// Customer insights route
router.get('/customer-insights', authenticate as ExpressHandler, getCustomerInsights as ExpressHandler);

// AI recommendations routes
router.get('/recommendations/:type', authenticate as ExpressHandler, getAIRecommendations as ExpressHandler);

// LHDN validation route
router.get('/validate-invoice/:invoiceId', authenticate as ExpressHandler, validateInvoice as ExpressHandler);

export default router;
