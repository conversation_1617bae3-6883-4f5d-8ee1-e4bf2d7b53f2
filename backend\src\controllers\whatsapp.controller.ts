import { Request, Response } from 'express';
import { prisma } from '../index';
import config from '../config/config';
import { generateInvoicePDF } from '../services/pdf.service';
import fs from 'fs-extra';

// Mock Twilio client for now - would be replaced with actual Twilio integration
const twilioClient = {
  messages: {
    create: async (options: any) => {
      console.log('Sending WhatsApp message:', options);
      return { sid: 'mock-message-sid' };
    },
  },
};

// Send invoice via WhatsApp
export const sendInvoiceViaWhatsApp = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId } = req.user;
    const { invoiceId, message } = req.body;

    // Check if WhatsApp integration is configured for the tenant
    const whatsappSettings = await prisma.whatsappSetting.findUnique({
      where: { tenantId },
    });

    if (!whatsappSettings || !whatsappSettings.isActive) {
      return res.status(400).json({ message: 'WhatsApp integration is not configured or not active' });
    }

    // Get invoice details
    const invoice = await prisma.invoice.findFirst({
      where: {
        id: invoiceId,
        tenantId,
      },
      include: {
        customer: true,
        items: true,
        tenant: true,
      },
    });

    if (!invoice) {
      return res.status(404).json({ message: 'Invoice not found or does not belong to your tenant' });
    }

    if (!invoice.customer.phone) {
      return res.status(400).json({ message: 'Customer does not have a phone number' });
    }

    // Format invoice details for WhatsApp
    const invoiceDetails = `
*Invoice #${invoice.invoiceNumber}*
From: ${invoice.tenant.businessName}
To: ${invoice.customer.name}
Amount: $${invoice.totalAmount.toFixed(2)}
Due Date: ${new Date(invoice.dueDate).toLocaleDateString()}

${message || 'Please find your invoice attached. Thank you for your business!'}
`;

    // Generate PDF invoice
    let pdfPath = null;
    try {
      pdfPath = await generateInvoicePDF(invoice);

      // Send WhatsApp message with PDF attachment using Twilio
      const twilioMessage = await twilioClient.messages.create({
        from: `whatsapp:${config.twilio.phoneNumber}`,
        to: `whatsapp:${invoice.customer.phone}`,
        body: invoiceDetails,
        mediaUrl: pdfPath ? [`${config.baseUrl}/temp/${pdfPath.split('/').pop()}`] : undefined,
      });

      // Clean up the PDF file after sending
      if (pdfPath) {
        await fs.unlink(pdfPath).catch(err => console.error('Error deleting PDF file:', err));
      }

      return twilioMessage;
    } catch (error) {
      console.error('Error generating or sending PDF:', error);

      // If PDF generation fails, still send the message without attachment
      const twilioMessage = await twilioClient.messages.create({
        from: `whatsapp:${config.twilio.phoneNumber}`,
        to: `whatsapp:${invoice.customer.phone}`,
        body: invoiceDetails,
      });

      return twilioMessage;
    }

    // Update invoice status to SENT if it's in DRAFT
    if (invoice.status === 'DRAFT') {
      await prisma.invoice.update({
        where: { id: invoiceId },
        data: { status: 'SENT' },
      });
    }

    // Create a record of the reminder
    await prisma.invoiceReminder.create({
      data: {
        invoiceId,
        reminderDate: new Date(),
        status: 'SENT',
        message: invoiceDetails,
      },
    });

    return res.status(200).json({
      message: 'Invoice sent via WhatsApp successfully',
      messageSid: 'mock-message-sid', // In a real implementation, this would be twilioMessage.sid
    });
  } catch (error: any) {
    console.error('Error sending invoice via WhatsApp:', error);
    return res.status(500).json({ message: 'Failed to send invoice via WhatsApp', error: error.message });
  }
};

// Send payment reminder via WhatsApp
export const sendPaymentReminder = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId } = req.user;
    const { invoiceId, message } = req.body;

    // Check if WhatsApp integration is configured for the tenant
    const whatsappSettings = await prisma.whatsappSetting.findUnique({
      where: { tenantId },
    });

    if (!whatsappSettings || !whatsappSettings.isActive) {
      return res.status(400).json({ message: 'WhatsApp integration is not configured or not active' });
    }

    // Get invoice details
    const invoice = await prisma.invoice.findFirst({
      where: {
        id: invoiceId,
        tenantId,
      },
      include: {
        customer: true,
        tenant: true,
      },
    });

    if (!invoice) {
      return res.status(404).json({ message: 'Invoice not found or does not belong to your tenant' });
    }

    if (!invoice.customer.phone) {
      return res.status(400).json({ message: 'Customer does not have a phone number' });
    }

    // Format payment reminder message
    const reminderMessage = `
*Payment Reminder: Invoice #${invoice.invoiceNumber}*
From: ${invoice.tenant.businessName}
Amount Due: $${invoice.totalAmount.toFixed(2)}
Due Date: ${new Date(invoice.dueDate).toLocaleDateString()}

${message || 'This is a friendly reminder that your payment is due. Please make your payment at your earliest convenience.'}
`;

    // Send WhatsApp message using Twilio
    const twilioMessage = await twilioClient.messages.create({
      from: `whatsapp:${config.twilio.phoneNumber}`,
      to: `whatsapp:${invoice.customer.phone}`,
      body: reminderMessage,
    });

    // Create a record of the reminder
    await prisma.invoiceReminder.create({
      data: {
        invoiceId,
        reminderDate: new Date(),
        status: 'SENT',
        message: reminderMessage,
      },
    });

    return res.status(200).json({
      message: 'Payment reminder sent via WhatsApp successfully',
      messageSid: twilioMessage.sid,
    });
  } catch (error: any) {
    console.error('Error sending payment reminder via WhatsApp:', error);
    return res.status(500).json({ message: 'Failed to send payment reminder', error: error.message });
  }
};

// Configure WhatsApp settings for a tenant
export const configureWhatsApp = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId } = req.user;
    const { provider, accountSid, authToken, phoneNumberId, isActive } = req.body;

    // Check if WhatsApp settings already exist for the tenant
    const existingSettings = await prisma.whatsappSetting.findUnique({
      where: { tenantId },
    });

    let whatsappSettings;

    if (existingSettings) {
      // Update existing settings
      whatsappSettings = await prisma.whatsappSetting.update({
        where: { tenantId },
        data: {
          provider,
          accountSid,
          authToken,
          phoneNumberId,
          isActive,
        },
      });
    } else {
      // Create new settings
      whatsappSettings = await prisma.whatsappSetting.create({
        data: {
          tenantId,
          provider,
          accountSid,
          authToken,
          phoneNumberId,
          isActive,
        },
      });
    }

    return res.status(200).json({
      message: 'WhatsApp settings configured successfully',
      settings: {
        id: whatsappSettings.id,
        provider: whatsappSettings.provider,
        isActive: whatsappSettings.isActive,
      },
    });
  } catch (error: any) {
    console.error('Error configuring WhatsApp settings:', error);
    return res.status(500).json({ message: 'Failed to configure WhatsApp settings', error: error.message });
  }
};
