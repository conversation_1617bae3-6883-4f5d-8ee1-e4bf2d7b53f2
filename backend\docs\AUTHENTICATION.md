# Authentication System

This document describes the authentication system used in the Invoix ERP platform.

## Overview

The authentication system uses a JWT-based approach with both access tokens and refresh tokens:

- **Access Tokens**: Short-lived tokens (1 hour) used for API authentication
- **Refresh Tokens**: Long-lived tokens (7 days) used to obtain new access tokens

## Token Flow

1. **Login/Registration**: User receives both access token and refresh token
2. **API Requests**: Access token is sent in the Authorization header
3. **Token Expiration**: When access token expires, client uses refresh token to get a new access token
4. **Logout**: Refresh token is invalidated in the database

## API Endpoints

### Public Endpoints

- `POST /api/auth/login`: Authenticate user and get tokens
- `POST /api/auth/register`: Register new user and get tokens
- `POST /api/auth/refresh-token`: Get new access token using refresh token

### Protected Endpoints

- `GET /api/auth/profile`: Get current user profile
- `PUT /api/auth/password`: Update user password
- `POST /api/auth/logout`: Logout user and invalidate refresh token

## Request/Response Examples

### Login

**Request:**
```json
POST /api/auth/login
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "message": "Login successful",
  "user": {
    "id": "user-id",
    "name": "User Name",
    "email": "<EMAIL>",
    "role": "USER",
    "tenant": {
      "id": "tenant-id",
      "name": "Tenant Name",
      "businessName": "Business Name"
    }
  },
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### Refresh Token

**Request:**
```json
POST /api/auth/refresh-token
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response:**
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

## Client Implementation

### Storing Tokens

- Store access token in memory or short-lived storage
- Store refresh token in secure HTTP-only cookie or secure storage

### Token Refresh Strategy

1. When an API call returns a 401 with `code: "TOKEN_EXPIRED"`:
   - Use the refresh token to get a new access token
   - Retry the original request with the new access token

2. If refresh token is invalid or expired:
   - Redirect user to login page

## Security Considerations

- Access tokens are short-lived to minimize risk if compromised
- Refresh tokens are stored in the database and can be revoked
- Passwords are hashed using bcrypt
- JWT secrets should be strong and environment-specific

## Setup

To generate secure JWT secrets, run:

```bash
node scripts/generate-jwt-secrets.js
```

This will create or update the `.env` file with secure random secrets for both access and refresh tokens.
