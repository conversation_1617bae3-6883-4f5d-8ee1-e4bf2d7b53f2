import express from 'express';
import { webhookController } from '../controllers/webhook.controller';

const router = express.Router();

// Stripe webhook
router.post('/stripe', express.raw({ type: 'application/json' }), webhookController.handleStripeWebhook);

// PayPal webhook
router.post('/paypal', express.json(), webhookController.handlePayPalWebhook);

// BillPlz webhook
router.post('/billplz', express.json(), webhookController.handleBillPlzWebhook);

export default router;
