'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { apiClient } from '@/lib/api/client';

export default function TwoFactorAuthPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [verificationCode, setVerificationCode] = useState('');
  const [useRecoveryCode, setUseRecoveryCode] = useState(false);
  const [recoveryCode, setRecoveryCode] = useState('');
  
  // Get the session token from the URL
  const sessionToken = searchParams.get('token');
  
  useEffect(() => {
    // Redirect if no session token is provided
    if (!sessionToken) {
      router.push('/login');
    }
  }, [sessionToken, router]);

  const handleVerify = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      if (useRecoveryCode) {
        // In a real app, this would call the API to verify the recovery code
        // await apiClient.post('/auth/2fa/recovery', { token: sessionToken, recoveryCode });
        
        // Mock verification
        if (recoveryCode.length > 10) {
          // Simulate successful verification
          router.push('/dashboard');
        } else {
          throw new Error('Invalid recovery code');
        }
      } else {
        // In a real app, this would call the API to verify the 2FA code
        // await apiClient.post('/auth/2fa/verify', { token: sessionToken, code: verificationCode });
        
        // Mock verification
        if (verificationCode.length === 6 && /^\d+$/.test(verificationCode)) {
          // Simulate successful verification
          router.push('/dashboard');
        } else {
          throw new Error('Invalid verification code');
        }
      }
    } catch (err: any) {
      console.error('2FA verification error:', err);
      setError(err.message || 'Failed to verify code. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <div className="mb-4 text-center">
            <Link href="/" className="inline-block">
              <h1 className="text-3xl font-bold text-indigo-600">Invoix</h1>
            </Link>
          </div>
          <CardTitle className="text-2xl font-bold text-center">
            Two-Factor Authentication
          </CardTitle>
          <CardDescription className="text-center">
            {useRecoveryCode 
              ? 'Enter a recovery code to access your account' 
              : 'Enter the verification code from your authenticator app'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleVerify} className="space-y-4">
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
            
            {useRecoveryCode ? (
              <Input
                label="Recovery Code"
                placeholder="Enter recovery code"
                value={recoveryCode}
                onChange={(e) => setRecoveryCode(e.target.value)}
                required
              />
            ) : (
              <Input
                label="Verification Code"
                placeholder="Enter 6-digit code"
                value={verificationCode}
                onChange={(e) => setVerificationCode(e.target.value)}
                maxLength={6}
                required
              />
            )}
            
            <Button
              type="submit"
              className="w-full"
              disabled={isLoading}
            >
              {isLoading ? 'Verifying...' : 'Verify'}
            </Button>
          </form>
        </CardContent>
        <CardFooter className="flex justify-center">
          <Button
            variant="link"
            onClick={() => setUseRecoveryCode(!useRecoveryCode)}
            className="text-sm"
          >
            {useRecoveryCode 
              ? 'Use authenticator app instead' 
              : 'Use a recovery code instead'}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
