import { NextRequest, NextResponse } from 'next/server';

// Mock configuration for development
let mockConfig = {
  id: '1',
  apiBaseUrl: 'https://sandbox.myinvois.hasil.gov.my/einvoicing',
  certificatePath: '/path/to/certificate.p12',
  isActive: false,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
};

export async function GET() {
  try {
    // In production, this would call your backend API
    // const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/lhdn-config`);
    // const data = await response.json();
    
    // For development, use mock data
    return NextResponse.json(mockConfig);
  } catch (error) {
    console.error('Error getting LHDN configuration:', error);
    return NextResponse.json(
      { message: 'Failed to get LHDN configuration' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const data = await request.json();
    
    // In production, this would call your backend API
    // const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/lhdn-config`, {
    //   method: 'PUT',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(data),
    // });
    // const responseData = await response.json();
    
    // For development, update mock data
    mockConfig = {
      ...mockConfig,
      ...data,
      updatedAt: new Date().toISOString(),
    };
    
    return NextResponse.json({
      message: 'LHDN configuration updated successfully',
      config: {
        ...mockConfig,
        certificatePassword: undefined, // Don't return the password
      },
    });
  } catch (error) {
    console.error('Error updating LHDN configuration:', error);
    return NextResponse.json(
      { message: 'Failed to update LHDN configuration' },
      { status: 500 }
    );
  }
}
