import Link from 'next/link';
import ForgotPasswordForm from '@/components/forms/ForgotPasswordForm';
import Image from 'next/image';

export default function ForgotPasswordPage() {
  return (
    <div className="min-h-screen flex flex-col md:flex-row">
      {/* Left side - Form */}
      <div className="flex-1 flex flex-col justify-center items-center p-8 md:p-12">
        <div className="w-full max-w-md">
          <div className="mb-8 text-center">
            <Link href="/" className="inline-block">
              <h1 className="text-3xl font-bold text-indigo-600">Invoix</h1>
            </Link>
          </div>
          <ForgotPasswordForm />
        </div>
      </div>
      
      {/* Right side - Image and info */}
      <div className="hidden md:flex flex-1 bg-indigo-600 text-white p-12 flex-col justify-center">
        <div className="max-w-md mx-auto">
          <h2 className="text-3xl font-bold mb-6">Recover your account</h2>
          <p className="text-lg mb-8">
            We'll send you a link to reset your password and get you back to managing your business.
          </p>
          <div className="bg-indigo-500/30 p-6 rounded-lg">
            <h3 className="text-xl font-semibold mb-4">Need help?</h3>
            <p className="mb-4">
              If you're having trouble accessing your account, our support team is here to help.
            </p>
            <Link 
              href="mailto:<EMAIL>" 
              className="inline-flex items-center text-white hover:text-indigo-100"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              Contact Support
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
