/**
 * Get all templates
 * @returns Promise with the templates
 */
export const getTemplates = async (): Promise<{ templates: any[] }> => {
  const response = await fetch('/api/templates');

  if (!response.ok) {
    const data = await response.json();
    throw new Error(data.error || 'Failed to fetch templates');
  }

  return response.json();
};

/**
 * Get a template by ID
 * @param id The template ID
 * @returns Promise with the template
 */
export const getTemplateById = async (id: string): Promise<{ template: any }> => {
  const response = await fetch(`/api/templates/${id}`);

  if (!response.ok) {
    const data = await response.json();
    throw new Error(data.error || 'Failed to fetch template');
  }

  return response.json();
};

/**
 * Create a new template
 * @param name The template name
 * @param template The template content
 * @returns Promise with the created template
 */
export const createTemplate = async (name: string, template: string): Promise<{ template: any }> => {
  const response = await fetch('/api/templates', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ name, template }),
  });

  if (!response.ok) {
    const data = await response.json();
    throw new Error(data.error || 'Failed to create template');
  }

  return response.json();
};

/**
 * Update a template
 * @param id The template ID
 * @param name The template name
 * @param template The template content
 * @returns Promise with the updated template
 */
export const updateTemplate = async (id: string, name: string, template: string): Promise<{ template: any }> => {
  const response = await fetch(`/api/templates/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ name, template }),
  });

  if (!response.ok) {
    const data = await response.json();
    throw new Error(data.error || 'Failed to update template');
  }

  return response.json();
};

/**
 * Delete a template
 * @param id The template ID
 * @returns Promise with the delete result
 */
export const deleteTemplate = async (id: string): Promise<{ message: string }> => {
  const response = await fetch(`/api/templates/${id}`, {
    method: 'DELETE',
  });

  if (!response.ok) {
    const data = await response.json();
    throw new Error(data.error || 'Failed to delete template');
  }

  return response.json();
};

/**
 * Generate a PDF with a specific template
 * @param invoiceId The invoice ID
 * @param templateId The template ID
 * @returns The URL for the PDF
 */
export const generatePDFWithTemplate = (invoiceId: string, templateId: string): string => {
  return `/api/pdf/invoices/${invoiceId}/view?templateId=${templateId}`;
};

/**
 * Download a PDF with a specific template
 * @param invoiceId The invoice ID
 * @param templateId The template ID
 */
export const downloadPDFWithTemplate = (invoiceId: string, templateId: string): void => {
  // Create a hidden anchor element
  const link = document.createElement('a');
  link.href = `/api/pdf/invoices/${invoiceId}/download?templateId=${templateId}`;
  link.target = '_blank';
  link.download = `invoice-${invoiceId}.pdf`;

  // Append to the document and trigger a click
  document.body.appendChild(link);
  link.click();

  // Clean up
  document.body.removeChild(link);
};

/**
 * Generate multiple PDFs in batch
 * @param invoiceIds The invoice IDs
 * @param templateId The template ID (optional)
 * @returns Promise with the batch result
 */
export const generateBatchPDFs = async (invoiceIds: string[], templateId?: string): Promise<any> => {
  const response = await fetch('/api/pdf/invoices/batch', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ invoiceIds, templateId }),
  });

  if (!response.ok) {
    const data = await response.json();
    throw new Error(data.error || 'Failed to generate batch PDFs');
  }

  return response.json();
};
