import { NextRequest, NextResponse } from 'next/server';

// Mock data for development - in production, this would call your backend API
const mockTaxpayers = [
  { tin: '*********', isValid: true, taxpayerName: 'ABC Company Sdn Bhd', taxpayerType: 'COMPANY' },
  { tin: '*********', isValid: true, taxpayerName: 'XYZ Enterprise Sdn Bhd', taxpayerType: 'COMPANY' },
  { tin: '*********', isValid: true, taxpayerName: '<PERSON>', taxpayerType: 'INDIVIDUAL' },
];

const mockInvoices = [
  {
    id: 'inv-001',
    invoiceNumber: 'INV-2023-001',
    isValidated: true,
    validationId: 'LHDN-1683561234-123'
  },
  {
    id: 'inv-002',
    invoiceNumber: 'INV-2023-002',
    isValidated: false,
    validationId: null
  },
  {
    id: 'inv-003',
    invoiceNumber: 'INV-2023-003',
    isValidated: true,
    validationId: 'LHDN-1683645678-456'
  },
];

export async function GET(
  request: NextRequest,
  { params }: { params: { route: string[] } }
) {
  const [action, id] = params.route;

  // Validate Business Registration Number
  if (action === 'validate-business' && id) {
    try {
      // In production, this would call your backend API
      // const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/lhdn/validate-business/${id}`);
      // const data = await response.json();

      // For development, use mock data
      const mockBusiness = mockTaxpayers.find(t => t.tin === id);

      if (mockBusiness) {
        return NextResponse.json({
          regNo: id,
          isValid: true,
          businessName: mockBusiness.taxpayerName,
          businessType: mockBusiness.taxpayerType
        });
      } else {
        return NextResponse.json(
          { isValid: false, message: 'Business registration number not found' },
          { status: 404 }
        );
      }
    } catch (error) {
      console.error('Error validating business registration number:', error);
      return NextResponse.json(
        { message: 'Failed to validate business registration number' },
        { status: 500 }
      );
    }
  }

  // Check validation status
  if (action === 'status' && id) {
    try {
      // In production, this would call your backend API
      // const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/lhdn/status/${id}`);
      // const data = await response.json();

      // For development, use mock data
      const mockInvoice = mockInvoices.find(i => i.id === id);

      if (mockInvoice) {
        return NextResponse.json({
          invoiceId: mockInvoice.id,
          invoiceNumber: mockInvoice.invoiceNumber,
          isValidated: mockInvoice.isValidated,
          validationId: mockInvoice.validationId,
        });
      } else {
        return NextResponse.json(
          { message: 'Invoice not found' },
          { status: 404 }
        );
      }
    } catch (error) {
      console.error('Error checking validation status:', error);
      return NextResponse.json(
        { message: 'Failed to check validation status' },
        { status: 500 }
      );
    }
  }

  return NextResponse.json(
    { message: 'Invalid route' },
    { status: 404 }
  );
}

export async function POST(
  request: NextRequest,
  { params }: { params: { route: string[] } }
) {
  const [action, id] = params.route;

  // Validate invoice
  if (action === 'validate' && id) {
    try {
      // In production, this would call your backend API
      // const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/lhdn/validate/${id}`, {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/json',
      //   },
      // });
      // const data = await response.json();

      // For development, use mock data
      const mockInvoice = mockInvoices.find(i => i.id === id);

      if (mockInvoice) {
        // Simulate validation
        const validationId = `LHDN-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

        // Update mock data (in production, this would be handled by the backend)
        mockInvoice.isValidated = true;
        mockInvoice.validationId = validationId;

        return NextResponse.json({
          message: 'Invoice validated successfully with LHDN MyInvois',
          validationId,
        });
      } else {
        return NextResponse.json(
          { message: 'Invoice not found' },
          { status: 404 }
        );
      }
    } catch (error) {
      console.error('Error validating invoice:', error);
      return NextResponse.json(
        { message: 'Failed to validate invoice' },
        { status: 500 }
      );
    }
  }

  return NextResponse.json(
    { message: 'Invalid route' },
    { status: 404 }
  );
}
