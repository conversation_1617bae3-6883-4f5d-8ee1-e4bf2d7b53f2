'use client';

import { useState, useEffect } from 'react';
import TINValidator from '../lhdn/TINValidator';

interface CustomerTINFieldProps {
  value: string;
  onChange: (value: string, isValid: boolean, customerName?: string) => void;
  label?: string;
  required?: boolean;
  className?: string;
}

export default function CustomerTINField({
  value,
  onChange,
  label = 'Customer Tax ID (TIN)',
  required = false,
  className,
}: CustomerTINFieldProps) {
  const [showValidator, setShowValidator] = useState(false);
  const [validationStatus, setValidationStatus] = useState<{
    isValid: boolean;
    validated: boolean;
    customerName?: string;
  }>({
    isValid: false,
    validated: false,
  });

  // Handle validation result
  const handleValidation = (result: { isValid: boolean; tin: string; taxpayerName?: string }) => {
    setValidationStatus({
      isValid: result.isValid,
      validated: true,
      customerName: result.taxpayerName,
    });
    
    onChange(result.tin, result.isValid, result.taxpayerName);
  };

  return (
    <div className={className}>
      <div className="flex justify-between items-center">
        <label className="block text-sm font-medium text-gray-700">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
        <button
          type="button"
          onClick={() => setShowValidator(!showValidator)}
          className="text-xs text-indigo-600 hover:text-indigo-500"
        >
          {showValidator ? 'Hide Validator' : 'Validate TIN'}
        </button>
      </div>
      
      <div className="mt-1">
        <input
          type="text"
          value={value}
          onChange={(e) => onChange(e.target.value, validationStatus.isValid)}
          className={`block w-full rounded-md shadow-sm sm:text-sm ${
            validationStatus.validated
              ? validationStatus.isValid
                ? 'border-green-300 focus:border-green-500 focus:ring-green-500'
                : 'border-red-300 focus:border-red-500 focus:ring-red-500'
              : 'border-gray-300 focus:border-indigo-500 focus:ring-indigo-500'
          }`}
          placeholder="Enter customer's tax identification number"
        />
        
        {validationStatus.validated && (
          <div className="mt-1">
            {validationStatus.isValid ? (
              <p className="text-xs text-green-600 flex items-center">
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Valid TIN: {validationStatus.customerName}
              </p>
            ) : (
              <p className="text-xs text-red-600 flex items-center">
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
                Invalid TIN. Please check and try again.
              </p>
            )}
          </div>
        )}
      </div>
      
      {showValidator && (
        <div className="mt-3 p-3 border border-gray-200 rounded-md bg-gray-50">
          <TINValidator
            initialValue={value}
            onValidation={handleValidation}
          />
        </div>
      )}
    </div>
  );
}
