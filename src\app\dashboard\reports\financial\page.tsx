'use client';

import { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { DatePicker } from '@/components/ui/date-picker';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { Download, FileText, Printer } from 'lucide-react';
import { format, subMonths } from 'date-fns';
import reportService from '@/lib/api/report.service';
import { Input } from '@/components/ui/input';

// Mock data for demonstration
const mockProfitLossData = {
  startDate: '2023-01-01',
  endDate: '2023-12-31',
  revenue: 250000,
  cogs: 100000,
  grossProfit: 150000,
  grossProfitMargin: 60,
  expenses: {
    'Rent': 24000,
    'Salaries': 60000,
    'Utilities': 12000,
    'Marketing': 18000,
    'Other': 6000
  },
  totalExpenses: 120000,
  netProfit: 30000,
  netProfitMargin: 12
};

const mockBalanceSheetData = {
  asOfDate: '2023-12-31',
  assets: {
    currentAssets: {
      cash: 50000,
      accountsReceivable: 75000,
      inventory: 100000,
      totalCurrentAssets: 225000
    },
    fixedAssets: {
      propertyAndEquipment: 150000,
      totalFixedAssets: 150000
    },
    totalAssets: 375000
  },
  liabilities: {
    currentLiabilities: {
      accountsPayable: 45000,
      totalCurrentLiabilities: 45000
    },
    longTermLiabilities: {
      loans: 100000,
      totalLongTermLiabilities: 100000
    },
    totalLiabilities: 145000
  },
  equity: {
    ownersEquity: 200000,
    retainedEarnings: 30000,
    totalEquity: 230000
  }
};

const mockCashFlowData = {
  startDate: '2023-01-01',
  endDate: '2023-12-31',
  cashInflows: {
    paymentsReceived: 240000,
    totalInflows: 240000
  },
  cashOutflows: {
    expensesPaid: 210000,
    totalOutflows: 210000
  },
  netCashFlow: 30000
};

// Monthly data for charts
const monthlyData = [
  { month: 'Jan', revenue: 18000, expenses: 15000, profit: 3000 },
  { month: 'Feb', revenue: 20000, expenses: 16000, profit: 4000 },
  { month: 'Mar', revenue: 22000, expenses: 17000, profit: 5000 },
  { month: 'Apr', revenue: 25000, expenses: 18000, profit: 7000 },
  { month: 'May', revenue: 23000, expenses: 19000, profit: 4000 },
  { month: 'Jun', revenue: 21000, expenses: 17500, profit: 3500 },
  { month: 'Jul', revenue: 24000, expenses: 18000, profit: 6000 },
  { month: 'Aug', revenue: 26000, expenses: 19000, profit: 7000 },
  { month: 'Sep', revenue: 28000, expenses: 20000, profit: 8000 },
  { month: 'Oct', revenue: 27000, expenses: 21000, profit: 6000 },
  { month: 'Nov', revenue: 25000, expenses: 20000, profit: 5000 },
  { month: 'Dec', revenue: 21000, expenses: 19500, profit: 1500 },
];

// Expense breakdown for pie chart
const expenseData = [
  { name: 'Rent', value: 24000, color: '#8884d8' },
  { name: 'Salaries', value: 60000, color: '#83a6ed' },
  { name: 'Utilities', value: 12000, color: '#8dd1e1' },
  { name: 'Marketing', value: 18000, color: '#82ca9d' },
  { name: 'Other', value: 6000, color: '#a4de6c' },
];

export default function FinancialReportsPage() {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('profit-loss');
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [dateRange, setDateRange] = useState({
    startDate: subMonths(new Date(), 12),
    endDate: new Date(),
  });
  const [asOfDate, setAsOfDate] = useState(new Date());
  const [exportFormat, setExportFormat] = useState('pdf');

  // State for report data
  const [profitLossData, setProfitLossData] = useState(mockProfitLossData);
  const [balanceSheetData, setBalanceSheetData] = useState(mockBalanceSheetData);
  const [cashFlowData, setCashFlowData] = useState(mockCashFlowData);

  // Load report data
  useEffect(() => {
    loadReportData();
  }, [activeTab, dateRange, asOfDate]);

  const loadReportData = async () => {
    setIsLoading(true);
    try {
      // In a real app, these would be API calls
      // For now, we'll use mock data

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      switch (activeTab) {
        case 'profit-loss':
          // const plData = await reportService.getFinancialReport('profit_loss', {
          //   startDate: format(dateRange.startDate, 'yyyy-MM-dd'),
          //   endDate: format(dateRange.endDate, 'yyyy-MM-dd'),
          // });
          // setProfitLossData(plData);
          setProfitLossData(mockProfitLossData);
          break;

        case 'balance-sheet':
          // const bsData = await reportService.getFinancialReport('balance_sheet', {
          //   asOfDate: format(asOfDate, 'yyyy-MM-dd'),
          // });
          // setBalanceSheetData(bsData);
          setBalanceSheetData(mockBalanceSheetData);
          break;

        case 'cash-flow':
          // const cfData = await reportService.getFinancialReport('cash_flow', {
          //   startDate: format(dateRange.startDate, 'yyyy-MM-dd'),
          //   endDate: format(dateRange.endDate, 'yyyy-MM-dd'),
          // });
          // setCashFlowData(cfData);
          setCashFlowData(mockCashFlowData);
          break;
      }
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to load report data',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleExport = async () => {
    setIsLoading(true);
    try {
      // In a real app, this would call the API to export the report
      // const response = await reportService.exportReport(
      //   activeTab.replace('-', '_'),
      //   '',
      //   exportFormat as any,
      //   activeTab === 'balance-sheet'
      //     ? { asOfDate: format(asOfDate, 'yyyy-MM-dd') }
      //     : {
      //         startDate: format(dateRange.startDate, 'yyyy-MM-dd'),
      //         endDate: format(dateRange.endDate, 'yyyy-MM-dd'),
      //       }
      // );

      // Simulate export
      await new Promise(resolve => setTimeout(resolve, 1000));

      toast({
        title: 'Export Successful',
        description: `Report has been exported as ${exportFormat.toUpperCase()}`,
        variant: 'default',
      });
    } catch (error: any) {
      toast({
        title: 'Export Failed',
        description: error.message || 'Failed to export report',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-MY', {
      style: 'currency',
      currency: 'MYR',
      minimumFractionDigits: 2,
    }).format(value);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between bg-white p-6 rounded-lg shadow-sm mb-6">
        <div>
          <h1 className="text-2xl font-bold">Financial Reports</h1>
          <p className="text-text-secondary">View and analyze your financial performance</p>
        </div>
        <div className="mt-4 md:mt-0 flex flex-col sm:flex-row gap-2">
          <Select value={exportFormat} onValueChange={setExportFormat}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="Export As" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="pdf">PDF</SelectItem>
              <SelectItem value="xlsx">Excel</SelectItem>
              <SelectItem value="csv">CSV</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={handleExport} disabled={isLoading}>
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" onClick={() => window.print()} disabled={isLoading}>
            <Printer className="w-4 h-4 mr-2" />
            Print
          </Button>
        </div>
      </div>

      <Tabs defaultValue="profit-loss" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-6 bg-white p-1 rounded-lg border">
          <TabsTrigger value="profit-loss" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">Profit & Loss</TabsTrigger>
          <TabsTrigger value="balance-sheet" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">Balance Sheet</TabsTrigger>
          <TabsTrigger value="cash-flow" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">Cash Flow</TabsTrigger>
        </TabsList>

        <div className="mb-6">
          {activeTab !== 'balance-sheet' ? (
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <label className="block text-sm font-medium mb-1">Start Date</label>
                <DatePicker
                  date={dateRange.startDate}
                  setDate={(date) => setDateRange(prev => ({ ...prev, startDate: date || prev.startDate }))}
                />
              </div>
              <div className="flex-1">
                <label className="block text-sm font-medium mb-1">End Date</label>
                <DatePicker
                  date={dateRange.endDate}
                  setDate={(date) => setDateRange(prev => ({ ...prev, endDate: date || prev.endDate }))}
                />
              </div>
            </div>
          ) : (
            <div className="w-full sm:w-1/2">
              <label className="block text-sm font-medium mb-1">As of Date</label>
              <DatePicker
                date={asOfDate}
                setDate={(date) => setAsOfDate(date || new Date())}
              />
            </div>
          )}
        </div>

        <TabsContent value="profit-loss">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <Card className="border-none shadow-md">
              <CardContent className="p-6">
                <div className="space-y-2">
                  <p className="text-sm font-medium text-text-secondary">Revenue</p>
                  <h3 className="text-2xl font-bold">{formatCurrency(profitLossData.revenue)}</h3>
                </div>
              </CardContent>
            </Card>

            <Card className="border-none shadow-md">
              <CardContent className="p-6">
                <div className="space-y-2">
                  <p className="text-sm font-medium text-text-secondary">Expenses</p>
                  <h3 className="text-2xl font-bold">{formatCurrency(profitLossData.totalExpenses)}</h3>
                </div>
              </CardContent>
            </Card>

            <Card className="border-none shadow-md">
              <CardContent className="p-6">
                <div className="space-y-2">
                  <p className="text-sm font-medium text-text-secondary">Net Profit</p>
                  <h3 className="text-2xl font-bold">{formatCurrency(profitLossData.netProfit)}</h3>
                  <p className="text-sm text-text-secondary">
                    Margin: {profitLossData.netProfitMargin.toFixed(2)}%
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="border-none shadow-md">
              <CardHeader className="pb-2 border-b">
                <CardTitle className="text-lg font-bold text-text-primary">Monthly Revenue & Expenses</CardTitle>
                <CardDescription className="text-text-secondary">
                  Comparison of revenue and expenses over time
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={monthlyData}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip formatter={(value) => formatCurrency(value as number)} />
                      <Legend />
                      <Bar dataKey="revenue" name="Revenue" fill="#8884d8" />
                      <Bar dataKey="expenses" name="Expenses" fill="#82ca9d" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card className="border-none shadow-md">
              <CardHeader className="pb-2 border-b">
                <CardTitle className="text-lg font-bold text-text-primary">Expense Breakdown</CardTitle>
                <CardDescription className="text-text-secondary">
                  Distribution of expenses by category
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={expenseData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {expenseData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => formatCurrency(value as number)} />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card className="mt-6">
            <CardHeader className="pb-2 border-b">
              <CardTitle className="text-lg font-bold text-text-primary">Profit & Loss Statement</CardTitle>
              <CardDescription className="text-text-secondary">
                For the period {format(dateRange.startDate, 'MMMM d, yyyy')} to {format(dateRange.endDate, 'MMMM d, yyyy')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-2">Revenue</h3>
                  <div className="flex justify-between py-2 border-b">
                    <span>Total Revenue</span>
                    <span className="font-medium">{formatCurrency(profitLossData.revenue)}</span>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-2">Cost of Goods Sold</h3>
                  <div className="flex justify-between py-2 border-b">
                    <span>Total COGS</span>
                    <span className="font-medium">{formatCurrency(profitLossData.cogs)}</span>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-2">Gross Profit</h3>
                  <div className="flex justify-between py-2 border-b">
                    <span>Gross Profit</span>
                    <span className="font-medium">{formatCurrency(profitLossData.grossProfit)}</span>
                  </div>
                  <div className="flex justify-between py-2 border-b">
                    <span>Gross Profit Margin</span>
                    <span className="font-medium">{profitLossData.grossProfitMargin.toFixed(2)}%</span>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-2">Expenses</h3>
                  {Object.entries(profitLossData.expenses).map(([category, amount]) => (
                    <div key={category} className="flex justify-between py-2 border-b">
                      <span>{category}</span>
                      <span>{formatCurrency(amount as number)}</span>
                    </div>
                  ))}
                  <div className="flex justify-between py-2 border-b font-medium">
                    <span>Total Expenses</span>
                    <span>{formatCurrency(profitLossData.totalExpenses)}</span>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-2">Net Profit</h3>
                  <div className="flex justify-between py-2 border-b">
                    <span>Net Profit</span>
                    <span className="font-medium">{formatCurrency(profitLossData.netProfit)}</span>
                  </div>
                  <div className="flex justify-between py-2 border-b">
                    <span>Net Profit Margin</span>
                    <span className="font-medium">{profitLossData.netProfitMargin.toFixed(2)}%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="balance-sheet">
          {/* Balance Sheet content */}
          <Card className="border-none shadow-md">
            <CardHeader className="pb-2 border-b">
              <CardTitle className="text-lg font-bold text-text-primary">Balance Sheet</CardTitle>
              <CardDescription className="text-text-secondary">
                As of {format(asOfDate, 'MMMM d, yyyy')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-2">Assets</h3>

                  <h4 className="text-md font-medium mt-4 mb-2">Current Assets</h4>
                  <div className="flex justify-between py-2 border-b">
                    <span>Cash</span>
                    <span>{formatCurrency(balanceSheetData.assets.currentAssets.cash)}</span>
                  </div>
                  <div className="flex justify-between py-2 border-b">
                    <span>Accounts Receivable</span>
                    <span>{formatCurrency(balanceSheetData.assets.currentAssets.accountsReceivable)}</span>
                  </div>
                  <div className="flex justify-between py-2 border-b">
                    <span>Inventory</span>
                    <span>{formatCurrency(balanceSheetData.assets.currentAssets.inventory)}</span>
                  </div>
                  <div className="flex justify-between py-2 border-b font-medium">
                    <span>Total Current Assets</span>
                    <span>{formatCurrency(balanceSheetData.assets.currentAssets.totalCurrentAssets)}</span>
                  </div>

                  <h4 className="text-md font-medium mt-4 mb-2">Fixed Assets</h4>
                  <div className="flex justify-between py-2 border-b">
                    <span>Property and Equipment</span>
                    <span>{formatCurrency(balanceSheetData.assets.fixedAssets.propertyAndEquipment)}</span>
                  </div>
                  <div className="flex justify-between py-2 border-b font-medium">
                    <span>Total Fixed Assets</span>
                    <span>{formatCurrency(balanceSheetData.assets.fixedAssets.totalFixedAssets)}</span>
                  </div>

                  <div className="flex justify-between py-2 border-b font-semibold">
                    <span>Total Assets</span>
                    <span>{formatCurrency(balanceSheetData.assets.totalAssets)}</span>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-2">Liabilities</h3>

                  <h4 className="text-md font-medium mt-4 mb-2">Current Liabilities</h4>
                  <div className="flex justify-between py-2 border-b">
                    <span>Accounts Payable</span>
                    <span>{formatCurrency(balanceSheetData.liabilities.currentLiabilities.accountsPayable)}</span>
                  </div>
                  <div className="flex justify-between py-2 border-b font-medium">
                    <span>Total Current Liabilities</span>
                    <span>{formatCurrency(balanceSheetData.liabilities.currentLiabilities.totalCurrentLiabilities)}</span>
                  </div>

                  <h4 className="text-md font-medium mt-4 mb-2">Long-term Liabilities</h4>
                  <div className="flex justify-between py-2 border-b">
                    <span>Loans</span>
                    <span>{formatCurrency(balanceSheetData.liabilities.longTermLiabilities.loans)}</span>
                  </div>
                  <div className="flex justify-between py-2 border-b font-medium">
                    <span>Total Long-term Liabilities</span>
                    <span>{formatCurrency(balanceSheetData.liabilities.longTermLiabilities.totalLongTermLiabilities)}</span>
                  </div>

                  <div className="flex justify-between py-2 border-b font-semibold">
                    <span>Total Liabilities</span>
                    <span>{formatCurrency(balanceSheetData.liabilities.totalLiabilities)}</span>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-2">Equity</h3>
                  <div className="flex justify-between py-2 border-b">
                    <span>Owner's Equity</span>
                    <span>{formatCurrency(balanceSheetData.equity.ownersEquity)}</span>
                  </div>
                  <div className="flex justify-between py-2 border-b">
                    <span>Retained Earnings</span>
                    <span>{formatCurrency(balanceSheetData.equity.retainedEarnings)}</span>
                  </div>
                  <div className="flex justify-between py-2 border-b font-semibold">
                    <span>Total Equity</span>
                    <span>{formatCurrency(balanceSheetData.equity.totalEquity)}</span>
                  </div>
                </div>

                <div>
                  <div className="flex justify-between py-2 border-b font-semibold">
                    <span>Total Liabilities and Equity</span>
                    <span>{formatCurrency(balanceSheetData.liabilities.totalLiabilities + balanceSheetData.equity.totalEquity)}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="cash-flow">
          {/* Cash Flow content */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <Card className="border-none shadow-md">
              <CardContent className="p-6">
                <div className="space-y-2">
                  <p className="text-sm font-medium text-text-secondary">Cash Inflows</p>
                  <h3 className="text-2xl font-bold">{formatCurrency(cashFlowData.cashInflows.totalInflows)}</h3>
                </div>
              </CardContent>
            </Card>

            <Card className="border-none shadow-md">
              <CardContent className="p-6">
                <div className="space-y-2">
                  <p className="text-sm font-medium text-text-secondary">Cash Outflows</p>
                  <h3 className="text-2xl font-bold">{formatCurrency(cashFlowData.cashOutflows.totalOutflows)}</h3>
                </div>
              </CardContent>
            </Card>

            <Card className="border-none shadow-md">
              <CardContent className="p-6">
                <div className="space-y-2">
                  <p className="text-sm font-medium text-text-secondary">Net Cash Flow</p>
                  <h3 className="text-2xl font-bold">{formatCurrency(cashFlowData.netCashFlow)}</h3>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card className="mb-6">
            <CardHeader className="pb-2 border-b">
              <CardTitle className="text-lg font-bold text-text-primary">Monthly Cash Flow</CardTitle>
              <CardDescription className="text-text-secondary">
                Net cash flow over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={monthlyData}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value) => formatCurrency(value as number)} />
                    <Legend />
                    <Line type="monotone" dataKey="profit" name="Net Cash Flow" stroke="#8884d8" activeDot={{ r: 8 }} />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          <Card className="border-none shadow-md">
            <CardHeader className="pb-2 border-b">
              <CardTitle className="text-lg font-bold text-text-primary">Cash Flow Statement</CardTitle>
              <CardDescription className="text-text-secondary">
                For the period {format(dateRange.startDate, 'MMMM d, yyyy')} to {format(dateRange.endDate, 'MMMM d, yyyy')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-2">Cash Inflows</h3>
                  <div className="flex justify-between py-2 border-b">
                    <span>Payments Received</span>
                    <span>{formatCurrency(cashFlowData.cashInflows.paymentsReceived)}</span>
                  </div>
                  <div className="flex justify-between py-2 border-b font-medium">
                    <span>Total Cash Inflows</span>
                    <span>{formatCurrency(cashFlowData.cashInflows.totalInflows)}</span>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-2">Cash Outflows</h3>
                  <div className="flex justify-between py-2 border-b">
                    <span>Expenses Paid</span>
                    <span>{formatCurrency(cashFlowData.cashOutflows.expensesPaid)}</span>
                  </div>
                  <div className="flex justify-between py-2 border-b font-medium">
                    <span>Total Cash Outflows</span>
                    <span>{formatCurrency(cashFlowData.cashOutflows.totalOutflows)}</span>
                  </div>
                </div>

                <div>
                  <div className="flex justify-between py-2 border-b font-semibold">
                    <span>Net Cash Flow</span>
                    <span>{formatCurrency(cashFlowData.netCashFlow)}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
