'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { UserPlus } from 'lucide-react';

// Mock data for employees
const mockEmployees = [
  {
    id: '1',
    employeeId: 'EMP001',
    name: '<PERSON>',
    position: 'Software Engineer',
    department: 'Engineering',
    email: '<EMAIL>',
    phone: '+60123456789',
    joinDate: '2022-01-15',
    status: 'Active',
  },
  {
    id: '2',
    employeeId: 'EMP002',
    name: '<PERSON>',
    position: 'HR Manager',
    department: 'Human Resources',
    email: '<EMAIL>',
    phone: '+***********',
    joinDate: '2021-06-10',
    status: 'Active',
  },
  {
    id: '3',
    employeeId: 'EMP003',
    name: '<PERSON>',
    position: 'Sales Executive',
    department: 'Sales',
    email: 'micha<PERSON>.joh<PERSON>@example.com',
    phone: '+***********',
    joinDate: '2022-03-22',
    status: 'On Leave',
  },
  {
    id: '4',
    employeeId: 'EMP004',
    name: 'Sarah Williams',
    position: 'Accountant',
    department: 'Finance',
    email: '<EMAIL>',
    phone: '+***********',
    joinDate: '2021-11-05',
    status: 'Active',
  },
  {
    id: '5',
    employeeId: 'EMP005',
    name: 'David Brown',
    position: 'Marketing Specialist',
    department: 'Marketing',
    email: '<EMAIL>',
    phone: '+***********',
    joinDate: '2022-02-18',
    status: 'Inactive',
  },
];

// Mock data for attendance
const mockAttendance = [
  {
    id: '1',
    employeeId: 'EMP001',
    employeeName: 'John Doe',
    date: '2023-06-20',
    checkIn: '09:00:00',
    checkOut: '18:00:00',
    status: 'Present',
    workHours: 9,
  },
  {
    id: '2',
    employeeId: 'EMP002',
    employeeName: 'Jane Smith',
    date: '2023-06-20',
    checkIn: '08:45:00',
    checkOut: '17:30:00',
    status: 'Present',
    workHours: 8.75,
  },
  {
    id: '3',
    employeeId: 'EMP003',
    employeeName: 'Michael Johnson',
    date: '2023-06-20',
    checkIn: null,
    checkOut: null,
    status: 'Absent',
    workHours: 0,
  },
  {
    id: '4',
    employeeId: 'EMP004',
    employeeName: 'Sarah Williams',
    date: '2023-06-20',
    checkIn: '09:15:00',
    checkOut: '18:15:00',
    status: 'Present',
    workHours: 9,
  },
  {
    id: '5',
    employeeId: 'EMP005',
    employeeName: 'David Brown',
    date: '2023-06-20',
    checkIn: null,
    checkOut: null,
    status: 'On Leave',
    workHours: 0,
  },
];

// Mock data for leave requests
const mockLeaveRequests = [
  {
    id: '1',
    employeeId: 'EMP003',
    employeeName: 'Michael Johnson',
    leaveType: 'Annual Leave',
    startDate: '2023-06-20',
    endDate: '2023-06-22',
    days: 3,
    reason: 'Family vacation',
    status: 'Approved',
  },
  {
    id: '2',
    employeeId: 'EMP001',
    employeeName: 'John Doe',
    leaveType: 'Sick Leave',
    startDate: '2023-06-25',
    endDate: '2023-06-25',
    days: 1,
    reason: 'Doctor appointment',
    status: 'Pending',
  },
  {
    id: '3',
    employeeId: 'EMP005',
    employeeName: 'David Brown',
    leaveType: 'Annual Leave',
    startDate: '2023-06-19',
    endDate: '2023-06-23',
    days: 5,
    reason: 'Personal matters',
    status: 'Approved',
  },
];

// Mock data for payroll
const mockPayroll = [
  {
    id: '1',
    month: 'June 2023',
    totalEmployees: 5,
    totalSalary: 25000,
    status: 'Pending',
    processedDate: null,
  },
  {
    id: '2',
    month: 'May 2023',
    totalEmployees: 5,
    totalSalary: 25000,
    status: 'Processed',
    processedDate: '2023-05-28',
  },
  {
    id: '3',
    month: 'April 2023',
    totalEmployees: 5,
    totalSalary: 24500,
    status: 'Processed',
    processedDate: '2023-04-28',
  },
];

// Column definitions for tables
const employeeColumns: ColumnDef<any>[] = [
  {
    accessorKey: 'employeeId',
    header: 'Employee ID',
  },
  {
    accessorKey: 'name',
    header: 'Name',
  },
  {
    accessorKey: 'position',
    header: 'Position',
  },
  {
    accessorKey: 'department',
    header: 'Department',
  },
  {
    accessorKey: 'email',
    header: 'Email',
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.getValue('status');
      return status === 'Active' ? (
        <Badge className="bg-green-500">Active</Badge>
      ) : status === 'On Leave' ? (
        <Badge className="bg-yellow-500">On Leave</Badge>
      ) : (
        <Badge variant="outline">Inactive</Badge>
      );
    },
  },
  {
    id: 'actions',
    cell: ({ row }) => {
      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm">
              Actions
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem>View Details</DropdownMenuItem>
            <DropdownMenuItem>Edit</DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="text-red-600">Deactivate</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];

const attendanceColumns: ColumnDef<any>[] = [
  {
    accessorKey: 'employeeName',
    header: 'Employee',
  },
  {
    accessorKey: 'date',
    header: 'Date',
    cell: ({ row }) => {
      return new Date(row.getValue('date')).toLocaleDateString();
    },
  },
  {
    accessorKey: 'checkIn',
    header: 'Check In',
    cell: ({ row }) => {
      return row.getValue('checkIn') || '-';
    },
  },
  {
    accessorKey: 'checkOut',
    header: 'Check Out',
    cell: ({ row }) => {
      return row.getValue('checkOut') || '-';
    },
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.getValue('status');
      return status === 'Present' ? (
        <Badge className="bg-green-500">Present</Badge>
      ) : status === 'Absent' ? (
        <Badge variant="destructive">Absent</Badge>
      ) : (
        <Badge className="bg-yellow-500">On Leave</Badge>
      );
    },
  },
  {
    accessorKey: 'workHours',
    header: 'Work Hours',
  },
];

const leaveRequestColumns: ColumnDef<any>[] = [
  {
    accessorKey: 'employeeName',
    header: 'Employee',
  },
  {
    accessorKey: 'leaveType',
    header: 'Leave Type',
  },
  {
    accessorKey: 'startDate',
    header: 'Start Date',
    cell: ({ row }) => {
      return new Date(row.getValue('startDate')).toLocaleDateString();
    },
  },
  {
    accessorKey: 'endDate',
    header: 'End Date',
    cell: ({ row }) => {
      return new Date(row.getValue('endDate')).toLocaleDateString();
    },
  },
  {
    accessorKey: 'days',
    header: 'Days',
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.getValue('status');
      return status === 'Approved' ? (
        <Badge className="bg-green-500">Approved</Badge>
      ) : status === 'Rejected' ? (
        <Badge variant="destructive">Rejected</Badge>
      ) : (
        <Badge className="bg-yellow-500">Pending</Badge>
      );
    },
  },
  {
    id: 'actions',
    cell: ({ row }) => {
      const status = row.getValue('status');
      return status === 'Pending' ? (
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" className="bg-green-50 text-green-600 hover:bg-green-100">
            Approve
          </Button>
          <Button variant="outline" size="sm" className="bg-red-50 text-red-600 hover:bg-red-100">
            Reject
          </Button>
        </div>
      ) : (
        <Button variant="ghost" size="sm">
          View
        </Button>
      );
    },
  },
];

const payrollColumns: ColumnDef<any>[] = [
  {
    accessorKey: 'month',
    header: 'Month',
  },
  {
    accessorKey: 'totalEmployees',
    header: 'Total Employees',
  },
  {
    accessorKey: 'totalSalary',
    header: 'Total Salary',
    cell: ({ row }) => {
      return <div>RM {row.getValue('totalSalary').toLocaleString()}</div>;
    },
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.getValue('status');
      return status === 'Processed' ? (
        <Badge className="bg-green-500">Processed</Badge>
      ) : (
        <Badge className="bg-yellow-500">Pending</Badge>
      );
    },
  },
  {
    accessorKey: 'processedDate',
    header: 'Processed Date',
    cell: ({ row }) => {
      const date = row.getValue('processedDate');
      return date ? new Date(date).toLocaleDateString() : '-';
    },
  },
  {
    id: 'actions',
    cell: ({ row }) => {
      const status = row.getValue('status');
      return status === 'Pending' ? (
        <Button variant="outline" size="sm">
          Process Payroll
        </Button>
      ) : (
        <Button variant="ghost" size="sm">
          View Details
        </Button>
      );
    },
  },
];

export default function HRPage() {
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  return (
    <div className="space-y-4">
      {/* Page header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between bg-white p-3 md:p-4 rounded-lg shadow-sm mb-3">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <svg className="w-6 h-6 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            HR Management
          </h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage employees, attendance, leave, and payroll
          </p>
        </div>
        <div className="mt-4 md:mt-0 flex space-x-3">
          <Button variant="outline" size="sm">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
            </svg>
            Filter
          </Button>
          <Button size="sm" className="bg-indigo-600 hover:bg-indigo-700">
            <UserPlus className="mr-2 h-4 w-4" />
            Add Employee
          </Button>
        </div>
      </div>

      {successMessage && (
        <Alert variant="success" className="bg-green-50 text-green-800 border-green-200">
          <AlertDescription>{successMessage}</AlertDescription>
        </Alert>
      )}

      {errorMessage && (
        <Alert variant="destructive">
          <AlertDescription>{errorMessage}</AlertDescription>
        </Alert>
      )}

      {/* Employees list */}
      <Card className="border-none shadow-md">
        <CardHeader className="pb-2 border-b">
          <CardTitle className="text-lg font-bold">Employee Directory</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full text-sm text-left">
              <thead className="text-xs font-medium text-gray-500 bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-3 py-2">Employee ID</th>
                  <th className="px-3 py-2">Name</th>
                  <th className="px-3 py-2">Position</th>
                  <th className="px-3 py-2">Department</th>
                  <th className="px-3 py-2">Email</th>
                  <th className="px-3 py-2">Status</th>
                  <th className="px-3 py-2 text-right">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {mockEmployees.map((employee) => (
                  <tr key={employee.id} className="bg-white hover:bg-gray-50 transition-colors">
                    <td className="px-3 py-2 font-medium text-indigo-600">
                      {employee.employeeId}
                    </td>
                    <td className="px-3 py-2 font-medium">
                      {employee.name}
                    </td>
                    <td className="px-3 py-2">
                      {employee.position}
                    </td>
                    <td className="px-3 py-2">
                      {employee.department}
                    </td>
                    <td className="px-3 py-2">
                      {employee.email}
                    </td>
                    <td className="px-3 py-2">
                      <span className={`inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium ${
                        employee.status === 'Active' ? 'bg-green-100 text-green-800' :
                        employee.status === 'On Leave' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {employee.status === 'Active' && (
                          <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        )}
                        {employee.status === 'On Leave' && (
                          <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                        )}
                        {employee.status}
                      </span>
                    </td>
                    <td className="px-3 py-2 text-right">
                      <div className="flex justify-end space-x-1">
                        <Button variant="ghost" size="sm" className="h-7 px-2 text-indigo-600 hover:text-indigo-800 hover:bg-indigo-50">
                          View
                        </Button>
                        <Button variant="ghost" size="sm" className="h-7 px-2 text-gray-600 hover:text-gray-800 hover:bg-gray-50">
                          Edit
                        </Button>
                        <Button variant="ghost" size="sm" className="h-7 px-2 text-gray-600 hover:text-gray-800 hover:bg-gray-50">
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                          </svg>
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          <div className="flex items-center justify-between p-3 border-t border-gray-200">
            <div className="text-sm text-gray-500">
              Showing <span className="font-medium">1</span> to <span className="font-medium">{mockEmployees.length}</span> of <span className="font-medium">{mockEmployees.length}</span> results
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm" disabled>
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                Previous
              </Button>
              <Button variant="outline" size="sm">
                Next
                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
