'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import CustomerForm from '@/components/forms/CustomerForm';
import { Spinner } from '@/components/ui/Spinner';

export default function EditCustomerPage() {
  const params = useParams();
  const customerId = params.id as string;
  
  const [customer, setCustomer] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCustomer = async () => {
      try {
        // In a real implementation, this would fetch the customer from your API
        // const response = await fetch(`/api/customers/${customerId}`);
        // const data = await response.json();
        // setCustomer(data.customer);
        
        // For development, use mock data
        const mockCustomer = {
          id: customerId,
          name: 'Acme Corporation',
          email: '<EMAIL>',
          phone: '+60*********',
          address: '123 Main Street, Kuala Lumpur, Malaysia',
          taxId: '*********',
          notes: 'Important client with multiple projects',
        };
        
        setCustomer(mockCustomer);
      } catch (err) {
        console.error('Error fetching customer:', err);
        setError('Failed to load customer. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchCustomer();
  }, [customerId]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spinner size="lg" />
        <span className="ml-2 text-text-secondary">Loading...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4">
        <p>{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-2 text-red-600 hover:text-red-800 font-medium"
        >
          Try Again
        </button>
      </div>
    );
  }

  if (!customer) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 text-yellow-800 rounded-md p-4">
        <p>Customer not found or you don't have permission to view it.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow-sm mb-6">
        <h1 className="text-2xl font-bold text-text-primary flex items-center">
          <svg className="w-6 h-6 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
          Edit Customer
        </h1>
        <p className="mt-1 text-sm text-text-secondary">
          Update customer information and tax ID
        </p>
      </div>

      <CustomerForm 
        customer={customer} 
        isEditing={true} 
      />
    </div>
  );
}
