'use client';

import { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface InvoiceData {
  invoiceNumber?: string;
  customerName?: string;
  customerTaxId?: string;
  items?: Array<{
    description?: string;
    quantity?: number;
    unitPrice?: number;
    amount?: number;
  }>;
  subtotal?: number;
  tax?: number;
  totalAmount?: number;
}

interface AIValidationAssistantProps {
  invoiceData: InvoiceData;
  className?: string;
}

export default function AIValidationAssistant({ invoiceData, className }: AIValidationAssistantProps) {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [issues, setIssues] = useState<Array<{ id: string; message: string; severity: 'warning' | 'error' }>>([]);
  const [suggestions, setSuggestions] = useState<Array<{ id: string; message: string }>>([]);

  useEffect(() => {
    // Analyze the invoice data when it changes
    analyzeInvoice();
  }, [invoiceData]);

  const analyzeInvoice = () => {
    setIsAnalyzing(true);
    
    // Simulate AI analysis with a delay
    setTimeout(() => {
      const newIssues = [];
      const newSuggestions = [];
      
      // Check for missing customer TIN
      if (!invoiceData.customerTaxId) {
        newIssues.push({
          id: 'missing-tin',
          message: 'Customer Tax ID (TIN) is missing. This is required for LHDN compliance.',
          severity: 'error',
        });
      }
      
      // Check for missing customer name
      if (!invoiceData.customerName) {
        newIssues.push({
          id: 'missing-customer',
          message: 'Customer name is missing.',
          severity: 'error',
        });
      }
      
      // Check for missing invoice items
      if (!invoiceData.items || invoiceData.items.length === 0) {
        newIssues.push({
          id: 'no-items',
          message: 'Invoice has no items. At least one item is required.',
          severity: 'error',
        });
      } else {
        // Check for missing item descriptions
        const itemsWithoutDescription = invoiceData.items.filter(item => !item.description).length;
        if (itemsWithoutDescription > 0) {
          newIssues.push({
            id: 'missing-descriptions',
            message: `${itemsWithoutDescription} item(s) are missing descriptions.`,
            severity: 'warning',
          });
        }
        
        // Check for zero quantities or prices
        const itemsWithZeroValues = invoiceData.items.filter(item => 
          !item.quantity || item.quantity <= 0 || !item.unitPrice || item.unitPrice <= 0
        ).length;
        
        if (itemsWithZeroValues > 0) {
          newIssues.push({
            id: 'zero-values',
            message: `${itemsWithZeroValues} item(s) have zero or negative quantity/price.`,
            severity: 'warning',
          });
        }
      }
      
      // Check for tax calculation
      if (invoiceData.items && invoiceData.items.length > 0) {
        const calculatedSubtotal = invoiceData.items.reduce(
          (sum, item) => sum + (item.amount || 0),
          0
        );
        
        if (Math.abs((invoiceData.subtotal || 0) - calculatedSubtotal) > 0.01) {
          newIssues.push({
            id: 'subtotal-mismatch',
            message: 'Subtotal does not match the sum of item amounts.',
            severity: 'error',
          });
        }
        
        // Check if tax is missing but likely required
        if ((!invoiceData.tax || invoiceData.tax === 0) && calculatedSubtotal > 500) {
          newSuggestions.push({
            id: 'missing-tax',
            message: 'This invoice may require tax. Please verify if tax should be applied.',
          });
        }
      }
      
      // Add AI-powered suggestions
      if (invoiceData.customerTaxId && !invoiceData.customerTaxId.match(/^\d{9}$/)) {
        newSuggestions.push({
          id: 'tin-format',
          message: 'The Tax ID format may be incorrect. Malaysian TINs typically have 9 digits.',
        });
      }
      
      if (invoiceData.items && invoiceData.items.length > 10) {
        newSuggestions.push({
          id: 'many-items',
          message: 'This invoice has many items. Consider grouping similar items for better readability.',
        });
      }
      
      setIssues(newIssues);
      setSuggestions(newSuggestions);
      setIsAnalyzing(false);
    }, 1000);
  };

  return (
    <div className={cn("bg-white rounded-lg shadow p-4", className)}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">AI Validation Assistant</h3>
        <button
          onClick={analyzeInvoice}
          disabled={isAnalyzing}
          className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
        >
          {isAnalyzing ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Analyzing...
            </>
          ) : (
            <>
              <svg className="mr-1.5 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
              </svg>
              Analyze Invoice
            </>
          )}
        </button>
      </div>
      
      {isAnalyzing ? (
        <div className="py-4 text-center text-gray-500">
          <svg className="animate-spin mx-auto h-8 w-8 text-indigo-500" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <p className="mt-2">AI is analyzing your invoice for compliance issues...</p>
        </div>
      ) : (
        <div>
          {issues.length === 0 && suggestions.length === 0 ? (
            <div className="py-4 text-center bg-green-50 rounded-md border border-green-100">
              <svg className="mx-auto h-12 w-12 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="mt-2 text-sm font-medium text-green-800">No compliance issues detected!</p>
              <p className="text-xs text-green-600">This invoice appears to be ready for LHDN validation.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {issues.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Issues to Resolve</h4>
                  <ul className="space-y-2">
                    {issues.map(issue => (
                      <li
                        key={issue.id}
                        className={cn(
                          "p-3 rounded-md text-sm",
                          issue.severity === 'error'
                            ? "bg-red-50 border border-red-100 text-red-800"
                            : "bg-yellow-50 border border-yellow-100 text-yellow-800"
                        )}
                      >
                        <div className="flex">
                          <span className="flex-shrink-0 mr-2">
                            {issue.severity === 'error' ? (
                              <svg className="h-5 w-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                            ) : (
                              <svg className="h-5 w-5 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                              </svg>
                            )}
                          </span>
                          <span>{issue.message}</span>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              
              {suggestions.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">AI Suggestions</h4>
                  <ul className="space-y-2">
                    {suggestions.map(suggestion => (
                      <li
                        key={suggestion.id}
                        className="p-3 rounded-md text-sm bg-blue-50 border border-blue-100 text-blue-800"
                      >
                        <div className="flex">
                          <span className="flex-shrink-0 mr-2">
                            <svg className="h-5 w-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                          </span>
                          <span>{suggestion.message}</span>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
