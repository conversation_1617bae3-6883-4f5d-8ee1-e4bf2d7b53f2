'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { apiClient } from '@/lib/api/client';

export default function InvitePage({ params }: { params: { token: string } }) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isValidToken, setIsValidToken] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [inviteData, setInviteData] = useState<{
    email: string;
    name: string;
    organization: string;
    role: string;
  } | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    password: '',
    confirmPassword: '',
  });

  useEffect(() => {
    const verifyInvite = async () => {
      try {
        // In a real app, this would call the API to verify the invite token
        // const response = await apiClient.get(`/invites/verify/${params.token}`, { skipAuth: true });
        // setInviteData(response);
        
        // Mock verification
        setTimeout(() => {
          // Simulate a valid token
          setInviteData({
            email: '<EMAIL>',
            name: 'Invited User',
            organization: 'Acme Corporation',
            role: 'USER',
          });
          setFormData(prev => ({
            ...prev,
            name: 'Invited User',
          }));
          setIsValidToken(true);
          setIsLoading(false);
        }, 1000);
      } catch (err: any) {
        console.error('Invite verification error:', err);
        setError(err.message || 'Invalid or expired invitation.');
        setIsValidToken(false);
        setIsLoading(false);
      }
    };

    verifyInvite();
  }, [params.token]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsVerifying(true);
    setError(null);

    // Validate passwords match
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      setIsVerifying(false);
      return;
    }

    // Validate password strength
    if (formData.password.length < 8) {
      setError('Password must be at least 8 characters long');
      setIsVerifying(false);
      return;
    }

    try {
      // In a real app, this would call the API to accept the invitation
      // await apiClient.post('/invites/accept', {
      //   token: params.token,
      //   name: formData.name,
      //   password: formData.password,
      // }, { skipAuth: true });
      
      // Mock acceptance
      setTimeout(() => {
        // Redirect to login page
        router.push('/login?invited=true');
      }, 1000);
    } catch (err: any) {
      console.error('Invite acceptance error:', err);
      setError(err.message || 'Failed to accept invitation. Please try again.');
      setIsVerifying(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="w-full max-w-md text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Verifying your invitation...</p>
        </div>
      </div>
    );
  }

  if (!isValidToken) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="space-y-1">
            <div className="mb-4 text-center">
              <Link href="/" className="inline-block">
                <h1 className="text-3xl font-bold text-indigo-600">Invoix</h1>
              </Link>
            </div>
            <CardTitle className="text-2xl font-bold text-center">
              Invalid Invitation
            </CardTitle>
            <CardDescription className="text-center">
              The invitation link is invalid or has expired.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          </CardContent>
          <CardFooter className="flex justify-center">
            <Link href="/login">
              <Button>Go to Login</Button>
            </Link>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <div className="mb-4 text-center">
            <Link href="/" className="inline-block">
              <h1 className="text-3xl font-bold text-indigo-600">Invoix</h1>
            </Link>
          </div>
          <CardTitle className="text-2xl font-bold text-center">
            Accept Invitation
          </CardTitle>
          <CardDescription className="text-center">
            You've been invited to join {inviteData?.organization} as a {inviteData?.role.toLowerCase()}.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
            
            <Input
              label="Email"
              value={inviteData?.email || ''}
              disabled
              helpText="This is the email address the invitation was sent to"
            />
            
            <Input
              label="Name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              required
            />
            
            <Input
              label="Password"
              type="password"
              name="password"
              placeholder="••••••••"
              value={formData.password}
              onChange={handleChange}
              required
            />
            
            <Input
              label="Confirm Password"
              type="password"
              name="confirmPassword"
              placeholder="••••••••"
              value={formData.confirmPassword}
              onChange={handleChange}
              required
            />
            
            <Button
              type="submit"
              className="w-full"
              disabled={isVerifying}
            >
              {isVerifying ? 'Creating Account...' : 'Accept Invitation'}
            </Button>
          </form>
        </CardContent>
        <CardFooter className="flex justify-center">
          <div className="text-sm text-center text-gray-500">
            Already have an account?{' '}
            <Link href="/login" className="text-indigo-600 hover:underline">
              Sign in
            </Link>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
