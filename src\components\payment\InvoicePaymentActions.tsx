'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Spinner } from '@/components/ui/spinner';
import { Badge } from '@/components/ui/badge';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

interface InvoicePaymentActionsProps {
  invoice: any;
  onSuccess?: () => void;
}

export default function InvoicePaymentActions({ invoice, onSuccess }: InvoicePaymentActionsProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [paymentLink, setPaymentLink] = useState<string | null>(null);
  const [expiryDays, setExpiryDays] = useState(30);

  const handleCreatePaymentLink = async () => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);
    
    try {
      // In a real implementation, this would call your API
      // Simulate API call with timeout
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Mock response
      const mockPaymentLink = `https://pay.invoix.com/invoice/${invoice.id}?expires=${expiryDays}d`;
      setPaymentLink(mockPaymentLink);
      setSuccess('Payment link created successfully');
      
      if (onSuccess) {
        onSuccess();
      }
    } catch (err: any) {
      console.error('Error creating payment link:', err);
      setError(err.message || 'Failed to create payment link');
    } finally {
      setIsLoading(false);
      setIsDialogOpen(false);
    }
  };

  const handleCopyLink = () => {
    if (paymentLink) {
      navigator.clipboard.writeText(paymentLink);
      setSuccess('Payment link copied to clipboard');
      
      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    }
  };

  const isPaid = invoice.status === 'PAID';

  return (
    <div className="space-y-4">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      
      {success && (
        <Alert variant="success" className="bg-green-50 text-green-800 border-green-200">
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}
      
      {isPaid ? (
        <div className="text-center py-2">
          <Badge variant="default" className="mb-2">Paid</Badge>
          <p className="text-sm text-gray-500">This invoice has been paid</p>
        </div>
      ) : paymentLink ? (
        <div className="space-y-3">
          <div className="flex items-center">
            <Input 
              value={paymentLink} 
              readOnly 
              className="flex-1"
            />
            <Button 
              variant="outline" 
              size="sm" 
              className="ml-2"
              onClick={handleCopyLink}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
            </Button>
          </div>
          <div className="flex justify-between">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => {
                setPaymentLink(null);
                setSuccess(null);
              }}
            >
              Create New Link
            </Button>
            <Button 
              variant="default" 
              size="sm"
              onClick={() => {
                window.open(paymentLink, '_blank');
              }}
            >
              Open Payment Page
            </Button>
          </div>
        </div>
      ) : (
        <div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button className="w-full">
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                </svg>
                Create Payment Link
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create Payment Link</DialogTitle>
                <DialogDescription>
                  Create a payment link for invoice #{invoice.invoiceNumber}
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="expiryDays">Link Expiry (Days)</Label>
                  <Input 
                    id="expiryDays" 
                    type="number"
                    min="1"
                    max="365"
                    value={expiryDays.toString()} 
                    onChange={(e) => setExpiryDays(parseInt(e.target.value) || 30)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label>Invoice Details</Label>
                  <div className="text-sm">
                    <div className="flex justify-between py-1">
                      <span className="text-gray-500">Invoice Number:</span>
                      <span>{invoice.invoiceNumber}</span>
                    </div>
                    <div className="flex justify-between py-1">
                      <span className="text-gray-500">Amount:</span>
                      <span>RM {invoice.totalAmount.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between py-1">
                      <span className="text-gray-500">Customer:</span>
                      <span>{invoice.customer.name}</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <DialogFooter>
                <Button 
                  variant="outline" 
                  onClick={() => setIsDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button 
                  onClick={handleCreatePaymentLink}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Spinner size="sm" className="mr-2" />
                      Creating...
                    </>
                  ) : (
                    <>Create Payment Link</>
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
          
          <div className="text-center mt-2">
            <p className="text-xs text-gray-500">
              Create a secure payment link to send to your customer
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
