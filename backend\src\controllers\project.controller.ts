import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { handleError } from '../utils/errorHandler';

const prisma = new PrismaClient();

// Project Controllers
export const createProject = async (req: Request, res: Response) => {
  try {
    const {
      name,
      description,
      startDate,
      endDate,
      budget,
      clientId
    } = req.body;

    const tenantId = req.user.tenantId;

    const project = await prisma.project.create({
      data: {
        name,
        description,
        startDate: new Date(startDate),
        endDate: endDate ? new Date(endDate) : null,
        budget: budget ? parseFloat(budget) : null,
        clientId,
        tenant: {
          connect: { id: tenantId }
        }
      }
    });

    res.status(201).json(project);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const getProjects = async (req: Request, res: Response) => {
  try {
    const tenantId = req.user.tenantId;
    const { status, clientId } = req.query;

    const whereClause: any = { tenantId };

    if (status) {
      whereClause.status = status as string;
    }

    if (clientId) {
      whereClause.clientId = clientId as string;
    }

    const projects = await prisma.project.findMany({
      where: whereClause,
      include: {
        tasks: {
          select: {
            id: true,
            status: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Calculate task completion statistics
    const projectsWithStats = projects.map(project => {
      const totalTasks = project.tasks.length;
      const completedTasks = project.tasks.filter(task => task.status === 'DONE').length;
      const completionPercentage = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

      return {
        ...project,
        taskStats: {
          total: totalTasks,
          completed: completedTasks,
          completionPercentage
        }
      };
    });

    res.status(200).json(projectsWithStats);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const getProject = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenantId;

    const project = await prisma.project.findFirst({
      where: {
        id,
        tenantId
      },
      include: {
        tasks: {
          orderBy: {
            createdAt: 'asc'
          }
        },
        timeEntries: {
          orderBy: {
            startTime: 'desc'
          },
          take: 10
        },
        expenses: {
          orderBy: {
            date: 'desc'
          },
          take: 10
        }
      }
    });

    if (!project) {
      return res.status(404).json({ message: 'Project not found' });
    }

    // Calculate project statistics
    const totalTasks = project.tasks.length;
    const completedTasks = project.tasks.filter(task => task.status === 'DONE').length;
    const completionPercentage = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

    // Calculate total time spent
    const totalHours = project.timeEntries.reduce((sum, entry) => {
      return sum + entry.duration;
    }, 0);

    // Calculate total expenses
    const totalExpenses = project.expenses.reduce((sum, expense) => {
      return sum + expense.amount;
    }, 0);

    const projectWithStats = {
      ...project,
      stats: {
        taskCompletion: {
          total: totalTasks,
          completed: completedTasks,
          completionPercentage
        },
        timeTracking: {
          totalHours
        },
        expenses: {
          totalExpenses
        }
      }
    };

    res.status(200).json(projectWithStats);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const updateProject = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const {
      name,
      description,
      startDate,
      endDate,
      status,
      budget,
      clientId,
      completionPercentage
    } = req.body;

    const tenantId = req.user.tenantId;

    const project = await prisma.project.findFirst({
      where: {
        id,
        tenantId
      }
    });

    if (!project) {
      return res.status(404).json({ message: 'Project not found' });
    }

    const updatedProject = await prisma.project.update({
      where: { id },
      data: {
        name,
        description,
        startDate: startDate ? new Date(startDate) : undefined,
        endDate: endDate ? new Date(endDate) : null,
        status,
        budget: budget ? parseFloat(budget) : null,
        clientId,
        completionPercentage: completionPercentage ? parseFloat(completionPercentage) : undefined
      }
    });

    res.status(200).json(updatedProject);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const deleteProject = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenantId;

    const project = await prisma.project.findFirst({
      where: {
        id,
        tenantId
      },
      include: {
        tasks: true,
        timeEntries: true,
        expenses: true
      }
    });

    if (!project) {
      return res.status(404).json({ message: 'Project not found' });
    }

    // Delete related records in a transaction
    await prisma.$transaction([
      prisma.timeEntry.deleteMany({
        where: {
          projectId: id
        }
      }),
      prisma.projectExpense.deleteMany({
        where: {
          projectId: id
        }
      }),
      prisma.projectTask.deleteMany({
        where: {
          projectId: id
        }
      }),
      prisma.project.delete({
        where: { id }
      })
    ]);

    res.status(200).json({ message: 'Project deleted successfully' });
  } catch (error) {
    handleError(error, req, res);
  }
};

// Project Task Controllers
export const createProjectTask = async (req: Request, res: Response) => {
  try {
    const {
      projectId,
      name,
      description,
      startDate,
      dueDate,
      priority,
      assignedTo,
      estimatedHours
    } = req.body;

    const tenantId = req.user.tenantId;

    // Verify project exists
    const project = await prisma.project.findFirst({
      where: {
        id: projectId,
        tenantId
      }
    });

    if (!project) {
      return res.status(404).json({ message: 'Project not found' });
    }

    const task = await prisma.projectTask.create({
      data: {
        name,
        description,
        startDate: startDate ? new Date(startDate) : null,
        dueDate: dueDate ? new Date(dueDate) : null,
        priority: priority || 'MEDIUM',
        assignedTo,
        estimatedHours: estimatedHours ? parseFloat(estimatedHours) : null,
        project: {
          connect: { id: projectId }
        },
        tenant: {
          connect: { id: tenantId }
        }
      }
    });

    res.status(201).json(task);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const getProjectTasks = async (req: Request, res: Response) => {
  try {
    const tenantId = req.user.tenantId;
    const { projectId, status, assignedTo } = req.query;

    const whereClause: any = { tenantId };

    if (projectId) {
      whereClause.projectId = projectId as string;
    }

    if (status) {
      whereClause.status = status as string;
    }

    if (assignedTo) {
      whereClause.assignedTo = assignedTo as string;
    }

    const tasks = await prisma.projectTask.findMany({
      where: whereClause,
      include: {
        project: {
          select: {
            name: true
          }
        },
        timeEntries: {
          select: {
            duration: true
          }
        }
      },
      orderBy: [
        { dueDate: 'asc' },
        { priority: 'desc' }
      ]
    });

    // Calculate actual hours spent on each task
    const tasksWithHours = tasks.map(task => {
      const actualHours = task.timeEntries.reduce((sum, entry) => {
        return sum + entry.duration;
      }, 0);

      return {
        ...task,
        actualHours
      };
    });

    res.status(200).json(tasksWithHours);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const getProjectTask = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenantId;

    const task = await prisma.projectTask.findFirst({
      where: {
        id,
        tenantId
      },
      include: {
        project: true,
        timeEntries: {
          orderBy: {
            startTime: 'desc'
          }
        }
      }
    });

    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    // Calculate actual hours spent
    const actualHours = task.timeEntries.reduce((sum, entry) => {
      return sum + entry.duration;
    }, 0);

    const taskWithHours = {
      ...task,
      actualHours
    };

    res.status(200).json(taskWithHours);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const updateProjectTask = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const {
      name,
      description,
      startDate,
      dueDate,
      completionDate,
      status,
      priority,
      assignedTo,
      estimatedHours
    } = req.body;

    const tenantId = req.user.tenantId;

    const task = await prisma.projectTask.findFirst({
      where: {
        id,
        tenantId
      }
    });

    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    const updatedTask = await prisma.projectTask.update({
      where: { id },
      data: {
        name,
        description,
        startDate: startDate ? new Date(startDate) : null,
        dueDate: dueDate ? new Date(dueDate) : null,
        completionDate: completionDate ? new Date(completionDate) : status === 'DONE' ? new Date() : null,
        status,
        priority,
        assignedTo,
        estimatedHours: estimatedHours ? parseFloat(estimatedHours) : null
      }
    });

    // If task is marked as done, update project completion percentage
    if (status === 'DONE' && task.status !== 'DONE') {
      const projectTasks = await prisma.projectTask.findMany({
        where: {
          projectId: task.projectId
        }
      });

      const totalTasks = projectTasks.length;
      const completedTasks = projectTasks.filter(t => t.status === 'DONE' || t.id === id).length;
      const completionPercentage = (completedTasks / totalTasks) * 100;

      await prisma.project.update({
        where: { id: task.projectId },
        data: {
          completionPercentage
        }
      });
    }

    res.status(200).json(updatedTask);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const deleteProjectTask = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenantId;

    const task = await prisma.projectTask.findFirst({
      where: {
        id,
        tenantId
      },
      include: {
        timeEntries: true
      }
    });

    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    // Delete related time entries first
    await prisma.timeEntry.deleteMany({
      where: {
        taskId: id
      }
    });

    // Then delete the task
    await prisma.projectTask.delete({
      where: { id }
    });

    // Update project completion percentage
    const projectTasks = await prisma.projectTask.findMany({
      where: {
        projectId: task.projectId
      }
    });

    const totalTasks = projectTasks.length;
    if (totalTasks > 0) {
      const completedTasks = projectTasks.filter(t => t.status === 'DONE').length;
      const completionPercentage = (completedTasks / totalTasks) * 100;

      await prisma.project.update({
        where: { id: task.projectId },
        data: {
          completionPercentage
        }
      });
    }

    res.status(200).json({ message: 'Task deleted successfully' });
  } catch (error) {
    handleError(error, req, res);
  }
};

// Time Entry Controllers
export const createTimeEntry = async (req: Request, res: Response) => {
  try {
    const {
      projectId,
      taskId,
      employeeId,
      description,
      startTime,
      endTime,
      duration,
      billable
    } = req.body;

    const tenantId = req.user.tenantId;

    // Verify project exists
    const project = await prisma.project.findFirst({
      where: {
        id: projectId,
        tenantId
      }
    });

    if (!project) {
      return res.status(404).json({ message: 'Project not found' });
    }

    // Verify task exists if provided
    if (taskId) {
      const task = await prisma.projectTask.findFirst({
        where: {
          id: taskId,
          projectId,
          tenantId
        }
      });

      if (!task) {
        return res.status(404).json({ message: 'Task not found' });
      }
    }

    // Calculate duration if not provided
    let calculatedDuration = duration;
    if (!calculatedDuration && startTime && endTime) {
      const start = new Date(startTime);
      const end = new Date(endTime);
      calculatedDuration = (end.getTime() - start.getTime()) / (1000 * 60 * 60); // Convert to hours
    }

    const timeEntry = await prisma.timeEntry.create({
      data: {
        description,
        startTime: new Date(startTime),
        endTime: endTime ? new Date(endTime) : null,
        duration: parseFloat(calculatedDuration || 0),
        billable: billable !== undefined ? billable : true,
        employeeId,
        project: {
          connect: { id: projectId }
        },
        task: taskId ? {
          connect: { id: taskId }
        } : undefined,
        tenant: {
          connect: { id: tenantId }
        }
      }
    });

    // Update task actual hours
    if (taskId) {
      const taskTimeEntries = await prisma.timeEntry.findMany({
        where: {
          taskId
        }
      });

      const totalHours = taskTimeEntries.reduce((sum, entry) => {
        return sum + entry.duration;
      }, 0);

      await prisma.projectTask.update({
        where: { id: taskId },
        data: {
          actualHours: totalHours
        }
      });
    }

    res.status(201).json(timeEntry);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const getTimeEntries = async (req: Request, res: Response) => {
  try {
    const tenantId = req.user.tenantId;
    const { projectId, taskId, employeeId, startDate, endDate } = req.query;

    const whereClause: any = { tenantId };

    if (projectId) {
      whereClause.projectId = projectId as string;
    }

    if (taskId) {
      whereClause.taskId = taskId as string;
    }

    if (employeeId) {
      whereClause.employeeId = employeeId as string;
    }

    if (startDate && endDate) {
      whereClause.startTime = {
        gte: new Date(startDate as string),
        lte: new Date(endDate as string)
      };
    } else if (startDate) {
      whereClause.startTime = {
        gte: new Date(startDate as string)
      };
    } else if (endDate) {
      whereClause.startTime = {
        lte: new Date(endDate as string)
      };
    }

    const timeEntries = await prisma.timeEntry.findMany({
      where: whereClause,
      include: {
        project: {
          select: {
            name: true
          }
        },
        task: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        startTime: 'desc'
      }
    });

    res.status(200).json(timeEntries);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const updateTimeEntry = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const {
      description,
      startTime,
      endTime,
      duration,
      billable
    } = req.body;

    const tenantId = req.user.tenantId;

    const timeEntry = await prisma.timeEntry.findFirst({
      where: {
        id,
        tenantId
      }
    });

    if (!timeEntry) {
      return res.status(404).json({ message: 'Time entry not found' });
    }

    // Calculate duration if not provided
    let calculatedDuration = duration;
    if (!calculatedDuration && startTime && endTime) {
      const start = new Date(startTime);
      const end = new Date(endTime);
      calculatedDuration = (end.getTime() - start.getTime()) / (1000 * 60 * 60); // Convert to hours
    }

    const updatedTimeEntry = await prisma.timeEntry.update({
      where: { id },
      data: {
        description,
        startTime: startTime ? new Date(startTime) : undefined,
        endTime: endTime ? new Date(endTime) : null,
        duration: calculatedDuration ? parseFloat(calculatedDuration) : undefined,
        billable: billable !== undefined ? billable : undefined
      }
    });

    // Update task actual hours if this entry is associated with a task
    if (timeEntry.taskId) {
      const taskTimeEntries = await prisma.timeEntry.findMany({
        where: {
          taskId: timeEntry.taskId
        }
      });

      const totalHours = taskTimeEntries.reduce((sum, entry) => {
        return sum + entry.duration;
      }, 0);

      await prisma.projectTask.update({
        where: { id: timeEntry.taskId },
        data: {
          actualHours: totalHours
        }
      });
    }

    res.status(200).json(updatedTimeEntry);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const deleteTimeEntry = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenantId;

    const timeEntry = await prisma.timeEntry.findFirst({
      where: {
        id,
        tenantId
      }
    });

    if (!timeEntry) {
      return res.status(404).json({ message: 'Time entry not found' });
    }

    const taskId = timeEntry.taskId;

    await prisma.timeEntry.delete({
      where: { id }
    });

    // Update task actual hours if this entry was associated with a task
    if (taskId) {
      const taskTimeEntries = await prisma.timeEntry.findMany({
        where: {
          taskId
        }
      });

      const totalHours = taskTimeEntries.reduce((sum, entry) => {
        return sum + entry.duration;
      }, 0);

      await prisma.projectTask.update({
        where: { id: taskId },
        data: {
          actualHours: totalHours
        }
      });
    }

    res.status(200).json({ message: 'Time entry deleted successfully' });
  } catch (error) {
    handleError(error, req, res);
  }
};

// Project Expense Controllers
export const createProjectExpense = async (req: Request, res: Response) => {
  try {
    const {
      projectId,
      description,
      amount,
      date,
      category,
      receiptUrl,
      reimbursable
    } = req.body;

    const tenantId = req.user.tenantId;

    // Verify project exists
    const project = await prisma.project.findFirst({
      where: {
        id: projectId,
        tenantId
      }
    });

    if (!project) {
      return res.status(404).json({ message: 'Project not found' });
    }

    const expense = await prisma.projectExpense.create({
      data: {
        description,
        amount: parseFloat(amount),
        date: new Date(date),
        category,
        receiptUrl,
        reimbursable: reimbursable !== undefined ? reimbursable : false,
        project: {
          connect: { id: projectId }
        },
        tenant: {
          connect: { id: tenantId }
        }
      }
    });

    // Update project actual cost
    const projectExpenses = await prisma.projectExpense.findMany({
      where: {
        projectId
      }
    });

    const totalExpenses = projectExpenses.reduce((sum, exp) => {
      return sum + exp.amount;
    }, 0);

    await prisma.project.update({
      where: { id: projectId },
      data: {
        actualCost: totalExpenses
      }
    });

    res.status(201).json(expense);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const getProjectExpenses = async (req: Request, res: Response) => {
  try {
    const tenantId = req.user.tenantId;
    const { projectId, category, status } = req.query;

    const whereClause: any = { tenantId };

    if (projectId) {
      whereClause.projectId = projectId as string;
    }

    if (category) {
      whereClause.category = category as string;
    }

    if (status) {
      whereClause.status = status as string;
    }

    const expenses = await prisma.projectExpense.findMany({
      where: whereClause,
      include: {
        project: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        date: 'desc'
      }
    });

    res.status(200).json(expenses);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const updateProjectExpense = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const {
      description,
      amount,
      date,
      category,
      receiptUrl,
      reimbursable,
      status
    } = req.body;

    const tenantId = req.user.tenantId;

    const expense = await prisma.projectExpense.findFirst({
      where: {
        id,
        tenantId
      }
    });

    if (!expense) {
      return res.status(404).json({ message: 'Expense not found' });
    }

    const updatedExpense = await prisma.projectExpense.update({
      where: { id },
      data: {
        description,
        amount: amount ? parseFloat(amount) : undefined,
        date: date ? new Date(date) : undefined,
        category,
        receiptUrl,
        reimbursable: reimbursable !== undefined ? reimbursable : undefined,
        status
      }
    });

    // Update project actual cost if amount changed
    if (amount && parseFloat(amount) !== expense.amount) {
      const projectExpenses = await prisma.projectExpense.findMany({
        where: {
          projectId: expense.projectId
        }
      });

      const totalExpenses = projectExpenses.reduce((sum, exp) => {
        return sum + (exp.id === id ? parseFloat(amount) : exp.amount);
      }, 0);

      await prisma.project.update({
        where: { id: expense.projectId },
        data: {
          actualCost: totalExpenses
        }
      });
    }

    res.status(200).json(updatedExpense);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const deleteProjectExpense = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenantId;

    const expense = await prisma.projectExpense.findFirst({
      where: {
        id,
        tenantId
      }
    });

    if (!expense) {
      return res.status(404).json({ message: 'Expense not found' });
    }

    const projectId = expense.projectId;

    await prisma.projectExpense.delete({
      where: { id }
    });

    // Update project actual cost
    const projectExpenses = await prisma.projectExpense.findMany({
      where: {
        projectId
      }
    });

    const totalExpenses = projectExpenses.reduce((sum, exp) => {
      return sum + exp.amount;
    }, 0);

    await prisma.project.update({
      where: { id: projectId },
      data: {
        actualCost: totalExpenses
      }
    });

    res.status(200).json({ message: 'Expense deleted successfully' });
  } catch (error) {
    handleError(error, req, res);
  }
};
