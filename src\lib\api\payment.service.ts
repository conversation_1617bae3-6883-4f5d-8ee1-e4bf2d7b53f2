import { apiClient } from './client';

// Types
export interface PaymentMethod {
  id: string;
  type: 'CREDIT_CARD' | 'BANK_TRANSFER' | 'CASH' | 'PAYPAL' | 'OTHER';
  name: string;
  isDefault: boolean;
  details?: Record<string, any>;
}

export interface PaymentGateway {
  id: string;
  name: string;
  provider: 'STRIPE' | 'PAYPAL' | 'BILLPLZ' | 'OTHER';
  isActive: boolean;
  supportedMethods: string[];
}

export interface CreatePaymentRequest {
  invoiceId: string;
  amount: number;
  paymentMethod: string;
  paymentGateway?: string;
  returnUrl?: string;
  metadata?: Record<string, any>;
}

export interface PaymentResponse {
  id: string;
  invoiceId: string;
  amount: number;
  status: 'PENDING' | 'COMPLETED' | 'FAILED' | 'REFUNDED';
  paymentMethod: string;
  paymentGateway: string;
  transactionId?: string;
  paymentUrl?: string;
  createdAt: string;
  updatedAt: string;
}

export interface PaymentGatewayConfig {
  provider: string;
  apiKey: string;
  secretKey: string;
  webhookSecret?: string;
  isActive: boolean;
}

/**
 * Payment API Service
 * Provides methods for interacting with payment-related API endpoints
 */
class PaymentService {
  /**
   * Get all payment methods for the current tenant
   */
  async getPaymentMethods(): Promise<PaymentMethod[]> {
    return apiClient.get('/payments/methods');
  }

  /**
   * Get all payment gateways for the current tenant
   */
  async getPaymentGateways(): Promise<PaymentGateway[]> {
    return apiClient.get('/payments/gateways');
  }

  /**
   * Create a new payment
   */
  async createPayment(data: CreatePaymentRequest): Promise<PaymentResponse> {
    return apiClient.post('/payments', data);
  }

  /**
   * Get payment by ID
   */
  async getPayment(paymentId: string): Promise<PaymentResponse> {
    return apiClient.get(`/payments/${paymentId}`);
  }

  /**
   * Get payments for an invoice
   */
  async getInvoicePayments(invoiceId: string): Promise<PaymentResponse[]> {
    return apiClient.get(`/invoices/${invoiceId}/payments`);
  }

  /**
   * Create a payment intent with Stripe
   */
  async createStripePaymentIntent(invoiceId: string, amount: number): Promise<{ clientSecret: string }> {
    return apiClient.post('/payments/stripe/create-intent', { invoiceId, amount });
  }

  /**
   * Create a PayPal payment
   */
  async createPayPalPayment(invoiceId: string, amount: number, returnUrl: string): Promise<{ paymentUrl: string; paymentId: string }> {
    return apiClient.post('/payments/paypal/create', { invoiceId, amount, returnUrl });
  }

  /**
   * Create a BillPlz payment
   */
  async createBillPlzPayment(invoiceId: string, amount: number, returnUrl: string): Promise<{ billUrl: string; billId: string }> {
    return apiClient.post('/payments/billplz/create', { invoiceId, amount, returnUrl });
  }

  /**
   * Record a manual payment (cash, bank transfer, etc.)
   */
  async recordManualPayment(invoiceId: string, amount: number, method: string, reference?: string, notes?: string): Promise<PaymentResponse> {
    return apiClient.post('/payments/manual', { invoiceId, amount, method, reference, notes });
  }

  /**
   * Refund a payment
   */
  async refundPayment(paymentId: string, amount?: number, reason?: string): Promise<PaymentResponse> {
    return apiClient.post(`/payments/${paymentId}/refund`, { amount, reason });
  }

  /**
   * Get payment gateway configuration
   */
  async getPaymentGatewayConfig(provider: string): Promise<PaymentGatewayConfig> {
    return apiClient.get(`/payments/gateways/${provider}/config`);
  }

  /**
   * Update payment gateway configuration
   */
  async updatePaymentGatewayConfig(provider: string, config: Partial<PaymentGatewayConfig>): Promise<PaymentGatewayConfig> {
    return apiClient.put(`/payments/gateways/${provider}/config`, config);
  }

  /**
   * Enable or disable a payment gateway
   */
  async togglePaymentGateway(provider: string, isActive: boolean): Promise<{ success: boolean }> {
    return apiClient.put(`/payments/gateways/${provider}/toggle`, { isActive });
  }
}

export const paymentService = new PaymentService();
export default paymentService;
