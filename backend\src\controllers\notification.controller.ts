import { Request, Response } from 'express';
import { prisma } from '../index';
import { logger } from '../utils/logger';
import { notificationService } from '../services/notification.service';
import { emailService } from '../services/email.service';
import { whatsappService } from '../services/whatsapp.service';

/**
 * Notification Controller
 * Handles API endpoints for notification management
 */
export class NotificationController {
  /**
   * Get notification settings
   * @route GET /api/notifications/settings
   */
  async getSettings(req: Request, res: Response) {
    try {
      const tenantId = req.user.tenantId;
      const settings = await notificationService.getSettings(tenantId);
      return res.json(settings);
    } catch (error: any) {
      logger.error('Error getting notification settings:', error);
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Update notification settings
   * @route PUT /api/notifications/settings
   */
  async updateSettings(req: Request, res: Response) {
    try {
      const tenantId = req.user.tenantId;
      const settings = req.body;
      const updatedSettings = await notificationService.updateSettings(tenantId, settings);
      return res.json(updatedSettings);
    } catch (error: any) {
      logger.error('Error updating notification settings:', error);
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Get email settings
   * @route GET /api/notifications/email/settings
   */
  async getEmailSettings(req: Request, res: Response) {
    try {
      const tenantId = req.user.tenantId;
      const settings = await prisma.emailSetting.findUnique({
        where: { tenantId },
      });
      
      // Remove sensitive data
      if (settings) {
        const { password, ...safeSettings } = settings;
        return res.json(safeSettings);
      }
      
      return res.json(null);
    } catch (error: any) {
      logger.error('Error getting email settings:', error);
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Update email settings
   * @route PUT /api/notifications/email/settings
   */
  async updateEmailSettings(req: Request, res: Response) {
    try {
      const tenantId = req.user.tenantId;
      const settings = req.body;
      
      const existingSettings = await prisma.emailSetting.findUnique({
        where: { tenantId },
      });
      
      let updatedSettings;
      
      if (existingSettings) {
        updatedSettings = await prisma.emailSetting.update({
          where: { id: existingSettings.id },
          data: settings,
        });
      } else {
        updatedSettings = await prisma.emailSetting.create({
          data: {
            ...settings,
            tenantId,
          },
        });
      }
      
      // Remove sensitive data
      const { password, ...safeSettings } = updatedSettings;
      
      return res.json(safeSettings);
    } catch (error: any) {
      logger.error('Error updating email settings:', error);
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Test email connection
   * @route POST /api/notifications/email/test
   */
  async testEmailConnection(req: Request, res: Response) {
    try {
      const tenantId = req.user.tenantId;
      const { recipient } = req.body;
      
      if (!recipient) {
        return res.status(400).json({ error: 'Recipient email is required' });
      }
      
      const result = await emailService.sendEmail({
        to: recipient,
        subject: 'Test Email from Invoix',
        html: `
          <h1>Test Email</h1>
          <p>This is a test email from Invoix to verify your SMTP settings.</p>
          <p>If you received this email, your email settings are configured correctly.</p>
          <p>Time sent: ${new Date().toLocaleString()}</p>
        `,
        tenantId,
      });
      
      if (result.success) {
        return res.json({ success: true, message: 'Test email sent successfully' });
      } else {
        return res.status(500).json({ success: false, error: result.error });
      }
    } catch (error: any) {
      logger.error('Error testing email connection:', error);
      return res.status(500).json({ success: false, error: error.message });
    }
  }

  /**
   * Get email templates
   * @route GET /api/notifications/email/templates
   */
  async getEmailTemplates(req: Request, res: Response) {
    try {
      const tenantId = req.user.tenantId;
      const templates = await prisma.emailTemplate.findMany({
        where: { tenantId },
      });
      return res.json(templates);
    } catch (error: any) {
      logger.error('Error getting email templates:', error);
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Get email template by name
   * @route GET /api/notifications/email/templates/:name
   */
  async getEmailTemplate(req: Request, res: Response) {
    try {
      const tenantId = req.user.tenantId;
      const { name } = req.params;
      
      const template = await prisma.emailTemplate.findFirst({
        where: {
          tenantId,
          name,
        },
      });
      
      if (!template) {
        return res.status(404).json({ error: 'Template not found' });
      }
      
      return res.json(template);
    } catch (error: any) {
      logger.error('Error getting email template:', error);
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Create or update email template
   * @route PUT /api/notifications/email/templates/:name
   */
  async updateEmailTemplate(req: Request, res: Response) {
    try {
      const tenantId = req.user.tenantId;
      const { name } = req.params;
      const { subject, body, variables } = req.body;
      
      const existingTemplate = await prisma.emailTemplate.findFirst({
        where: {
          tenantId,
          name,
        },
      });
      
      let template;
      
      if (existingTemplate) {
        template = await prisma.emailTemplate.update({
          where: { id: existingTemplate.id },
          data: {
            subject,
            body,
            variables,
          },
        });
      } else {
        template = await prisma.emailTemplate.create({
          data: {
            name,
            subject,
            body,
            variables,
            tenantId,
          },
        });
      }
      
      return res.json(template);
    } catch (error: any) {
      logger.error('Error updating email template:', error);
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Get WhatsApp settings
   * @route GET /api/notifications/whatsapp/settings
   */
  async getWhatsAppSettings(req: Request, res: Response) {
    try {
      const tenantId = req.user.tenantId;
      const settings = await whatsappService.getSettings(tenantId);
      return res.json(settings);
    } catch (error: any) {
      logger.error('Error getting WhatsApp settings:', error);
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Update WhatsApp settings
   * @route PUT /api/notifications/whatsapp/settings
   */
  async updateWhatsAppSettings(req: Request, res: Response) {
    try {
      const tenantId = req.user.tenantId;
      const settings = req.body;
      const updatedSettings = await whatsappService.updateSettings(tenantId, settings);
      return res.json(updatedSettings);
    } catch (error: any) {
      logger.error('Error updating WhatsApp settings:', error);
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Test WhatsApp connection
   * @route POST /api/notifications/whatsapp/test
   */
  async testWhatsAppConnection(req: Request, res: Response) {
    try {
      const tenantId = req.user.tenantId;
      const result = await whatsappService.testConnection(tenantId);
      return res.json(result);
    } catch (error: any) {
      logger.error('Error testing WhatsApp connection:', error);
      return res.status(500).json({ success: false, error: error.message });
    }
  }

  /**
   * Get WhatsApp templates
   * @route GET /api/notifications/whatsapp/templates
   */
  async getWhatsAppTemplates(req: Request, res: Response) {
    try {
      const tenantId = req.user.tenantId;
      const templates = await whatsappService.getTemplates(tenantId);
      return res.json(templates);
    } catch (error: any) {
      logger.error('Error getting WhatsApp templates:', error);
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Get customer notification preferences
   * @route GET /api/notifications/customers/:customerId/preferences
   */
  async getCustomerPreferences(req: Request, res: Response) {
    try {
      const { customerId } = req.params;
      const preferences = await notificationService.getCustomerPreferences(customerId);
      return res.json(preferences);
    } catch (error: any) {
      logger.error('Error getting customer notification preferences:', error);
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Update customer notification preferences
   * @route PUT /api/notifications/customers/:customerId/preferences
   */
  async updateCustomerPreferences(req: Request, res: Response) {
    try {
      const { customerId } = req.params;
      const preferences = req.body;
      const updatedPreferences = await notificationService.updateCustomerPreferences(customerId, preferences);
      return res.json(updatedPreferences);
    } catch (error: any) {
      logger.error('Error updating customer notification preferences:', error);
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Get invoice notification settings
   * @route GET /api/notifications/invoices/:invoiceId/settings
   */
  async getInvoiceNotificationSettings(req: Request, res: Response) {
    try {
      const { invoiceId } = req.params;
      const settings = await notificationService.getInvoiceNotificationSettings(invoiceId);
      return res.json(settings);
    } catch (error: any) {
      logger.error('Error getting invoice notification settings:', error);
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Update invoice notification settings
   * @route PUT /api/notifications/invoices/:invoiceId/settings
   */
  async updateInvoiceNotificationSettings(req: Request, res: Response) {
    try {
      const { invoiceId } = req.params;
      const settings = req.body;
      const updatedSettings = await notificationService.updateInvoiceNotificationSettings(invoiceId, settings);
      return res.json(updatedSettings);
    } catch (error: any) {
      logger.error('Error updating invoice notification settings:', error);
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Send invoice notification
   * @route POST /api/notifications/invoices/:invoiceId/send
   */
  async sendInvoiceNotification(req: Request, res: Response) {
    try {
      const tenantId = req.user.tenantId;
      const { invoiceId } = req.params;
      const { channel } = req.body;
      
      // Get invoice with customer
      const invoice = await prisma.invoice.findUnique({
        where: { id: invoiceId },
        include: {
          customer: true,
          items: true,
        },
      });
      
      if (!invoice) {
        return res.status(404).json({ error: 'Invoice not found' });
      }
      
      // Send notification based on channel
      let result;
      
      if (channel === 'email' || channel === 'both') {
        result = await emailService.sendInvoiceEmail({
          invoiceId,
          tenantId,
          attachPdf: true,
        });
        
        if (!result.success && channel === 'email') {
          return res.status(500).json({ error: result.error });
        }
      }
      
      if (channel === 'whatsapp' || channel === 'both') {
        if (!invoice.customer.phone) {
          return res.status(400).json({ error: 'Customer has no phone number' });
        }
        
        result = await whatsappService.sendInvoice(tenantId, invoiceId, invoice.customer.phone);
      }
      
      return res.json({ success: true, message: 'Invoice notification sent' });
    } catch (error: any) {
      logger.error('Error sending invoice notification:', error);
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Get notification history
   * @route GET /api/notifications/history
   */
  async getNotificationHistory(req: Request, res: Response) {
    try {
      const tenantId = req.user.tenantId;
      const { page, limit, type, channel, status, startDate, endDate, recipient, relatedId, relatedType } = req.query;
      
      const result = await notificationService.getNotificationHistory(tenantId, {
        page: page ? parseInt(page as string) : undefined,
        limit: limit ? parseInt(limit as string) : undefined,
        type: type as string,
        channel: channel as string,
        status: status as string,
        startDate: startDate ? new Date(startDate as string) : undefined,
        endDate: endDate ? new Date(endDate as string) : undefined,
        recipient: recipient as string,
        relatedId: relatedId as string,
        relatedType: relatedType as string,
      });
      
      return res.json(result);
    } catch (error: any) {
      logger.error('Error getting notification history:', error);
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Resend notification
   * @route POST /api/notifications/:notificationId/resend
   */
  async resendNotification(req: Request, res: Response) {
    try {
      const { notificationId } = req.params;
      
      // Get notification
      const notification = await prisma.notification.findUnique({
        where: { id: notificationId },
      });
      
      if (!notification) {
        return res.status(404).json({ error: 'Notification not found' });
      }
      
      // Create new notification with same data
      const result = await notificationService.sendNotification({
        type: notification.type,
        channel: notification.channel,
        recipient: notification.recipient,
        recipientName: notification.recipientName || undefined,
        subject: notification.subject || undefined,
        content: notification.content || undefined,
        tenantId: notification.tenantId,
        relatedId: notification.relatedId || undefined,
        relatedType: notification.relatedType || undefined,
      });
      
      return res.json(result);
    } catch (error: any) {
      logger.error('Error resending notification:', error);
      return res.status(500).json({ error: error.message });
    }
  }
}

// Export singleton instance
export const notificationController = new NotificationController();
export default notificationController;
