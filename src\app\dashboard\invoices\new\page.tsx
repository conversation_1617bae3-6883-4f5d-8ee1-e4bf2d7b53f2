'use client';

import { useState, useEffect } from 'react';
import InvoiceForm from '@/components/forms/InvoiceForm';
import { Spinner } from '@/components/ui/Spinner';

export default function NewInvoicePage() {
  const [customers, setCustomers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCustomers = async () => {
      try {
        // In a real implementation, this would fetch customers from your API
        // const response = await fetch('/api/customers');
        // const data = await response.json();
        // setCustomers(data.customers);
        
        // For development, use mock data
        setCustomers([
          { id: '1', name: 'Acme Corporation', email: '<EMAIL>', taxId: '*********' },
          { id: '2', name: 'Wayne Enterprises', email: '<EMAIL>', taxId: '*********' },
          { id: '3', name: 'Stark Industries', email: '<EMAIL>', taxId: '*********' },
          { id: '4', name: 'Daily Planet', email: '<EMAIL>', taxId: '*********' },
          { id: '5', name: 'LexCorp', email: '<EMAIL>', taxId: '*********' },
        ]);
      } catch (err) {
        console.error('Error fetching customers:', err);
        setError('Failed to load customers. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchCustomers();
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spinner size="lg" />
        <span className="ml-2 text-text-secondary">Loading...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4">
        <p>{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-2 text-red-600 hover:text-red-800 font-medium"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow-sm mb-6">
        <h1 className="text-2xl font-bold text-text-primary flex items-center">
          <svg className="w-6 h-6 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Create New Invoice
        </h1>
        <p className="mt-1 text-sm text-text-secondary">
          Create a new invoice with LHDN MyInvois validation
        </p>
      </div>

      <InvoiceForm customers={customers} />
    </div>
  );
}
