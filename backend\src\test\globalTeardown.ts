// This file runs once after all tests
import { PrismaClient } from '@prisma/client';

export default async function globalTeardown() {
  // Create a test database connection
  const prisma = new PrismaClient();
  
  try {
    // Clean up any test data or connections
    await prisma.$disconnect();
    console.log('Disconnected from test database');
  } catch (error) {
    console.error('Error during test teardown', error);
  }
}
