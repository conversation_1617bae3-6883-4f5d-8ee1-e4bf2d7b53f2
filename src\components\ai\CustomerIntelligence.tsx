'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { DatePicker } from '@/components/ui/date-picker';
import { Badge } from '@/components/ui/badge';
import { PieChart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import aiService from '@/lib/api/ai.service';
import { useToast } from '@/components/ui/use-toast';

const COLORS = ['#4f46e5', '#10b981', '#f59e0b', '#ef4444', '#6b7280'];

export default function CustomerIntelligence() {
  const [isLoading, setIsLoading] = useState(false);
  const [startDate, setStartDate] = useState<Date | undefined>(new Date(new Date().setMonth(new Date().getMonth() - 3)));
  const [endDate, setEndDate] = useState<Date | undefined>(new Date());
  const [data, setData] = useState<any>(null);
  const { toast } = useToast();

  useEffect(() => {
    generateInsights();
  }, []);

  const generateInsights = async () => {
    setIsLoading(true);
    
    try {
      const params = {
        startDate: startDate?.toISOString(),
        endDate: endDate?.toISOString(),
      };
      
      const response = await aiService.getCustomerInsights(params);
      setData(response);
      
      toast({
        title: 'Customer Insights Generated',
        description: 'AI has analyzed your customer data and generated insights.',
        variant: 'default',
      });
    } catch (error) {
      console.error('Error generating customer insights:', error);
      toast({
        title: 'Error',
        description: 'Failed to generate customer insights. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
        <span className="ml-2 text-gray-600">Analyzing customer data...</span>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg border border-dashed border-gray-300">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900">Customer Intelligence</h3>
          <p className="mt-1 text-sm text-gray-500">
            Generate AI-powered customer analysis and predictions
          </p>
          <Button onClick={generateInsights} className="mt-4">Generate Customer Insights</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h2 className="text-lg font-semibold flex items-center">
          <svg 
            className="w-5 h-5 mr-2 text-indigo-600" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24" 
            xmlns="http://www.w3.org/2000/svg"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" 
            />
          </svg>
          AI Customer Intelligence
        </h2>
        <div className="flex flex-col sm:flex-row gap-4">
          <DatePicker
            date={startDate}
            setDate={setStartDate}
            label="Start Date"
            className="w-full sm:w-40"
          />
          <DatePicker
            date={endDate}
            setDate={setEndDate}
            label="End Date"
            className="w-full sm:w-40"
          />
          <Button onClick={generateInsights} className="mt-auto">Update</Button>
        </div>
      </div>

      {/* Customer Segmentation */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="shadow-md">
          <CardHeader className="bg-white border-b pb-3">
            <CardTitle className="text-base font-semibold">Customer Segmentation</CardTitle>
          </CardHeader>
          <CardContent className="pt-4">
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={data.customerSegments}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="count"
                    nameKey="name"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {data.customerSegments.map((entry: any, index: number) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value, name) => [`${value} customers`, name]} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
            <div className="mt-4 grid grid-cols-2 gap-2">
              {data.customerSegments.map((segment: any, index: number) => (
                <div key={index} className="flex items-center justify-between p-2 rounded-md bg-gray-50">
                  <div className="flex items-center">
                    <span 
                      className="w-3 h-3 rounded-full mr-2" 
                      style={{ backgroundColor: COLORS[index % COLORS.length] }}
                    ></span>
                    <span className="text-sm font-medium">{segment.name}</span>
                  </div>
                  <Badge 
                    variant="outline" 
                    className={segment.growth.startsWith('+') ? 
                      "bg-green-50 text-green-700 border-green-200" : 
                      "bg-red-50 text-red-700 border-red-200"}
                  >
                    {segment.growth}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-md">
          <CardHeader className="bg-white border-b pb-3">
            <CardTitle className="text-base font-semibold">Customer Lifecycle</CardTitle>
          </CardHeader>
          <CardContent className="pt-4">
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={[
                    { name: 'Acquisition', value: data.customerLifecycle.acquisition },
                    { name: 'Engagement', value: data.customerLifecycle.engagement },
                    { name: 'Retention', value: data.customerLifecycle.retention },
                    { name: 'Churn', value: data.customerLifecycle.churn },
                  ]}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="value" fill="#4f46e5" name="Customers" />
                </BarChart>
              </ResponsiveContainer>
            </div>
            <div className="mt-4 text-sm text-gray-600">
              <p>
                <span className="font-medium">New customers:</span> {data.customerLifecycle.acquisition} in the selected period
              </p>
              <p>
                <span className="font-medium">Active customers:</span> {data.customerLifecycle.engagement} regularly engaging
              </p>
              <p>
                <span className="font-medium">Churn rate:</span> {((data.customerLifecycle.churn / (data.customerLifecycle.engagement + data.customerLifecycle.retention + data.customerLifecycle.churn)) * 100).toFixed(1)}%
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Customers and Churn Predictions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="shadow-md">
          <CardHeader className="bg-white border-b pb-3">
            <CardTitle className="text-base font-semibold">Top Customers</CardTitle>
          </CardHeader>
          <CardContent className="pt-4">
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-2 px-3 font-medium text-gray-500">Customer</th>
                    <th className="text-right py-2 px-3 font-medium text-gray-500">Revenue</th>
                    <th className="text-right py-2 px-3 font-medium text-gray-500">Growth</th>
                    <th className="text-right py-2 px-3 font-medium text-gray-500">Risk</th>
                  </tr>
                </thead>
                <tbody>
                  {data.topCustomers.map((customer: any, index: number) => (
                    <tr key={index} className={index < data.topCustomers.length - 1 ? "border-b" : ""}>
                      <td className="py-2 px-3 font-medium">{customer.name}</td>
                      <td className="py-2 px-3 text-right">RM {customer.revenue.toLocaleString()}</td>
                      <td className="py-2 px-3 text-right">
                        <Badge 
                          variant="outline" 
                          className={customer.growth.startsWith('+') ? 
                            "bg-green-50 text-green-700 border-green-200" : 
                            "bg-red-50 text-red-700 border-red-200"}
                        >
                          {customer.growth}
                        </Badge>
                      </td>
                      <td className="py-2 px-3 text-right">
                        <Badge 
                          variant={customer.risk === 'low' ? 'outline' : 
                                  customer.risk === 'medium' ? 'secondary' : 'destructive'}
                        >
                          {customer.risk}
                        </Badge>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-md">
          <CardHeader className="bg-white border-b pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-base font-semibold">Churn Predictions</CardTitle>
              <Badge variant="outline" className="bg-indigo-50 text-indigo-700 border-indigo-200">
                AI Powered
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="pt-4">
            <div className="space-y-4">
              {data.churnPredictions.map((prediction: any, index: number) => (
                <div key={index} className="bg-amber-50 border border-amber-100 rounded-md p-3">
                  <div className="flex items-start">
                    <svg 
                      className="w-5 h-5 text-amber-500 mt-0.5 mr-2 flex-shrink-0" 
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24" 
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path 
                        strokeLinecap="round" 
                        strokeLinejoin="round" 
                        strokeWidth={2} 
                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" 
                      />
                    </svg>
                    <div>
                      <div className="font-medium text-amber-800">{prediction.name}</div>
                      <div className="text-sm text-amber-700">{prediction.reason}</div>
                      <div className="mt-1 text-xs text-amber-600">
                        <span className="font-medium">Churn Probability:</span> {prediction.probability}%
                      </div>
                    </div>
                  </div>
                </div>
              ))}
              
              {data.churnPredictions.length === 0 && (
                <div className="flex items-center justify-center h-32 bg-green-50 rounded-md border border-green-100">
                  <div className="text-center">
                    <svg 
                      className="w-8 h-8 text-green-500 mx-auto" 
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24" 
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path 
                        strokeLinecap="round" 
                        strokeLinejoin="round" 
                        strokeWidth={2} 
                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" 
                      />
                    </svg>
                    <p className="mt-2 text-sm font-medium text-green-800">No high-risk customers detected</p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* AI Insights */}
      <Card className="shadow-md">
        <CardHeader className="bg-white border-b pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-base font-semibold">AI-Generated Customer Insights</CardTitle>
            <Badge variant="outline" className="bg-indigo-50 text-indigo-700 border-indigo-200">
              AI Powered
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="pt-4">
          <ul className="space-y-3">
            {data.insights.map((insight: string, index: number) => (
              <li key={index} className="flex items-start">
                <svg 
                  className="w-5 h-5 text-indigo-600 mt-0.5 mr-2 flex-shrink-0" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24" 
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" 
                  />
                </svg>
                <span>{insight}</span>
              </li>
            ))}
          </ul>
        </CardContent>
      </Card>
    </div>
  );
}
