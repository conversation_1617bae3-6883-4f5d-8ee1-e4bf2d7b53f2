import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

/**
 * API route for getting all templates
 */
export async function GET(request: NextRequest) {
  try {
    // Get the user session
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    // Get the access token from the session
    const accessToken = session.accessToken;

    // Call the backend API to get all templates
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/templates`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      },
    });

    // If the response is not OK, return an error
    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      return NextResponse.json(
        { error: errorData?.message || 'Failed to fetch templates' },
        { status: response.status }
      );
    }

    // Return the templates
    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching templates:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

/**
 * API route for creating a new template
 */
export async function POST(request: NextRequest) {
  try {
    // Get the user session
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    // Get the access token from the session
    const accessToken = session.accessToken;

    // Get the request body
    const body = await request.json();

    // Call the backend API to create a new template
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/templates`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    // If the response is not OK, return an error
    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      return NextResponse.json(
        { error: errorData?.message || 'Failed to create template' },
        { status: response.status }
      );
    }

    // Return the created template
    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error creating template:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
