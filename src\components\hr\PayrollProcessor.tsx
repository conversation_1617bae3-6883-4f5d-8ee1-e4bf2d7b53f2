'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Spinner } from '@/components/ui/spinner';
import { Badge } from '@/components/ui/badge';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { format } from 'date-fns';

export default function PayrollProcessor() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedMonth, setSelectedMonth] = useState<string>('');
  const [selectedYear, setSelectedYear] = useState<string>(new Date().getFullYear().toString());
  
  // Mock employee data for payroll
  const [employees, setEmployees] = useState([
    {
      id: '1',
      employeeId: 'EMP001',
      name: 'John Doe',
      position: 'Software Engineer',
      department: 'Engineering',
      basicSalary: 5000,
      allowances: 500,
      deductions: 200,
      epfEmployee: 550,
      epfEmployer: 600,
      socso: 50,
      eis: 20,
      pcb: 300,
      netSalary: 4430,
      selected: true,
    },
    {
      id: '2',
      employeeId: 'EMP002',
      name: 'Jane Smith',
      position: 'HR Manager',
      department: 'Human Resources',
      basicSalary: 6000,
      allowances: 600,
      deductions: 0,
      epfEmployee: 660,
      epfEmployer: 720,
      socso: 60,
      eis: 24,
      pcb: 400,
      netSalary: 5456,
      selected: true,
    },
    {
      id: '3',
      employeeId: 'EMP003',
      name: 'Michael Johnson',
      position: 'Sales Executive',
      department: 'Sales',
      basicSalary: 4500,
      allowances: 800,
      deductions: 100,
      epfEmployee: 495,
      epfEmployer: 540,
      socso: 45,
      eis: 18,
      pcb: 250,
      netSalary: 4410,
      selected: true,
    },
    {
      id: '4',
      employeeId: 'EMP004',
      name: 'Sarah Williams',
      position: 'Accountant',
      department: 'Finance',
      basicSalary: 5500,
      allowances: 400,
      deductions: 150,
      epfEmployee: 605,
      epfEmployer: 660,
      socso: 55,
      eis: 22,
      pcb: 350,
      netSalary: 4740,
      selected: true,
    },
    {
      id: '5',
      employeeId: 'EMP005',
      name: 'David Brown',
      position: 'Marketing Specialist',
      department: 'Marketing',
      basicSalary: 4800,
      allowances: 500,
      deductions: 0,
      epfEmployee: 528,
      epfEmployer: 576,
      socso: 48,
      eis: 19.2,
      pcb: 280,
      netSalary: 4444,
      selected: true,
    },
  ]);
  
  // Available months
  const months = [
    { value: '01', label: 'January' },
    { value: '02', label: 'February' },
    { value: '03', label: 'March' },
    { value: '04', label: 'April' },
    { value: '05', label: 'May' },
    { value: '06', label: 'June' },
    { value: '07', label: 'July' },
    { value: '08', label: 'August' },
    { value: '09', label: 'September' },
    { value: '10', label: 'October' },
    { value: '11', label: 'November' },
    { value: '12', label: 'December' },
  ];
  
  // Available years
  const currentYear = new Date().getFullYear();
  const years = [
    (currentYear - 1).toString(),
    currentYear.toString(),
    (currentYear + 1).toString(),
  ];

  const handleToggleEmployee = (id: string) => {
    setEmployees(employees.map(emp => 
      emp.id === id ? { ...emp, selected: !emp.selected } : emp
    ));
  };

  const handleSelectAll = (selected: boolean) => {
    setEmployees(employees.map(emp => ({ ...emp, selected })));
  };

  const handleProcessPayroll = async () => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);
    
    try {
      if (!selectedMonth) {
        throw new Error('Please select a month');
      }
      
      if (!selectedYear) {
        throw new Error('Please select a year');
      }
      
      const selectedEmployees = employees.filter(emp => emp.selected);
      
      if (selectedEmployees.length === 0) {
        throw new Error('Please select at least one employee');
      }
      
      // In a real implementation, this would call your API
      // Simulate API call with timeout
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const monthName = months.find(m => m.value === selectedMonth)?.label;
      
      setSuccess(`Payroll processed successfully for ${monthName} ${selectedYear}`);
      setIsDialogOpen(false);
    } catch (err: any) {
      console.error('Error processing payroll:', err);
      setError(err.message || 'Failed to process payroll');
    } finally {
      setIsLoading(false);
    }
  };

  const getTotalPayrollAmount = () => {
    return employees
      .filter(emp => emp.selected)
      .reduce((total, emp) => total + emp.netSalary, 0);
  };

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      
      {success && (
        <Alert variant="success" className="bg-green-50 text-green-800 border-green-200">
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}
      
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h2 className="text-xl font-bold">Payroll Processing</h2>
          <p className="text-sm text-gray-500">Process monthly payroll for employees</p>
        </div>
        <div className="mt-4 md:mt-0">
          <Button onClick={() => setIsDialogOpen(true)}>
            Process Payroll
          </Button>
        </div>
      </div>
      
      <div className="border rounded-md overflow-hidden">
        <div className="bg-gray-50 p-4 border-b">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="selectAll" 
                checked={employees.every(emp => emp.selected)}
                onCheckedChange={(checked) => handleSelectAll(!!checked)}
              />
              <Label htmlFor="selectAll" className="font-medium">Select All</Label>
            </div>
            <div className="text-sm text-gray-500">
              {employees.filter(emp => emp.selected).length} of {employees.length} employees selected
            </div>
          </div>
        </div>
        
        <div className="divide-y">
          {employees.map((employee) => (
            <div key={employee.id} className="p-4 hover:bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Checkbox 
                    id={`employee-${employee.id}`} 
                    checked={employee.selected}
                    onCheckedChange={() => handleToggleEmployee(employee.id)}
                  />
                  <div>
                    <p className="font-medium">{employee.name}</p>
                    <p className="text-sm text-gray-500">{employee.position} • {employee.department}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-medium">RM {employee.netSalary.toFixed(2)}</p>
                  <p className="text-sm text-gray-500">Net Salary</p>
                </div>
              </div>
              
              <div className="mt-3 pl-8 grid grid-cols-1 md:grid-cols-3 gap-2 text-sm">
                <div>
                  <span className="text-gray-500">Basic: </span>
                  <span>RM {employee.basicSalary.toFixed(2)}</span>
                </div>
                <div>
                  <span className="text-gray-500">Allowances: </span>
                  <span>RM {employee.allowances.toFixed(2)}</span>
                </div>
                <div>
                  <span className="text-gray-500">Deductions: </span>
                  <span>RM {employee.deductions.toFixed(2)}</span>
                </div>
                <div>
                  <span className="text-gray-500">EPF: </span>
                  <span>RM {employee.epfEmployee.toFixed(2)}</span>
                </div>
                <div>
                  <span className="text-gray-500">SOCSO: </span>
                  <span>RM {employee.socso.toFixed(2)}</span>
                </div>
                <div>
                  <span className="text-gray-500">PCB: </span>
                  <span>RM {employee.pcb.toFixed(2)}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="bg-gray-50 p-4 border-t">
          <div className="flex justify-between items-center">
            <div>
              <p className="font-medium">Total Payroll Amount</p>
              <p className="text-sm text-gray-500">For selected employees</p>
            </div>
            <div className="text-xl font-bold">
              RM {getTotalPayrollAmount().toFixed(2)}
            </div>
          </div>
        </div>
      </div>
      
      {/* Process Payroll Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Process Payroll</DialogTitle>
            <DialogDescription>
              Select the month and year to process payroll.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="month">Month</Label>
                <Select
                  value={selectedMonth}
                  onValueChange={setSelectedMonth}
                >
                  <SelectTrigger id="month">
                    <SelectValue placeholder="Select month" />
                  </SelectTrigger>
                  <SelectContent>
                    {months.map((month) => (
                      <SelectItem key={month.value} value={month.value}>
                        {month.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="year">Year</Label>
                <Select
                  value={selectedYear}
                  onValueChange={setSelectedYear}
                >
                  <SelectTrigger id="year">
                    <SelectValue placeholder="Select year" />
                  </SelectTrigger>
                  <SelectContent>
                    {years.map((year) => (
                      <SelectItem key={year} value={year}>
                        {year}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label>Selected Employees</Label>
              <div className="p-3 border rounded-md bg-gray-50">
                <p className="font-medium">{employees.filter(emp => emp.selected).length} employees selected</p>
                <p className="text-sm text-gray-500">Total: RM {getTotalPayrollAmount().toFixed(2)}</p>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="paymentDate">Payment Date</Label>
              <Input 
                id="paymentDate"
                type="date" 
                defaultValue={new Date().toISOString().split('T')[0]}
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsDialogOpen(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleProcessPayroll}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Spinner size="sm" className="mr-2" />
                  Processing...
                </>
              ) : (
                <>Process Payroll</>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
