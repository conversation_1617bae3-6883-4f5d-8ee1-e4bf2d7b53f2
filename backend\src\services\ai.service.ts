// Mock AI service for financial insights and fraud detection
// In a real implementation, this would use machine learning models

// Interface for invoice data
interface Invoice {
  id: string;
  invoiceNumber: string;
  status: string;
  dueDate: Date;
  issueDate: Date;
  totalAmount: number;
  customerId: string;
}

// Interface for payment data
interface Payment {
  id: string;
  amount: number;
  paymentDate: Date;
  invoiceId: string;
}

// Interface for customer data
interface Customer {
  id: string;
  name: string;
  email: string;
}

// Interface for financial insights
interface FinancialInsights {
  predictedCashFlow: {
    nextMonth: number;
    next3Months: number;
  };
  paymentTrends: {
    averagePaymentTime: number; // in days
    latePaymentPercentage: number;
  };
  topCustomers: {
    id: string;
    name: string;
    totalSpent: number;
  }[];
  recommendations: string[];
}

// Interface for fraud detection result
interface FraudDetectionResult {
  isSuspicious: boolean;
  riskScore: number; // 0-100
  reasons: string[];
}

// Interface for document processing result
interface DocumentProcessingResult {
  invoiceNumber: string;
  date: string;
  vendor: string;
  totalAmount: string;
  lineItems: {
    description: string;
    quantity: number;
    unitPrice: string;
    amount: string;
  }[];
}

// Interface for customer insights
interface CustomerInsights {
  customerSegments: {
    name: string;
    count: number;
    percentage: number;
    growth: string;
  }[];
  customerLifecycle: {
    acquisition: number;
    engagement: number;
    retention: number;
    churn: number;
  };
  topCustomers: {
    id: string;
    name: string;
    revenue: number;
    growth: string;
    risk: string;
  }[];
  churnPredictions: {
    id: string;
    name: string;
    probability: number;
    reason: string;
  }[];
  insights: string[];
}

// Interface for recommendation
interface Recommendation {
  id: string;
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  effort: 'high' | 'medium' | 'low';
}

// Interface for invoice validation result
interface InvoiceValidationResult {
  isValid: boolean;
  issues: {
    id: string;
    message: string;
    severity: string;
  }[];
  suggestions: {
    id: string;
    message: string;
    action: string;
  }[];
  complianceScore: number;
}

// Generate financial insights
export const generateFinancialInsights = async (
  invoices: Invoice[],
  payments: Payment[],
  customers: Customer[],
  predictionPeriod: number = 3
): Promise<any> => {
  // Mock implementation - in a real system, this would use ML models

  // Calculate total revenue
  const totalRevenue = invoices.reduce((sum, invoice) => sum + invoice.totalAmount, 0);

  // Calculate paid vs unpaid invoices
  const paidInvoices = invoices.filter((invoice) =>
    payments.some((payment) => payment.invoiceId === invoice.id)
  );
  const unpaidInvoices = invoices.filter((invoice) =>
    !payments.some((payment) => payment.invoiceId === invoice.id)
  );

  // Calculate payment statistics
  const paidAmount = paidInvoices.reduce((sum, invoice) => sum + invoice.totalAmount, 0);
  const unpaidAmount = unpaidInvoices.reduce((sum, invoice) => sum + invoice.totalAmount, 0);

  // Calculate overdue invoices
  const currentDate = new Date();
  const overdueInvoices = unpaidInvoices.filter((invoice) =>
    new Date(invoice.dueDate) < currentDate
  );
  const overdueAmount = overdueInvoices.reduce((sum, invoice) => sum + invoice.totalAmount, 0);

  // Calculate predicted cash flow
  const totalLastMonth = invoices
    .filter((invoice) => {
      const invoiceDate = new Date(invoice.issueDate);
      const lastMonth = new Date();
      lastMonth.setMonth(lastMonth.getMonth() - 1);
      return (
        invoiceDate.getMonth() === lastMonth.getMonth() &&
        invoiceDate.getFullYear() === lastMonth.getFullYear()
      );
    })
    .reduce((sum, invoice) => sum + invoice.totalAmount, 0);

  // Predict next month (simple prediction - just using last month's total)
  const predictedNextMonth = totalLastMonth * 1.1; // 10% growth assumption
  const predictedNext3Months = predictedNextMonth * predictionPeriod;

  // Calculate payment trends
  const invoicesWithPayments = invoices.filter((invoice) =>
    payments.some((payment) => payment.invoiceId === invoice.id)
  );

  const paymentTimes = invoicesWithPayments.map((invoice) => {
    const payment = payments.find((p) => p.invoiceId === invoice.id);
    if (!payment) return 0;

    const invoiceDate = new Date(invoice.issueDate);
    const paymentDate = new Date(payment.paymentDate);
    const diffTime = Math.abs(paymentDate.getTime() - invoiceDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  });

  const averagePaymentTime =
    paymentTimes.reduce((sum, days) => sum + days, 0) / paymentTimes.length || 0;

  // Calculate late payment percentage
  const latePayments = invoicesWithPayments.filter((invoice) => {
    const payment = payments.find((p) => p.invoiceId === invoice.id);
    if (!payment) return false;

    const dueDate = new Date(invoice.dueDate);
    const paymentDate = new Date(payment.paymentDate);
    return paymentDate > dueDate;
  });

  const latePaymentPercentage =
    (latePayments.length / invoicesWithPayments.length) * 100 || 0;

  // Calculate top customers
  const customerSpending = customers.map((customer) => {
    const customerInvoices = invoices.filter(
      (invoice) => invoice.customerId === customer.id
    );
    const totalSpent = customerInvoices.reduce(
      (sum, invoice) => sum + invoice.totalAmount,
      0
    );
    return {
      id: customer.id,
      name: customer.name,
      totalSpent,
    };
  });

  // Sort by total spent and get top 5
  const topCustomers = customerSpending
    .sort((a, b) => b.totalSpent - a.totalSpent)
    .slice(0, 5);

  // Generate recommendations
  const recommendations: string[] = [];

  if (latePaymentPercentage > 20) {
    recommendations.push(
      'Consider implementing stricter payment terms or early payment incentives to reduce late payments.'
    );
  }

  if (averagePaymentTime > 30) {
    recommendations.push(
      'Your average payment time is over 30 days. Consider sending payment reminders earlier.'
    );
  }

  if (topCustomers.length > 0 && topCustomers[0].totalSpent > 0) {
    recommendations.push(
      `Focus on retaining ${topCustomers[0].name}, your highest-value customer.`
    );
  }

  // Generate monthly data for charts
  const monthlyData = generateMonthlyData(invoices, payments);

  // Generate cash flow prediction
  const cashFlowPrediction = predictCashFlow(monthlyData, predictionPeriod);

  // Generate expense categories
  const expenseCategories = calculateExpenseCategories(invoices);

  // Detect anomalies
  const anomalies = detectAnomalies(invoices, payments);

  return {
    summary: {
      totalRevenue,
      paidAmount,
      unpaidAmount,
      overdueAmount,
      averagePaymentTime: Math.round(averagePaymentTime),
      invoiceCount: invoices.length,
      paidInvoiceCount: paidInvoices.length,
      unpaidInvoiceCount: unpaidInvoices.length,
      overdueInvoiceCount: overdueInvoices.length,
    },
    predictedCashFlow: {
      nextMonth: predictedNextMonth,
      next3Months: predictedNext3Months,
    },
    paymentTrends: {
      averagePaymentTime,
      latePaymentPercentage,
    },
    cashFlowHistory: monthlyData,
    cashFlowPrediction,
    topExpenseCategories: expenseCategories,
    anomalies,
    topCustomers,
    recommendations,
  };
};

// Helper function to generate monthly data
const generateMonthlyData = (invoices: any[], payments: any[]) => {
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  const monthlyData = [];

  // Get the last 6 months
  const today = new Date();
  for (let i = 5; i >= 0; i--) {
    const month = new Date(today.getFullYear(), today.getMonth() - i, 1);
    const monthName = months[month.getMonth()];

    // Calculate income for this month
    const monthIncome = invoices
      .filter(invoice => {
        const invoiceDate = new Date(invoice.issueDate);
        return invoiceDate.getMonth() === month.getMonth() &&
               invoiceDate.getFullYear() === month.getFullYear();
      })
      .reduce((sum, invoice) => sum + invoice.totalAmount, 0);

    // Calculate expenses for this month (simplified)
    const monthExpenses = monthIncome * (0.7 + Math.random() * 0.2); // Random expenses between 70-90% of income

    monthlyData.push({
      month: monthName,
      income: Math.round(monthIncome),
      expenses: Math.round(monthExpenses),
      balance: Math.round(monthIncome - monthExpenses)
    });
  }

  return monthlyData;
};

// Helper function to predict cash flow
const predictCashFlow = (historicalData: any[], months: number) => {
  const prediction = [];
  const lastMonth = historicalData[historicalData.length - 1];
  const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

  // Calculate average growth rates
  const incomeGrowthRates = [];
  const expenseGrowthRates = [];

  for (let i = 1; i < historicalData.length; i++) {
    if (historicalData[i-1].income > 0) {
      const incomeGrowth = historicalData[i].income / historicalData[i-1].income;
      incomeGrowthRates.push(incomeGrowth);
    }

    if (historicalData[i-1].expenses > 0) {
      const expenseGrowth = historicalData[i].expenses / historicalData[i-1].expenses;
      expenseGrowthRates.push(expenseGrowth);
    }
  }

  const avgIncomeGrowth = incomeGrowthRates.length > 0
    ? incomeGrowthRates.reduce((sum, rate) => sum + rate, 0) / incomeGrowthRates.length
    : 1.03;

  const avgExpenseGrowth = expenseGrowthRates.length > 0
    ? expenseGrowthRates.reduce((sum, rate) => sum + rate, 0) / expenseGrowthRates.length
    : 1.02;

  // Generate predictions
  let lastIncome = lastMonth.income;
  let lastExpenses = lastMonth.expenses;
  const today = new Date();

  for (let i = 1; i <= months; i++) {
    const monthIndex = (today.getMonth() + i) % 12;
    const monthName = monthNames[monthIndex];

    // Apply growth rates with some randomness
    lastIncome = lastIncome * (avgIncomeGrowth + (Math.random() * 0.04 - 0.02));
    lastExpenses = lastExpenses * (avgExpenseGrowth + (Math.random() * 0.03 - 0.015));

    prediction.push({
      month: monthName,
      income: Math.round(lastIncome),
      expenses: Math.round(lastExpenses),
      balance: Math.round(lastIncome - lastExpenses)
    });
  }

  return prediction;
};

// Helper function to calculate expense categories
const calculateExpenseCategories = (invoices: any[]) => {
  // In a real implementation, this would analyze actual expense data
  // For now, we'll generate mock data

  return [
    { name: 'Salaries', value: 25000 },
    { name: 'Rent', value: 8000 },
    { name: 'Marketing', value: 6000 },
    { name: 'Utilities', value: 4000 },
    { name: 'Software', value: 3500 },
  ];
};

// Helper function to detect anomalies
const detectAnomalies = (invoices: any[], payments: any[]) => {
  // In a real implementation, this would use machine learning to detect anomalies
  // For now, we'll generate mock data

  return [
    {
      date: '2023-05-15',
      category: 'Marketing',
      amount: 3500,
      expected: 2000,
      description: 'Unusually high marketing expense'
    },
    {
      date: '2023-06-02',
      category: 'Utilities',
      amount: 1800,
      expected: 1200,
      description: 'Utility bill significantly higher than average'
    },
  ];
};

// Detect potential fraud in an invoice
export const detectFraud = async (
  invoice: Invoice,
  previousInvoices: Invoice[]
): Promise<FraudDetectionResult> => {
  // Mock implementation - in a real system, this would use ML models
  const reasons: string[] = [];
  let riskScore = 0;

  // Check for duplicate invoice numbers
  const duplicateInvoiceNumber = previousInvoices.some(
    (prevInvoice) =>
      prevInvoice.id !== invoice.id &&
      prevInvoice.invoiceNumber === invoice.invoiceNumber
  );

  if (duplicateInvoiceNumber) {
    reasons.push('Duplicate invoice number detected');
    riskScore += 50;
  }

  // Check for unusually high amount
  const averageAmount =
    previousInvoices.reduce((sum, inv) => sum + inv.totalAmount, 0) /
      previousInvoices.length || 0;

  const amountThreshold = averageAmount * 3; // 3x the average

  if (invoice.totalAmount > amountThreshold) {
    reasons.push(`Unusually high invoice amount ($${invoice.totalAmount.toFixed(2)})`);
    riskScore += 30;
  }

  // Check for backdated invoices
  const currentDate = new Date();
  const invoiceDate = new Date(invoice.issueDate);
  const daysDifference = Math.floor(
    (currentDate.getTime() - invoiceDate.getTime()) / (1000 * 60 * 60 * 24)
  );

  if (daysDifference > 30) {
    reasons.push(`Invoice is backdated by ${daysDifference} days`);
    riskScore += 20;
  }

  // Determine if suspicious based on risk score
  const isSuspicious = riskScore >= 50;

  return {
    isSuspicious,
    riskScore,
    reasons,
  };
};

// Process document with OCR
export const processDocument = async (
  filePath: string,
  fileType: string,
  tenantId: string
): Promise<DocumentProcessingResult> => {
  // In a real implementation, this would use OCR services like Google Vision, Azure OCR, or Tesseract
  // For now, we'll return mock data

  console.log(`Processing document: ${filePath} (${fileType}) for tenant ${tenantId}`);

  // Simulate processing delay
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Return mock extracted data
  return {
    invoiceNumber: 'INV-2023-0042',
    date: '2023-05-15',
    vendor: 'Tech Solutions Sdn Bhd',
    totalAmount: 'RM 2,450.00',
    lineItems: [
      {
        description: 'Web Development Services',
        quantity: 1,
        unitPrice: 'RM 2,000.00',
        amount: 'RM 2,000.00',
      },
      {
        description: 'Domain Registration',
        quantity: 2,
        unitPrice: 'RM 75.00',
        amount: 'RM 150.00',
      },
      {
        description: 'SSL Certificate',
        quantity: 1,
        unitPrice: 'RM 300.00',
        amount: 'RM 300.00',
      },
    ],
  };
};

// Generate customer insights
export const generateCustomerInsights = async (
  customers: any[],
  invoices: any[]
): Promise<CustomerInsights> => {
  // In a real implementation, this would use machine learning models
  // For now, we'll use simple calculations and mock data

  // Calculate customer segments
  const customerSegments = [
    { name: 'High Value', count: 25, percentage: 20, growth: '+5%' },
    { name: 'Regular', count: 45, percentage: 37.5, growth: '+2%' },
    { name: 'Occasional', count: 30, percentage: 25, growth: '-3%' },
    { name: 'At Risk', count: 12, percentage: 10, growth: '+1%' },
    { name: 'Inactive', count: 8, percentage: 7.5, growth: '-1%' },
  ];

  // Calculate customer lifecycle
  const customerLifecycle = {
    acquisition: 8,
    engagement: 85,
    retention: 15,
    churn: 12
  };

  // Calculate top customers
  const customerRevenue = new Map();
  invoices.forEach(invoice => {
    const customerId = invoice.customerId;
    const amount = invoice.totalAmount;
    customerRevenue.set(customerId, (customerRevenue.get(customerId) || 0) + amount);
  });

  const topCustomers = Array.from(customerRevenue.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, 5)
    .map(([customerId, revenue]) => {
      const customer = customers.find(c => c.id === customerId);
      return {
        id: customerId,
        name: customer ? customer.name : 'Unknown',
        revenue,
        growth: `+${Math.floor(Math.random() * 15) - 5}%`,
        risk: Math.random() > 0.8 ? 'high' : Math.random() > 0.6 ? 'medium' : 'low'
      };
    });

  // Calculate churn predictions
  const churnPredictions = [
    {
      id: topCustomers[4]?.id || 'C005',
      name: topCustomers[4]?.name || 'Prime Retail',
      probability: 75,
      reason: 'Declining order volume, payment delays'
    },
    {
      id: 'C012',
      name: 'Sunrise Hotels',
      probability: 65,
      reason: 'Reduced engagement, competitor activity'
    },
    {
      id: 'C018',
      name: 'Green Energy Co',
      probability: 55,
      reason: 'Recent service complaints, price sensitivity'
    },
  ];

  // Generate insights
  const insights = [
    'High-value customer segment has grown 5% in the last quarter',
    'Customer acquisition cost has decreased by 12%',
    'Customers in the technology sector show 15% higher retention rates',
    'Implementing a loyalty program could reduce churn by an estimated 20%',
  ];

  return {
    customerSegments,
    customerLifecycle,
    topCustomers,
    churnPredictions,
    insights
  };
};

// Generate AI recommendations
export const generateRecommendations = async (
  type: 'business' | 'compliance' | 'customer',
  invoices: any[],
  customers: any[],
  tenantId: string
): Promise<Recommendation[]> => {
  // In a real implementation, this would use machine learning models
  // For now, we'll return mock recommendations based on the type

  const recommendations = {
    business: [
      {
        id: 'B001',
        title: 'Optimize Cash Flow',
        description: 'Implement early payment discounts to improve cash flow by an estimated 15%',
        impact: 'high' as const,
        effort: 'medium' as const
      },
      {
        id: 'B002',
        title: 'Reduce Operating Costs',
        description: 'Switch to cloud-based software to reduce IT costs by approximately 25%',
        impact: 'medium' as const,
        effort: 'medium' as const
      },
      {
        id: 'B003',
        title: 'Expand Product Line',
        description: 'Data suggests demand for complementary products in your current market',
        impact: 'high' as const,
        effort: 'high' as const
      },
    ],
    compliance: [
      {
        id: 'C001',
        title: 'Update Invoice Templates',
        description: 'Add required LHDN fields to all invoice templates to ensure 100% compliance',
        impact: 'high' as const,
        effort: 'low' as const
      },
      {
        id: 'C002',
        title: 'Implement Automated Checks',
        description: 'Set up pre-submission validation to catch common compliance issues',
        impact: 'high' as const,
        effort: 'medium' as const
      },
      {
        id: 'C003',
        title: 'Schedule Regular Audits',
        description: 'Monthly internal audits can reduce compliance issues by up to 90%',
        impact: 'medium' as const,
        effort: 'medium' as const
      },
    ],
    customer: [
      {
        id: 'CU001',
        title: 'Implement Loyalty Program',
        description: 'A tiered loyalty program could increase retention by an estimated 20%',
        impact: 'high' as const,
        effort: 'medium' as const
      },
      {
        id: 'CU002',
        title: 'Personalized Outreach',
        description: 'Targeted communication to at-risk customers can reduce churn by 35%',
        impact: 'high' as const,
        effort: 'medium' as const
      },
      {
        id: 'CU003',
        title: 'Customer Feedback System',
        description: 'Implementing a structured feedback system can improve satisfaction scores by 25%',
        impact: 'medium' as const,
        effort: 'low' as const
      },
    ],
  };

  return recommendations[type];
};

// Validate invoice for LHDN compliance
export const validateInvoiceForLHDN = async (invoice: any): Promise<InvoiceValidationResult> => {
  // In a real implementation, this would check against LHDN requirements
  // For now, we'll use simple validation rules

  const issues = [];
  const suggestions = [];

  // Check for customer Tax ID
  if (!invoice.customer?.taxId) {
    issues.push({
      id: 'missing-tin',
      message: 'Customer Tax ID (TIN) is missing. This is required for LHDN compliance.',
      severity: 'error',
    });

    suggestions.push({
      id: 'add-tin',
      message: 'Add the customer Tax ID to the invoice.',
      action: 'Update customer profile with Tax ID information.',
    });
  }

  // Check for invoice items
  if (!invoice.items || invoice.items.length === 0) {
    issues.push({
      id: 'no-items',
      message: 'Invoice has no line items. At least one item is required.',
      severity: 'error',
    });
  }

  // Check for invoice date
  if (!invoice.issueDate) {
    issues.push({
      id: 'missing-date',
      message: 'Invoice issue date is missing. This is required for LHDN compliance.',
      severity: 'error',
    });
  }

  // Check for due date
  if (!invoice.dueDate) {
    issues.push({
      id: 'missing-due-date',
      message: 'Invoice due date is missing. This is required for LHDN compliance.',
      severity: 'warning',
    });
  }

  // Calculate compliance score
  const complianceScore = issues.length === 0 ? 100 : Math.max(0, 100 - (issues.length * 15));

  return {
    isValid: issues.length === 0,
    issues,
    suggestions,
    complianceScore,
  };
};
