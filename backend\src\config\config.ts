import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const config = {
  // Server configuration
  port: process.env.PORT || 5000,
  nodeEnv: process.env.NODE_ENV || 'development',
  baseUrl: process.env.BASE_URL || 'http://localhost:5000',

  // JWT configuration
  jwt: {
    secret: process.env.JWT_SECRET || 'default-secret-key-change-this',
    expiresIn: '24h',
  },

  // WhatsApp configuration (Twilio)
  twilio: {
    accountSid: process.env.TWILIO_ACCOUNT_SID,
    authToken: process.env.TWILIO_AUTH_TOKEN,
    phoneNumber: process.env.TWILIO_PHONE_NUMBER,
  },

  // Payment configuration (Stripe)
  stripe: {
    secretKey: process.env.STRIPE_SECRET_KEY,
    webhookSecret: process.env.STRIPE_WEBHOOK_SECRET,
  },

  // LHDN MyInvois API configuration
  lhdn: {
    apiKey: process.env.LHDN_API_KEY,
    apiUrl: process.env.LHDN_API_URL,
  },
};

export default config;
