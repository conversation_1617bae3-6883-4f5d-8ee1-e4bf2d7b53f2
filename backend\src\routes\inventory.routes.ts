import express from 'express';
import { authenticateJWT } from '../middleware/auth';
import {
  createProduct,
  getProducts,
  getProduct,
  updateProduct,
  deleteProduct,
  createWarehouse,
  getWarehouses,
  getWarehouse,
  updateWarehouse,
  deleteWarehouse,
  updateInventoryItem,
  getInventoryItems,
  getInventoryItem,
  createInventoryTransaction,
  getInventoryTransactions
} from '../controllers/inventory.controller';

const router = express.Router();

// Apply authentication middleware to all inventory routes
router.use(authenticateJWT);

// Product routes
router.post('/products', createProduct);
router.get('/products', getProducts);
router.get('/products/:id', getProduct);
router.put('/products/:id', updateProduct);
router.delete('/products/:id', deleteProduct);

// Warehouse routes
router.post('/warehouses', createWarehouse);
router.get('/warehouses', getWarehouses);
router.get('/warehouses/:id', getWarehouse);
router.put('/warehouses/:id', updateWarehouse);
router.delete('/warehouses/:id', deleteWarehouse);

// Inventory Item routes
router.get('/items', getInventoryItems);
router.get('/items/:id', getInventoryItem);
router.put('/items/:id', updateInventoryItem);

// Inventory Transaction routes
router.post('/transactions', createInventoryTransaction);
router.get('/transactions', getInventoryTransactions);

export default router;
