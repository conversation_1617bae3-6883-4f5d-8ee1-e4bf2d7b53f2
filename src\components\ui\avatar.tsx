'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface AvatarProps {
  src?: string;
  alt?: string;
  initials?: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  shape?: 'circle' | 'square' | 'rounded';
  status?: 'online' | 'offline' | 'away' | 'busy' | 'none';
  className?: string;
  fallbackClassName?: string;
  statusClassName?: string;
  bordered?: boolean;
  borderColor?: string;
}

export function Avatar({
  src,
  alt = '',
  initials,
  size = 'md',
  shape = 'circle',
  status = 'none',
  className,
  fallbackClassName,
  statusClassName,
  bordered = false,
  borderColor,
}: AvatarProps) {
  const [imageError, setImageError] = React.useState(false);

  const sizeClasses = {
    xs: 'h-6 w-6 text-xs',
    sm: 'h-8 w-8 text-sm',
    md: 'h-10 w-10 text-base',
    lg: 'h-12 w-12 text-lg',
    xl: 'h-16 w-16 text-xl',
  };

  const shapeClasses = {
    circle: 'rounded-full',
    square: 'rounded-none',
    rounded: 'rounded-md',
  };

  const statusClasses = {
    online: 'bg-green-500',
    offline: 'bg-neutral-400',
    away: 'bg-amber-500',
    busy: 'bg-red-500',
    none: 'hidden',
  };

  const statusSizeClasses = {
    xs: 'h-1.5 w-1.5',
    sm: 'h-2 w-2',
    md: 'h-2.5 w-2.5',
    lg: 'h-3 w-3',
    xl: 'h-3.5 w-3.5',
  };

  const borderClasses = bordered
    ? `ring-2 ${borderColor || 'ring-white'}`
    : '';

  const handleImageError = () => {
    setImageError(true);
  };

  return (
    <div className="relative inline-block">
      {!imageError && src ? (
        <img
          src={src}
          alt={alt}
          onError={handleImageError}
          className={cn(
            'object-cover',
            sizeClasses[size],
            shapeClasses[shape],
            borderClasses,
            className
          )}
        />
      ) : (
        <div
          className={cn(
            'flex items-center justify-center bg-primary-100 text-primary-800 font-medium',
            sizeClasses[size],
            shapeClasses[shape],
            borderClasses,
            fallbackClassName
          )}
        >
          {initials || alt.charAt(0).toUpperCase()}
        </div>
      )}
      {status !== 'none' && (
        <span
          className={cn(
            'absolute bottom-0 right-0 block rounded-full ring-2 ring-white',
            statusClasses[status],
            statusSizeClasses[size],
            statusClassName
          )}
        />
      )}
    </div>
  );
}

interface AvatarGroupProps {
  avatars: {
    src?: string;
    alt?: string;
    initials?: string;
  }[];
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  max?: number;
  className?: string;
}

export function AvatarGroup({
  avatars,
  size = 'md',
  max = 5,
  className,
}: AvatarGroupProps) {
  const visibleAvatars = avatars.slice(0, max);
  const remainingCount = avatars.length - max;

  const offsetClasses = {
    xs: '-ml-1',
    sm: '-ml-1.5',
    md: '-ml-2',
    lg: '-ml-2.5',
    xl: '-ml-3',
  };

  const sizeClasses = {
    xs: 'h-6 w-6 text-xs',
    sm: 'h-8 w-8 text-sm',
    md: 'h-10 w-10 text-base',
    lg: 'h-12 w-12 text-lg',
    xl: 'h-16 w-16 text-xl',
  };

  return (
    <div className={cn('flex', className)}>
      {visibleAvatars.map((avatar, index) => (
        <div
          key={index}
          className={cn(
            index !== 0 && offsetClasses[size],
            'relative inline-block'
          )}
        >
          <Avatar
            src={avatar.src}
            alt={avatar.alt || ''}
            initials={avatar.initials}
            size={size}
            bordered
          />
        </div>
      ))}
      {remainingCount > 0 && (
        <div
          className={cn(
            offsetClasses[size],
            'relative inline-flex items-center justify-center rounded-full bg-neutral-100 text-neutral-800 font-medium ring-2 ring-white',
            sizeClasses[size]
          )}
        >
          +{remainingCount}
        </div>
      )}
    </div>
  );
}
