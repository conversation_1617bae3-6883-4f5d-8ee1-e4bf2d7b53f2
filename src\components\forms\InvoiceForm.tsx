'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { <PERSON>, CardContent, <PERSON>Footer, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/Alert';
import { Spinner } from '@/components/ui/Spinner';
import InvoiceValidationPanel from '@/components/lhdn/InvoiceValidationPanel';
import Customer<PERSON>NField from '@/components/forms/CustomerTINField';
import InvoiceNotificationOptions from '@/components/forms/InvoiceNotificationOptions';

interface InvoiceItem {
  id?: string;
  description: string;
  quantity: number;
  unitPrice: number;
  amount: number;
}

interface Customer {
  id: string;
  name: string;
  email: string;
  taxId?: string;
}

interface NotificationOptions {
  autoSend: boolean;
  channels: {
    email: boolean;
    whatsapp: boolean;
  };
  reminders: {
    enabled: boolean;
    beforeDue: number;
    afterDue: number;
  };
  escalation: {
    enabled: boolean;
    days: number;
  };
}

interface InvoiceFormProps {
  invoice?: {
    id?: string;
    invoiceNumber: string;
    dueDate: string;
    status: string;
    customerId: string;
    tax: number;
    notes?: string;
    items: InvoiceItem[];
    lhdnValidated?: boolean;
    lhdnValidationId?: string;
    notifications?: NotificationOptions;
  };
  customers: Customer[];
  isEditing?: boolean;
}

export default function InvoiceForm({ invoice, customers, isEditing = false }: InvoiceFormProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationMessage, setValidationMessage] = useState<string | null>(null);
  const [lhdnValidated, setLhdnValidated] = useState(invoice?.lhdnValidated || false);

  const [formData, setFormData] = useState({
    invoiceNumber: invoice?.invoiceNumber || '',
    dueDate: invoice?.dueDate || '',
    status: invoice?.status || 'DRAFT',
    customerId: invoice?.customerId || '',
    customerTaxId: '',
    tax: invoice?.tax || 0,
    notes: invoice?.notes || '',
    items: invoice?.items || [{ description: '', quantity: 1, unitPrice: 0, amount: 0 }],
    notifications: {
      autoSend: invoice?.notifications?.autoSend || true,
      channels: {
        email: invoice?.notifications?.channels?.email || true,
        whatsapp: invoice?.notifications?.channels?.whatsapp || false,
      },
      reminders: {
        enabled: invoice?.notifications?.reminders?.enabled || true,
        beforeDue: invoice?.notifications?.reminders?.beforeDue || 3,
        afterDue: invoice?.notifications?.reminders?.afterDue || 7,
      },
      escalation: {
        enabled: invoice?.notifications?.escalation?.enabled || false,
        days: invoice?.notifications?.escalation?.days || 14,
      },
    },
  });

  // Calculate subtotal and total
  const subtotal = formData.items.reduce((sum, item) => sum + item.amount, 0);
  const total = subtotal + formData.tax;

  // Get selected customer
  const selectedCustomer = customers.find(c => c.id === formData.customerId);

  useEffect(() => {
    // If customer has a tax ID, set it
    if (selectedCustomer?.taxId) {
      setFormData(prev => ({
        ...prev,
        customerTaxId: selectedCustomer.taxId || '',
      }));
    }
  }, [formData.customerId, selectedCustomer]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleCustomerTaxIdChange = (value: string, isValid: boolean) => {
    setFormData(prev => ({
      ...prev,
      customerTaxId: value,
    }));
  };

  const handleItemChange = (index: number, field: keyof InvoiceItem, value: string | number) => {
    const newItems = [...formData.items];

    // Update the field
    newItems[index] = {
      ...newItems[index],
      [field]: value,
    };

    // Recalculate amount if quantity or unitPrice changed
    if (field === 'quantity' || field === 'unitPrice') {
      const quantity = field === 'quantity' ? Number(value) : newItems[index].quantity;
      const unitPrice = field === 'unitPrice' ? Number(value) : newItems[index].unitPrice;
      newItems[index].amount = quantity * unitPrice;
    }

    setFormData(prev => ({
      ...prev,
      items: newItems,
    }));
  };

  const addItem = () => {
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, { description: '', quantity: 1, unitPrice: 0, amount: 0 }],
    }));
  };

  const removeItem = (index: number) => {
    if (formData.items.length > 1) {
      const newItems = [...formData.items];
      newItems.splice(index, 1);
      setFormData(prev => ({
        ...prev,
        items: newItems,
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setValidationMessage(null);

    try {
      // Prepare data for API
      const apiData = {
        invoiceNumber: formData.invoiceNumber,
        dueDate: formData.dueDate,
        status: formData.status,
        customerId: formData.customerId,
        tax: formData.tax,
        notes: formData.notes,
        items: formData.items.map(item => ({
          description: item.description,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
        })),
        notifications: formData.notifications,
      };

      // Call API to create or update invoice
      const url = isEditing && invoice?.id
        ? `/api/invoices/${invoice.id}`
        : '/api/invoices';

      const method = isEditing ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(apiData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to save invoice');
      }

      const data = await response.json();

      setValidationMessage('Invoice saved successfully');

      // Redirect to invoice details page after a short delay
      setTimeout(() => {
        router.push(`/dashboard/invoices/${data.invoice.id}`);
      }, 1500);
    } catch (err: any) {
      console.error('Error saving invoice:', err);
      setError(err.message || 'An error occurred while saving the invoice');
    } finally {
      setIsLoading(false);
    }
  };

  const handleValidationComplete = (result: any) => {
    setLhdnValidated(result.isValid);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Left column - Invoice details */}
        <div className="md:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Invoice Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Invoice Number"
                  name="invoiceNumber"
                  value={formData.invoiceNumber}
                  onChange={handleChange}
                  required
                />
                <Input
                  label="Due Date"
                  type="date"
                  name="dueDate"
                  value={formData.dueDate}
                  onChange={handleChange}
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Customer
                  </label>
                  <select
                    name="customerId"
                    value={formData.customerId}
                    onChange={handleChange}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    required
                  >
                    <option value="">Select a customer</option>
                    {customers.map(customer => (
                      <option key={customer.id} value={customer.id}>
                        {customer.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Status
                  </label>
                  <select
                    name="status"
                    value={formData.status}
                    onChange={handleChange}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  >
                    <option value="DRAFT">Draft</option>
                    <option value="SENT">Sent</option>
                    <option value="PAID">Paid</option>
                    <option value="OVERDUE">Overdue</option>
                    <option value="CANCELLED">Cancelled</option>
                  </select>
                </div>
              </div>

              {selectedCustomer && (
                <CustomerTINField
                  value={formData.customerTaxId}
                  onChange={handleCustomerTaxIdChange}
                  label="Customer Tax ID"
                />
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Notes
                </label>
                <textarea
                  name="notes"
                  value={formData.notes}
                  onChange={handleChange}
                  rows={3}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  placeholder="Additional notes or payment instructions"
                />
              </div>
            </CardContent>
          </Card>

          {/* Invoice Items */}
          <Card>
            <CardHeader>
              <CardTitle>Invoice Items</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead>
                      <tr>
                        <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                        <th className="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                        <th className="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Price</th>
                        <th className="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                        <th className="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {formData.items.map((item, index) => (
                        <tr key={index}>
                          <td className="px-3 py-2">
                            <input
                              type="text"
                              value={item.description}
                              onChange={(e) => handleItemChange(index, 'description', e.target.value)}
                              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                              placeholder="Item description"
                              required
                            />
                          </td>
                          <td className="px-3 py-2">
                            <input
                              type="number"
                              min="1"
                              value={item.quantity}
                              onChange={(e) => handleItemChange(index, 'quantity', Number(e.target.value))}
                              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm text-right"
                              required
                            />
                          </td>
                          <td className="px-3 py-2">
                            <input
                              type="number"
                              min="0"
                              step="0.01"
                              value={item.unitPrice}
                              onChange={(e) => handleItemChange(index, 'unitPrice', Number(e.target.value))}
                              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm text-right"
                              required
                            />
                          </td>
                          <td className="px-3 py-2 text-right">
                            {item.amount.toFixed(2)}
                          </td>
                          <td className="px-3 py-2 text-right">
                            <button
                              type="button"
                              onClick={() => removeItem(index)}
                              disabled={formData.items.length <= 1}
                              className="text-red-600 hover:text-red-900 disabled:opacity-50"
                            >
                              Remove
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                <div className="flex justify-end">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={addItem}
                  >
                    Add Item
                  </Button>
                </div>

                <div className="border-t pt-4 space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-500">Subtotal</span>
                    <span className="text-sm font-medium">{subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-gray-500">Tax</span>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      name="tax"
                      value={formData.tax}
                      onChange={handleChange}
                      className="w-32 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm text-right"
                    />
                  </div>
                  <div className="flex justify-between border-t pt-2">
                    <span className="text-base font-medium">Total</span>
                    <span className="text-base font-medium">{total.toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right column - LHDN validation */}
        <div className="space-y-6">
          <InvoiceValidationPanel
            invoiceId={invoice?.id}
            isNewInvoice={!isEditing || !invoice?.id}
            onValidationComplete={handleValidationComplete}
          />

          {/* Notification options */}
          <InvoiceNotificationOptions
            options={formData.notifications}
            onChange={(notificationOptions) => {
              setFormData({
                ...formData,
                notifications: notificationOptions
              });
            }}
            customerHasEmail={!!selectedCustomer?.email}
            customerHasPhone={true} // Assuming all customers have phone numbers for now
          />

          {/* Save invoice card */}
          <Card>
            <CardHeader>
              <CardTitle>Save Invoice</CardTitle>
            </CardHeader>
            <CardContent>
              {error && (
                <Alert variant="destructive" className="mb-4">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {validationMessage && (
                <Alert variant="success" className="mb-4">
                  <AlertDescription>{validationMessage}</AlertDescription>
                </Alert>
              )}

              <div className="text-sm text-gray-500 mb-4">
                {lhdnValidated ? (
                  <p className="text-green-600">
                    This invoice has been validated with LHDN MyInvois.
                  </p>
                ) : (
                  <p>
                    Save the invoice first, then validate it with LHDN MyInvois for tax compliance.
                  </p>
                )}
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Spinner size="sm" className="mr-2" />
                    Saving...
                  </>
                ) : (
                  isEditing ? 'Update Invoice' : 'Create Invoice'
                )}
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    </form>
  );
}
