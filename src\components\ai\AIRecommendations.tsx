'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import aiService from '@/lib/api/ai.service';
import { useToast } from '@/components/ui/use-toast';

export default function AIRecommendations() {
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('business');
  const [data, setData] = useState<any>({
    business: null,
    compliance: null,
    customer: null
  });
  const { toast } = useToast();

  useEffect(() => {
    if (!data[activeTab]) {
      generateRecommendations(activeTab);
    }
  }, [activeTab, data]);

  const generateRecommendations = async (type: 'business' | 'compliance' | 'customer') => {
    setIsLoading(true);
    
    try {
      const response = await aiService.getRecommendations(type);
      setData(prev => ({
        ...prev,
        [type]: response
      }));
      
      toast({
        title: 'Recommendations Generated',
        description: `AI has generated ${type} recommendations.`,
        variant: 'default',
      });
    } catch (error) {
      console.error(`Error generating ${type} recommendations:`, error);
      toast({
        title: 'Error',
        description: `Failed to generate ${type} recommendations. Please try again.`,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const refreshRecommendations = () => {
    generateRecommendations(activeTab as 'business' | 'compliance' | 'customer');
  };

  const getImpactBadge = (impact: string) => {
    switch (impact) {
      case 'high':
        return <Badge className="bg-green-100 text-green-800 border-green-200">High Impact</Badge>;
      case 'medium':
        return <Badge className="bg-amber-100 text-amber-800 border-amber-200">Medium Impact</Badge>;
      case 'low':
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Low Impact</Badge>;
      default:
        return null;
    }
  };

  const getEffortBadge = (effort: string) => {
    switch (effort) {
      case 'low':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Low Effort</Badge>;
      case 'medium':
        return <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">Medium Effort</Badge>;
      case 'high':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">High Effort</Badge>;
      default:
        return null;
    }
  };

  const getTabIcon = (tab: string) => {
    switch (tab) {
      case 'business':
        return (
          <svg 
            className="w-4 h-4 mr-2" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24" 
            xmlns="http://www.w3.org/2000/svg"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" 
            />
          </svg>
        );
      case 'compliance':
        return (
          <svg 
            className="w-4 h-4 mr-2" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24" 
            xmlns="http://www.w3.org/2000/svg"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" 
            />
          </svg>
        );
      case 'customer':
        return (
          <svg 
            className="w-4 h-4 mr-2" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24" 
            xmlns="http://www.w3.org/2000/svg"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" 
            />
          </svg>
        );
      default:
        return null;
    }
  };

  return (
    <Card className="shadow-md">
      <CardHeader className="bg-white border-b pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold flex items-center">
            <svg 
              className="w-5 h-5 mr-2 text-indigo-600" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24" 
              xmlns="http://www.w3.org/2000/svg"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" 
              />
            </svg>
            AI Recommendations
          </CardTitle>
          <Badge variant="outline" className="bg-indigo-50 text-indigo-700 border-indigo-200">
            AI Powered
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="pt-4">
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value)}>
          <TabsList className="grid grid-cols-3 mb-6">
            <TabsTrigger value="business" className="flex items-center justify-center">
              {getTabIcon('business')}
              Business
            </TabsTrigger>
            <TabsTrigger value="compliance" className="flex items-center justify-center">
              {getTabIcon('compliance')}
              Compliance
            </TabsTrigger>
            <TabsTrigger value="customer" className="flex items-center justify-center">
              {getTabIcon('customer')}
              Customer
            </TabsTrigger>
          </TabsList>
          
          {['business', 'compliance', 'customer'].map((tab) => (
            <TabsContent key={tab} value={tab} className="mt-0">
              {isLoading && activeTab === tab ? (
                <div className="flex items-center justify-center h-64">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                  <span className="ml-2 text-gray-600">Generating recommendations...</span>
                </div>
              ) : data[tab] ? (
                <div className="space-y-4">
                  <div className="flex justify-end">
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={refreshRecommendations}
                      className="text-xs"
                    >
                      <svg 
                        className="w-3.5 h-3.5 mr-1" 
                        fill="none" 
                        stroke="currentColor" 
                        viewBox="0 0 24 24" 
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path 
                          strokeLinecap="round" 
                          strokeLinejoin="round" 
                          strokeWidth={2} 
                          d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" 
                        />
                      </svg>
                      Refresh
                    </Button>
                  </div>
                  
                  {data[tab].map((recommendation: any) => (
                    <div key={recommendation.id} className="bg-white border rounded-lg p-4 shadow-sm">
                      <div className="flex justify-between items-start">
                        <h3 className="font-medium text-gray-900">{recommendation.title}</h3>
                        <div className="flex space-x-2">
                          {getImpactBadge(recommendation.impact)}
                          {getEffortBadge(recommendation.effort)}
                        </div>
                      </div>
                      <p className="mt-2 text-sm text-gray-600">{recommendation.description}</p>
                      <div className="mt-4 flex justify-end">
                        <Button 
                          variant="outline" 
                          size="sm"
                          className="text-xs mr-2"
                        >
                          Save for Later
                        </Button>
                        <Button 
                          size="sm"
                          className="text-xs bg-indigo-600 hover:bg-indigo-700"
                        >
                          Implement
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg border border-dashed border-gray-300">
                  <div className="text-center">
                    <h3 className="text-lg font-medium text-gray-900">{tab.charAt(0).toUpperCase() + tab.slice(1)} Recommendations</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      Generate AI-powered recommendations to improve your {tab}
                    </p>
                    <Button 
                      onClick={() => generateRecommendations(tab as 'business' | 'compliance' | 'customer')} 
                      className="mt-4"
                    >
                      Generate Recommendations
                    </Button>
                  </div>
                </div>
              )}
            </TabsContent>
          ))}
        </Tabs>
      </CardContent>
    </Card>
  );
}
