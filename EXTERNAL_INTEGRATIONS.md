# External Service Integrations

This document summarizes the external service integrations implemented for the Invoix ERP platform.

## Payment Gateway Integration

### Supported Payment Gateways

1. **Stripe**
   - Credit card processing
   - Payment intents API
   - Webhook handling for payment status updates
   - Client-side integration with Stripe Elements

2. **PayPal**
   - Express Checkout integration
   - Payment status tracking
   - Webhook handling

3. **BillPlz**
   - Malaysian payment gateway integration
   - Bill creation and tracking
   - Webhook handling

### Implementation Details

- **Frontend Service**: `src/lib/api/payment.service.ts`
  - Provides methods for interacting with payment-related API endpoints
  - Handles different payment methods and gateways
  - Manages payment status tracking

- **Backend Service**: `backend/src/services/payment.service.ts`
  - Processes payments through different gateways
  - Handles payment status updates
  - Updates invoice status based on payments
  - Manages webhook events

- **Webhook Controller**: `backend/src/controllers/webhook.controller.ts`
  - Handles webhook events from payment gateways
  - Verifies webhook signatures
  - Updates payment and invoice status

- **Webhook Routes**: `backend/src/routes/webhook.routes.ts`
  - Defines routes for webhook endpoints
  - Configures middleware for webhook processing

### Database Models

- **PaymentMethod**: Stores available payment methods
- **PaymentGateway**: Stores payment gateway configurations
- **Payment**: Tracks payment transactions
- **PaymentGatewayConfig**: Stores API credentials and settings

## LHDN MyInvois API Integration

### Features

1. **E-Invoice Validation**
   - Validates invoices against LHDN requirements
   - Ensures compliance with Malaysian tax regulations

2. **E-Invoice Submission**
   - Submits invoices to LHDN MyInvois system
   - Tracks submission status

3. **Certificate Management**
   - Handles digital certificate upload and storage
   - Manages certificate expiration

4. **Reporting**
   - Generates reports for submitted invoices
   - Provides submission status tracking

### Implementation Details

- **Frontend Service**: `src/lib/api/lhdn.service.ts`
  - Provides methods for interacting with LHDN-related API endpoints
  - Manages certificate operations
  - Handles invoice submissions

- **Backend Service**: `backend/src/services/lhdn.service.ts`
  - Integrates with LHDN MyInvois API
  - Handles digital certificate authentication
  - Converts invoices to UBL format
  - Manages submission status

### Database Models

- **LHDNCertificate**: Stores digital certificate information
- **LHDNSettings**: Stores LHDN integration settings
- **LHDNInvoiceSubmission**: Tracks invoice submissions to LHDN

## WhatsApp Integration

### Features

1. **Invoice Notifications**
   - Sends invoice details via WhatsApp
   - Attaches invoice PDF

2. **Payment Reminders**
   - Sends payment reminder messages
   - Includes payment links

3. **Payment Receipts**
   - Sends payment confirmation messages
   - Attaches receipt PDF

4. **Template Management**
   - Creates and manages message templates
   - Supports variable substitution

### Implementation Details

- **Frontend Service**: `src/lib/api/whatsapp.service.ts`
  - Provides methods for interacting with WhatsApp-related API endpoints
  - Manages templates and messages
  - Handles message sending

- **Backend Service**: `backend/src/services/whatsapp.service.ts`
  - Integrates with WhatsApp Business API providers
  - Supports Twilio and Meta API
  - Handles message formatting and sending
  - Manages message status tracking

### Supported Providers

1. **Twilio**
   - WhatsApp Business API integration
   - Message status tracking
   - Media attachment support

2. **Meta (Facebook)**
   - Direct WhatsApp Business API integration
   - Template message support
   - Interactive message components

### Database Models

- **WhatsAppSettings**: Stores WhatsApp integration settings
- **WhatsAppTemplate**: Stores message templates
- **WhatsAppMessage**: Tracks sent messages and their status

## Integration Configuration

### Environment Variables

```
# Stripe Integration
STRIPE_SECRET_KEY=sk_test_...
STRIPE_PUBLIC_KEY=pk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# PayPal Integration
PAYPAL_CLIENT_ID=...
PAYPAL_CLIENT_SECRET=...
PAYPAL_ENVIRONMENT=sandbox

# BillPlz Integration
BILLPLZ_API_KEY=...
BILLPLZ_COLLECTION_ID=...
BILLPLZ_X_SIGNATURE=...

# LHDN MyInvois API
LHDN_API_BASE_URL=https://api.myinvois.hasil.gov.my/api/v1.0
LHDN_CLIENT_ID=...
LHDN_CLIENT_SECRET=...
LHDN_API_KEY=...

# WhatsApp Integration (Twilio)
TWILIO_ACCOUNT_SID=...
TWILIO_AUTH_TOKEN=...
TWILIO_PHONE_NUMBER=...

# WhatsApp Integration (Meta)
META_API_TOKEN=...
META_PHONE_NUMBER_ID=...
```

### Security Considerations

1. **API Credentials**
   - All API keys and secrets are stored securely
   - Different credentials for development and production

2. **Webhook Verification**
   - All webhooks verify signatures to prevent tampering
   - Rate limiting to prevent abuse

3. **Error Handling**
   - Comprehensive error logging
   - Graceful fallbacks for API failures

4. **Data Protection**
   - Sensitive data is encrypted
   - PCI compliance for payment processing

## Testing

### Test Accounts

1. **Stripe**
   - Test mode with test API keys
   - Test card numbers for different scenarios

2. **PayPal**
   - Sandbox environment
   - Test accounts for buyer and seller

3. **LHDN**
   - Sandbox environment for testing
   - Test certificates

4. **WhatsApp**
   - Test phone numbers
   - Sandbox environment for Twilio

### Test Scenarios

1. **Payment Processing**
   - Successful payment
   - Failed payment
   - Refund processing

2. **LHDN Submission**
   - Successful submission
   - Validation errors
   - Certificate issues

3. **WhatsApp Messaging**
   - Message delivery
   - Template rendering
   - Media attachment

## Deployment Considerations

1. **API Rate Limits**
   - Implement rate limiting for all external APIs
   - Queue system for high-volume operations

2. **Webhook Reliability**
   - Implement retry mechanism for webhook processing
   - Store webhook events for manual processing if needed

3. **Monitoring**
   - Monitor API response times
   - Alert on high error rates
   - Track usage against quotas

4. **Fallbacks**
   - Implement fallback mechanisms for critical operations
   - Graceful degradation when services are unavailable
