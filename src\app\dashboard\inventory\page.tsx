'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { PlusCircle, Package, Warehouse, ArrowDownUp, AlertTriangle } from 'lucide-react';
import { DataTable } from '@/components/ui/data-table';
import { ColumnDef } from '@tanstack/react-table';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import { useRouter } from 'next/navigation';

// Mock data for demonstration
const mockProducts = [
  {
    id: '1',
    sku: 'PROD-001',
    name: 'Product 1',
    category: 'Electronics',
    unitPrice: 100.00,
    costPrice: 80.00,
    taxRate: 6,
    isActive: true,
    createdAt: new Date().toISOString(),
  },
  {
    id: '2',
    sku: 'PROD-002',
    name: 'Product 2',
    category: 'Office Supplies',
    unitPrice: 50.00,
    costPrice: 30.00,
    taxRate: 6,
    isActive: true,
    createdAt: new Date().toISOString(),
  },
];

const mockInventory = [
  {
    id: '1',
    productId: '1',
    productName: 'Product 1',
    warehouseId: '1',
    warehouseName: 'Main Warehouse',
    quantity: 100,
    reorderLevel: 20,
    status: 'In Stock',
  },
  {
    id: '2',
    productId: '2',
    productName: 'Product 2',
    warehouseId: '1',
    warehouseName: 'Main Warehouse',
    quantity: 5,
    reorderLevel: 10,
    status: 'Low Stock',
  },
];

const mockWarehouses = [
  {
    id: '1',
    name: 'Main Warehouse',
    location: 'Kuala Lumpur',
    isDefault: true,
  },
  {
    id: '2',
    name: 'Secondary Warehouse',
    location: 'Penang',
    isDefault: false,
  },
];

const mockTransactions = [
  {
    id: '1',
    type: 'PURCHASE',
    quantity: 50,
    productName: 'Product 1',
    warehouseName: 'Main Warehouse',
    date: new Date().toISOString(),
    notes: 'Initial stock',
  },
  {
    id: '2',
    type: 'SALE',
    quantity: 10,
    productName: 'Product 1',
    warehouseName: 'Main Warehouse',
    date: new Date().toISOString(),
    notes: 'Invoice #INV-001',
  },
];

// Column definitions for tables
const productColumns: ColumnDef<any>[] = [
  {
    accessorKey: 'sku',
    header: 'SKU',
  },
  {
    accessorKey: 'name',
    header: 'Name',
  },
  {
    accessorKey: 'category',
    header: 'Category',
  },
  {
    accessorKey: 'unitPrice',
    header: 'Unit Price',
    cell: ({ row }) => {
      return <div>RM {row.getValue('unitPrice').toFixed(2)}</div>;
    },
  },
  {
    accessorKey: 'isActive',
    header: 'Status',
    cell: ({ row }) => {
      return row.getValue('isActive') ? (
        <Badge className="bg-green-500">Active</Badge>
      ) : (
        <Badge variant="outline">Inactive</Badge>
      );
    },
  },
  {
    id: 'actions',
    cell: ({ row }) => {
      return (
        <div className="flex items-center justify-end gap-2">
          <Button variant="ghost" size="sm">
            Edit
          </Button>
          <Button variant="ghost" size="sm" className="text-red-500">
            Delete
          </Button>
        </div>
      );
    },
  },
];

const inventoryColumns: ColumnDef<any>[] = [
  {
    accessorKey: 'productName',
    header: 'Product',
  },
  {
    accessorKey: 'warehouseName',
    header: 'Warehouse',
  },
  {
    accessorKey: 'quantity',
    header: 'Quantity',
  },
  {
    accessorKey: 'reorderLevel',
    header: 'Reorder Level',
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.getValue('status');
      return status === 'Low Stock' ? (
        <Badge variant="destructive">Low Stock</Badge>
      ) : (
        <Badge className="bg-green-500">In Stock</Badge>
      );
    },
  },
  {
    id: 'actions',
    cell: ({ row }) => {
      return (
        <div className="flex items-center justify-end gap-2">
          <Button variant="ghost" size="sm">
            Adjust
          </Button>
          <Button variant="ghost" size="sm">
            View History
          </Button>
        </div>
      );
    },
  },
];

const warehouseColumns: ColumnDef<any>[] = [
  {
    accessorKey: 'name',
    header: 'Name',
  },
  {
    accessorKey: 'location',
    header: 'Location',
  },
  {
    accessorKey: 'isDefault',
    header: 'Default',
    cell: ({ row }) => {
      return row.getValue('isDefault') ? (
        <Badge className="bg-blue-500">Default</Badge>
      ) : (
        <span>-</span>
      );
    },
  },
  {
    id: 'actions',
    cell: ({ row }) => {
      return (
        <div className="flex items-center justify-end gap-2">
          <Button variant="ghost" size="sm">
            Edit
          </Button>
          <Button variant="ghost" size="sm" className="text-red-500">
            Delete
          </Button>
        </div>
      );
    },
  },
];

const transactionColumns: ColumnDef<any>[] = [
  {
    accessorKey: 'type',
    header: 'Type',
    cell: ({ row }) => {
      const type = row.getValue('type');
      return type === 'PURCHASE' ? (
        <Badge className="bg-green-500">Purchase</Badge>
      ) : type === 'SALE' ? (
        <Badge className="bg-blue-500">Sale</Badge>
      ) : (
        <Badge variant="outline">{type}</Badge>
      );
    },
  },
  {
    accessorKey: 'productName',
    header: 'Product',
  },
  {
    accessorKey: 'quantity',
    header: 'Quantity',
  },
  {
    accessorKey: 'warehouseName',
    header: 'Warehouse',
  },
  {
    accessorKey: 'date',
    header: 'Date',
    cell: ({ row }) => {
      return new Date(row.getValue('date')).toLocaleDateString();
    },
  },
  {
    accessorKey: 'notes',
    header: 'Notes',
  },
];

export default function InventoryPage() {
  const { toast } = useToast();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('products');
  const [searchTerm, setSearchTerm] = useState('');

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between bg-white p-6 rounded-lg shadow-sm mb-6">
        <div>
          <h1 className="text-2xl font-bold text-text-primary flex items-center">
            <svg className="w-6 h-6 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
            </svg>
            Inventory Management
          </h1>
          <p className="mt-1 text-sm text-text-secondary">
            Manage your products, inventory, and warehouses
          </p>
        </div>
        <div className="mt-4 md:mt-0 flex gap-2">
          {activeTab === 'products' && (
            <Button size="sm" className="bg-indigo-600 hover:bg-indigo-700" onClick={() => toast({ title: 'Add Product clicked' })}>
              <PlusCircle className="mr-2 h-4 w-4" /> Add Product
            </Button>
          )}
          {activeTab === 'inventory' && (
            <Button size="sm" className="bg-indigo-600 hover:bg-indigo-700" onClick={() => toast({ title: 'Adjust Stock clicked' })}>
              <ArrowDownUp className="mr-2 h-4 w-4" /> Adjust Stock
            </Button>
          )}
          {activeTab === 'warehouses' && (
            <Button size="sm" className="bg-indigo-600 hover:bg-indigo-700" onClick={() => toast({ title: 'Add Warehouse clicked' })}>
              <PlusCircle className="mr-2 h-4 w-4" /> Add Warehouse
            </Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <Card className="border-none shadow-md">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-text-secondary">Total Products</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Package className="mr-2 h-4 w-4 text-muted-foreground" />
              <div className="text-2xl font-bold">{mockProducts.length}</div>
            </div>
          </CardContent>
        </Card>
        <Card className="border-none shadow-md">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-text-secondary">Total Warehouses</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Warehouse className="mr-2 h-4 w-4 text-muted-foreground" />
              <div className="text-2xl font-bold">{mockWarehouses.length}</div>
            </div>
          </CardContent>
        </Card>
        <Card className="border-none shadow-md">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-text-secondary">Low Stock Items</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <AlertTriangle className="mr-2 h-4 w-4 text-muted-foreground" />
              <div className="text-2xl font-bold">
                {mockInventory.filter(item => item.status === 'Low Stock').length}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <div className="flex flex-col md:flex-row gap-4 my-6">
        <div className="flex-1 relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg className="h-5 w-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <Input
            type="text"
            placeholder={`Search ${activeTab}...`}
            className="pl-10 border-gray-200 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 rounded-md shadow-sm"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      <Tabs defaultValue="products" onValueChange={setActiveTab}>
        <TabsList className="mb-6 bg-white p-1 rounded-lg border">
          <TabsTrigger value="products" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
            </svg>
            Products
          </TabsTrigger>
          <TabsTrigger value="inventory" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            Inventory
          </TabsTrigger>
          <TabsTrigger value="warehouses" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
            Warehouses
          </TabsTrigger>
          <TabsTrigger value="transactions" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
            </svg>
            Transactions
          </TabsTrigger>
        </TabsList>

          <TabsContent value="products">
            <Card className="border-none shadow-md">
              <CardHeader className="pb-2 border-b">
                <CardTitle className="text-lg font-bold flex items-center">
                  <svg className="w-5 h-5 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                  </svg>
                  Products
                </CardTitle>
                <CardDescription className="text-text-secondary">Manage your product catalog</CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <DataTable columns={productColumns} data={mockProducts} />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="inventory">
            <Card className="border-none shadow-md">
              <CardHeader className="pb-2 border-b">
                <CardTitle className="text-lg font-bold flex items-center">
                  <svg className="w-5 h-5 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                  Inventory
                </CardTitle>
                <CardDescription className="text-text-secondary">Track stock levels across warehouses</CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <DataTable columns={inventoryColumns} data={mockInventory} />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="warehouses">
            <Card className="border-none shadow-md">
              <CardHeader className="pb-2 border-b">
                <CardTitle className="text-lg font-bold flex items-center">
                  <svg className="w-5 h-5 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                  Warehouses
                </CardTitle>
                <CardDescription className="text-text-secondary">Manage your storage locations</CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <DataTable columns={warehouseColumns} data={mockWarehouses} />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="transactions">
            <Card className="border-none shadow-md">
              <CardHeader className="pb-2 border-b">
                <CardTitle className="text-lg font-bold flex items-center">
                  <svg className="w-5 h-5 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
                  </svg>
                  Inventory Transactions
                </CardTitle>
                <CardDescription className="text-text-secondary">Track inventory movements</CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <DataTable columns={transactionColumns} data={mockTransactions} />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
    </div>
  );
}
