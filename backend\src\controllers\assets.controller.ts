import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { handleError } from '../utils/errorHandler';

const prisma = new PrismaClient();

// Fixed Asset Controllers
export const createFixedAsset = async (req: Request, res: Response) => {
  try {
    const {
      assetNumber,
      name,
      description,
      category,
      purchaseDate,
      purchasePrice,
      location,
      assignedTo
    } = req.body;

    const tenantId = req.user.tenantId;

    // Initial current value is the purchase price
    const currentValue = parseFloat(purchasePrice);

    const fixedAsset = await prisma.fixedAsset.create({
      data: {
        assetNumber,
        name,
        description,
        category,
        purchaseDate: new Date(purchaseDate),
        purchasePrice: parseFloat(purchasePrice),
        currentValue,
        location,
        assignedTo,
        tenant: {
          connect: { id: tenantId }
        }
      }
    });

    res.status(201).json(fixedAsset);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const getFixedAssets = async (req: Request, res: Response) => {
  try {
    const tenantId = req.user.tenantId;
    const { category, status } = req.query;

    const whereClause: any = { tenantId };

    if (category) {
      whereClause.category = category as string;
    }

    if (status) {
      whereClause.status = status as string;
    }

    const fixedAssets = await prisma.fixedAsset.findMany({
      where: whereClause,
      orderBy: {
        name: 'asc'
      }
    });

    res.status(200).json(fixedAssets);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const getFixedAsset = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenantId;

    const fixedAsset = await prisma.fixedAsset.findFirst({
      where: {
        id,
        tenantId
      },
      include: {
        depreciations: {
          orderBy: {
            depreciationDate: 'desc'
          }
        },
        maintenances: {
          orderBy: {
            maintenanceDate: 'desc'
          }
        }
      }
    });

    if (!fixedAsset) {
      return res.status(404).json({ message: 'Fixed asset not found' });
    }

    res.status(200).json(fixedAsset);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const updateFixedAsset = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const {
      assetNumber,
      name,
      description,
      category,
      purchaseDate,
      purchasePrice,
      currentValue,
      location,
      assignedTo,
      status,
      disposalDate,
      disposalValue
    } = req.body;

    const tenantId = req.user.tenantId;

    const fixedAsset = await prisma.fixedAsset.findFirst({
      where: {
        id,
        tenantId
      }
    });

    if (!fixedAsset) {
      return res.status(404).json({ message: 'Fixed asset not found' });
    }

    const updatedFixedAsset = await prisma.fixedAsset.update({
      where: { id },
      data: {
        assetNumber,
        name,
        description,
        category,
        purchaseDate: purchaseDate ? new Date(purchaseDate) : undefined,
        purchasePrice: purchasePrice ? parseFloat(purchasePrice) : undefined,
        currentValue: currentValue ? parseFloat(currentValue) : undefined,
        location,
        assignedTo,
        status,
        disposalDate: disposalDate ? new Date(disposalDate) : null,
        disposalValue: disposalValue ? parseFloat(disposalValue) : null
      }
    });

    res.status(200).json(updatedFixedAsset);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const deleteFixedAsset = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenantId;

    const fixedAsset = await prisma.fixedAsset.findFirst({
      where: {
        id,
        tenantId
      },
      include: {
        depreciations: true,
        maintenances: true
      }
    });

    if (!fixedAsset) {
      return res.status(404).json({ message: 'Fixed asset not found' });
    }

    // Delete related records first
    await prisma.assetDepreciation.deleteMany({
      where: {
        assetId: id
      }
    });

    await prisma.assetMaintenance.deleteMany({
      where: {
        assetId: id
      }
    });

    // Then delete the asset
    await prisma.fixedAsset.delete({
      where: { id }
    });

    res.status(200).json({ message: 'Fixed asset deleted successfully' });
  } catch (error) {
    handleError(error, req, res);
  }
};

// Asset Depreciation Controllers
export const createAssetDepreciation = async (req: Request, res: Response) => {
  try {
    const {
      assetId,
      depreciationDate,
      depreciationAmount,
      notes
    } = req.body;

    const tenantId = req.user.tenantId;

    // Get the asset
    const asset = await prisma.fixedAsset.findFirst({
      where: {
        id: assetId,
        tenantId
      }
    });

    if (!asset) {
      return res.status(404).json({ message: 'Fixed asset not found' });
    }

    // Calculate new book value
    const bookValue = asset.currentValue - parseFloat(depreciationAmount);

    if (bookValue < 0) {
      return res.status(400).json({ message: 'Depreciation amount cannot exceed current asset value' });
    }

    // Create depreciation record and update asset in a transaction
    const result = await prisma.$transaction([
      // Create depreciation record
      prisma.assetDepreciation.create({
        data: {
          depreciationDate: new Date(depreciationDate),
          depreciationAmount: parseFloat(depreciationAmount),
          bookValue,
          notes,
          asset: {
            connect: { id: assetId }
          },
          tenant: {
            connect: { id: tenantId }
          }
        }
      }),
      // Update asset current value
      prisma.fixedAsset.update({
        where: { id: assetId },
        data: {
          currentValue: bookValue
        }
      })
    ]);

    res.status(201).json(result[0]);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const getAssetDepreciations = async (req: Request, res: Response) => {
  try {
    const tenantId = req.user.tenantId;
    const { assetId } = req.query;

    const whereClause: any = { tenantId };

    if (assetId) {
      whereClause.assetId = assetId as string;
    }

    const depreciations = await prisma.assetDepreciation.findMany({
      where: whereClause,
      include: {
        asset: true
      },
      orderBy: {
        depreciationDate: 'desc'
      }
    });

    res.status(200).json(depreciations);
  } catch (error) {
    handleError(error, req, res);
  }
};

// Asset Maintenance Controllers
export const createAssetMaintenance = async (req: Request, res: Response) => {
  try {
    const {
      assetId,
      maintenanceDate,
      description,
      cost,
      provider,
      nextMaintenanceDate
    } = req.body;

    const tenantId = req.user.tenantId;

    // Get the asset
    const asset = await prisma.fixedAsset.findFirst({
      where: {
        id: assetId,
        tenantId
      }
    });

    if (!asset) {
      return res.status(404).json({ message: 'Fixed asset not found' });
    }

    // Create maintenance record
    const maintenance = await prisma.assetMaintenance.create({
      data: {
        maintenanceDate: new Date(maintenanceDate),
        description,
        cost: parseFloat(cost),
        provider,
        nextMaintenanceDate: nextMaintenanceDate ? new Date(nextMaintenanceDate) : null,
        asset: {
          connect: { id: assetId }
        },
        tenant: {
          connect: { id: tenantId }
        }
      }
    });

    // Update asset status if it was in maintenance
    if (asset.status === 'MAINTENANCE') {
      await prisma.fixedAsset.update({
        where: { id: assetId },
        data: {
          status: 'ACTIVE'
        }
      });
    }

    res.status(201).json(maintenance);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const getAssetMaintenances = async (req: Request, res: Response) => {
  try {
    const tenantId = req.user.tenantId;
    const { assetId } = req.query;

    const whereClause: any = { tenantId };

    if (assetId) {
      whereClause.assetId = assetId as string;
    }

    const maintenances = await prisma.assetMaintenance.findMany({
      where: whereClause,
      include: {
        asset: true
      },
      orderBy: {
        maintenanceDate: 'desc'
      }
    });

    res.status(200).json(maintenances);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const updateAssetMaintenance = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const {
      maintenanceDate,
      description,
      cost,
      provider,
      nextMaintenanceDate
    } = req.body;

    const tenantId = req.user.tenantId;

    const maintenance = await prisma.assetMaintenance.findFirst({
      where: {
        id,
        tenantId
      }
    });

    if (!maintenance) {
      return res.status(404).json({ message: 'Maintenance record not found' });
    }

    const updatedMaintenance = await prisma.assetMaintenance.update({
      where: { id },
      data: {
        maintenanceDate: maintenanceDate ? new Date(maintenanceDate) : undefined,
        description,
        cost: cost ? parseFloat(cost) : undefined,
        provider,
        nextMaintenanceDate: nextMaintenanceDate ? new Date(nextMaintenanceDate) : null
      }
    });

    res.status(200).json(updatedMaintenance);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const deleteAssetMaintenance = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenantId;

    const maintenance = await prisma.assetMaintenance.findFirst({
      where: {
        id,
        tenantId
      }
    });

    if (!maintenance) {
      return res.status(404).json({ message: 'Maintenance record not found' });
    }

    await prisma.assetMaintenance.delete({
      where: { id }
    });

    res.status(200).json({ message: 'Maintenance record deleted successfully' });
  } catch (error) {
    handleError(error, req, res);
  }
};
