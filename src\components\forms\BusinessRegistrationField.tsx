'use client';

import { useState } from 'react';
import { cn } from '@/lib/utils';
import { validateBusinessRegistration } from '@/lib/api/validation';

interface BusinessRegistrationFieldProps {
  value: string;
  onChange: (value: string, isValid: boolean, businessName?: string) => void;
  required?: boolean;
  className?: string;
  autoValidate?: boolean;
}

export default function BusinessRegistrationField({
  value,
  onChange,
  required = true,
  className,
  autoValidate = false,
}: BusinessRegistrationFieldProps) {
  const [isValidating, setIsValidating] = useState(false);
  const [validationResult, setValidationResult] = useState<{
    isValid: boolean;
    validated: boolean;
    businessName?: string;
  }>({
    isValid: false,
    validated: false,
  });

  const validateBusinessReg = async (regNo: string) => {
    if (!regNo.trim()) {
      setValidationResult({
        isValid: false,
        validated: true,
        businessName: undefined,
      });
      onChange(regNo, false);
      return;
    }

    setIsValidating(true);

    try {
      // Call the backend API to validate the business registration number
      const data = await validateBusinessRegistration(regNo);

      // Since we're using the API client, we don't need to check response.ok
      const result = {
        isValid: data.isValid,
        validated: true,
        businessName: data.businessName,
      };

      setValidationResult(result);

      onChange(regNo, data.isValid, data.businessName);
    } catch (error) {
      console.error('Error validating Business Registration Number:', error);
      setValidationResult({
        isValid: false,
        validated: true,
        businessName: undefined,
      });

      onChange(regNo, false);
    } finally {
      setIsValidating(false);
    }
  };

  return (
    <div className={cn("space-y-2", className)}>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Business Registration Number {required && <span className="text-red-500">*</span>}
        </label>
        <div className="relative">
          <input
            type="text"
            value={value}
            onChange={(e) => {
              const newValue = e.target.value;
              onChange(newValue, validationResult.isValid);

              // If autoValidate is enabled, validate after a short delay
              if (autoValidate) {
                setValidationResult(prev => ({ ...prev, validated: false }));
                if (newValue.length >= 8) {
                  const timer = setTimeout(() => {
                    validateBusinessReg(newValue);
                  }, 800);
                  return () => clearTimeout(timer);
                }
              }
            }}
            onBlur={() => {
              if (value && !validationResult.validated) {
                validateBusinessReg(value);
              }
            }}
            placeholder="Enter your business registration number"
            className={cn(
              "block w-full px-4 py-2 border rounded-md shadow-sm focus:ring-2 focus:ring-offset-2 focus:outline-none sm:text-sm",
              validationResult.validated
                ? validationResult.isValid
                  ? "border-green-300 focus:border-green-500 focus:ring-green-500"
                  : "border-red-300 focus:border-red-500 focus:ring-red-500"
                : "border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
            )}
          />
          {isValidating && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <svg className="animate-spin h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </div>
          )}
        </div>

        {validationResult.validated && (
          <div className="mt-1">
            {validationResult.isValid ? (
              <p className="text-xs text-green-600 flex items-center">
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Valid business: {validationResult.businessName}
              </p>
            ) : (
              <p className="text-xs text-red-600 flex items-center">
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
                Invalid business registration number
              </p>
            )}
          </div>
        )}

        <p className="mt-1 text-xs text-gray-500">
          Your business registration number will be used for tax compliance with LHDN MyInvois.
        </p>
      </div>
    </div>
  );
}
