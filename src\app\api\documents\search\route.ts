import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Get search parameters from query string
    const searchParams = request.nextUrl.searchParams;
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const status = searchParams.get('status');
    const documentType = searchParams.get('documentType');
    const page = searchParams.get('page') ? parseInt(searchParams.get('page')!) : 1;
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 10;
    
    // In a real implementation, this would call your backend API
    // const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/documents/search`, {
    //   method: 'GET',
    //   headers: {
    //     'Content-Type': 'application/json',
    //     'Authorization': `Bearer ${session.accessToken}`,
    //   },
    // });
    // const data = await response.json();
    
    // For development, use mock data
    const mockDocuments = [
      {
        documentId: 'DOC-001',
        documentNumber: 'INV-2025-001',
        documentType: 'INVOICE',
        issueDate: '2025-01-15',
        status: 'VALID',
        customerName: 'Acme Corporation',
        totalAmount: 2500.00,
      },
      {
        documentId: 'DOC-002',
        documentNumber: 'INV-2025-002',
        documentType: 'INVOICE',
        issueDate: '2025-01-20',
        status: 'VALID',
        customerName: 'Wayne Enterprises',
        totalAmount: 4200.00,
      },
      {
        documentId: 'DOC-003',
        documentNumber: 'INV-2025-003',
        documentType: 'INVOICE',
        issueDate: '2025-01-25',
        status: 'CANCELLED',
        customerName: 'Stark Industries',
        totalAmount: 1800.00,
      },
    ];
    
    // Filter documents based on search parameters
    let filteredDocuments = [...mockDocuments];
    
    if (startDate) {
      filteredDocuments = filteredDocuments.filter(doc => doc.issueDate >= startDate);
    }
    
    if (endDate) {
      filteredDocuments = filteredDocuments.filter(doc => doc.issueDate <= endDate);
    }
    
    if (status) {
      filteredDocuments = filteredDocuments.filter(doc => doc.status === status);
    }
    
    if (documentType) {
      filteredDocuments = filteredDocuments.filter(doc => doc.documentType === documentType);
    }
    
    // Paginate results
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    const paginatedDocuments = filteredDocuments.slice(startIndex, endIndex);
    
    return NextResponse.json({
      documents: paginatedDocuments,
      pagination: {
        page,
        limit,
        total: filteredDocuments.length,
        totalPages: Math.ceil(filteredDocuments.length / limit),
      },
    });
  } catch (error) {
    console.error('Error searching documents:', error);
    return NextResponse.json(
      { message: 'Failed to search documents' },
      { status: 500 }
    );
  }
}
