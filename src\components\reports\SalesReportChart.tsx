'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface SalesReportChartProps {
  reportType: string;
  dateRange: string;
  startDate?: string;
  endDate?: string;
}

export default function SalesReportChart({ 
  reportType, 
  dateRange,
  startDate,
  endDate 
}: SalesReportChartProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [chartView, setChartView] = useState<'chart' | 'table'>('chart');
  
  // Mock sales data
  const salesByCustomerData = {
    customers: [
      { name: 'Acme Corporation', sales: 45000, invoices: 12, percentage: 22.5 },
      { name: 'Wayne Enterprises', sales: 38000, invoices: 8, percentage: 19 },
      { name: 'Stark Industries', sales: 32000, invoices: 6, percentage: 16 },
      { name: 'Daily Planet', sales: 28000, invoices: 10, percentage: 14 },
      { name: 'LexCorp', sales: 25000, invoices: 5, percentage: 12.5 },
      { name: 'Others', sales: 32000, invoices: 15, percentage: 16 },
    ],
    total: 200000,
    totalInvoices: 56,
  };
  
  const salesByProductData = {
    products: [
      { name: 'Product A', sales: 52000, quantity: 120, percentage: 26 },
      { name: 'Product B', sales: 45000, quantity: 90, percentage: 22.5 },
      { name: 'Product C', sales: 38000, quantity: 75, percentage: 19 },
      { name: 'Product D', sales: 35000, quantity: 70, percentage: 17.5 },
      { name: 'Product E', sales: 30000, quantity: 60, percentage: 15 },
    ],
    total: 200000,
    totalQuantity: 415,
  };
  
  const salesByMonthData = {
    months: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    sales: [28000, 32000, 30000, 35000, 38000, 37000],
    growth: [null, 14.3, -6.3, 16.7, 8.6, -2.6],
    total: 200000,
    averageMonthly: 33333,
  };
  
  const invoiceStatusData = {
    statuses: [
      { status: 'Paid', count: 42, amount: 150000, percentage: 75 },
      { status: 'Pending', count: 8, amount: 35000, percentage: 17.5 },
      { status: 'Overdue', count: 4, amount: 12000, percentage: 6 },
      { status: 'Cancelled', count: 2, amount: 3000, percentage: 1.5 },
    ],
    total: 56,
    totalAmount: 200000,
  };
  
  const paymentCollectionData = {
    methods: [
      { method: 'Bank Transfer', count: 25, amount: 120000, percentage: 60 },
      { method: 'Credit Card', count: 15, amount: 60000, percentage: 30 },
      { method: 'Cash', count: 2, amount: 20000, percentage: 10 },
    ],
    timeToPayment: {
      average: 12, // days
      fastest: 1,
      slowest: 45,
    },
    aging: [
      { range: '0-15 days', amount: 35000, percentage: 17.5 },
      { range: '16-30 days', amount: 8000, percentage: 4 },
      { range: '31-60 days', amount: 4000, percentage: 2 },
      { range: '60+ days', amount: 3000, percentage: 1.5 },
    ],
    totalCollected: 150000,
    totalOutstanding: 50000,
  };

  useEffect(() => {
    // Simulate loading data
    setIsLoading(true);
    
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1500);
    
    return () => clearTimeout(timer);
  }, [reportType, dateRange, startDate, endDate]);

  const getReportTitle = () => {
    switch (reportType) {
      case 'sales-by-customer':
        return 'Sales by Customer';
      case 'sales-by-product':
        return 'Sales by Product';
      case 'sales-by-month':
        return 'Sales by Month';
      case 'invoice-status':
        return 'Invoice Status';
      case 'payment-collection':
        return 'Payment Collection';
      default:
        return 'Sales Report';
    }
  };

  const getDateRangeText = () => {
    switch (dateRange) {
      case 'today':
        return 'Today';
      case 'yesterday':
        return 'Yesterday';
      case 'this-week':
        return 'This Week';
      case 'last-week':
        return 'Last Week';
      case 'this-month':
        return 'This Month';
      case 'last-month':
        return 'Last Month';
      case 'this-quarter':
        return 'This Quarter';
      case 'last-quarter':
        return 'Last Quarter';
      case 'this-year':
        return 'This Year';
      case 'last-year':
        return 'Last Year';
      case 'custom':
        return `${startDate} to ${endDate}`;
      default:
        return 'Custom Period';
    }
  };

  const renderSalesByCustomerReport = () => {
    if (chartView === 'chart') {
      return (
        <div className="space-y-6">
          <div className="h-64 flex items-center justify-center">
            <div className="w-64 h-64 relative">
              <svg viewBox="0 0 100 100" className="w-full h-full">
                <circle cx="50" cy="50" r="40" fill="none" stroke="#e5e7eb" strokeWidth="20" />
                
                {salesByCustomerData.customers.map((customer, index) => {
                  const colors = ['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#6b7280'];
                  const offset = salesByCustomerData.customers
                    .slice(0, index)
                    .reduce((acc, curr) => acc + curr.percentage, 0);
                  
                  return (
                    <circle 
                      key={customer.name}
                      cx="50" 
                      cy="50" 
                      r="40" 
                      fill="none" 
                      stroke={colors[index % colors.length]} 
                      strokeWidth="20" 
                      strokeDasharray={`${customer.percentage * 2.51} 251.2`} 
                      strokeDashoffset={251.2 - (offset * 2.51)} 
                      transform="rotate(-90 50 50)" 
                    />
                  );
                })}
              </svg>
            </div>
          </div>
          <div className="flex flex-wrap justify-center gap-2">
            {salesByCustomerData.customers.map((customer, index) => {
              const colors = ['bg-blue-500', 'bg-red-500', 'bg-green-500', 'bg-yellow-500', 'bg-purple-500', 'bg-gray-500'];
              
              return (
                <div key={customer.name} className="flex items-center">
                  <div className={`w-3 h-3 ${colors[index % colors.length]} rounded-full mr-1`}></div>
                  <span className="text-xs">{customer.name} ({customer.percentage}%)</span>
                </div>
              );
            })}
          </div>
        </div>
      );
    } else {
      return (
        <div className="space-y-4">
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2">Customer</th>
                  <th className="text-right py-2">Sales</th>
                  <th className="text-right py-2">Invoices</th>
                  <th className="text-right py-2">Percentage</th>
                </tr>
              </thead>
              <tbody>
                {salesByCustomerData.customers.map((customer) => (
                  <tr key={customer.name} className="border-b">
                    <td className="py-2">{customer.name}</td>
                    <td className="text-right py-2">RM {customer.sales.toLocaleString()}</td>
                    <td className="text-right py-2">{customer.invoices}</td>
                    <td className="text-right py-2">{customer.percentage}%</td>
                  </tr>
                ))}
              </tbody>
              <tfoot>
                <tr className="font-bold">
                  <td className="py-2">Total</td>
                  <td className="text-right py-2">RM {salesByCustomerData.total.toLocaleString()}</td>
                  <td className="text-right py-2">{salesByCustomerData.totalInvoices}</td>
                  <td className="text-right py-2">100%</td>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>
      );
    }
  };

  const renderSalesByProductReport = () => {
    if (chartView === 'chart') {
      return (
        <div className="space-y-6">
          <div className="h-64 flex items-end justify-between px-4">
            {salesByProductData.products.map((product) => (
              <div key={product.name} className="flex flex-col items-center">
                <div 
                  className="w-12 bg-blue-500 rounded-t" 
                  style={{ height: `${(product.sales / salesByProductData.products[0].sales) * 200}px` }}
                ></div>
                <div className="mt-2 text-xs font-medium truncate w-16 text-center">{product.name}</div>
              </div>
            ))}
          </div>
        </div>
      );
    } else {
      return (
        <div className="space-y-4">
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2">Product</th>
                  <th className="text-right py-2">Sales</th>
                  <th className="text-right py-2">Quantity</th>
                  <th className="text-right py-2">Percentage</th>
                </tr>
              </thead>
              <tbody>
                {salesByProductData.products.map((product) => (
                  <tr key={product.name} className="border-b">
                    <td className="py-2">{product.name}</td>
                    <td className="text-right py-2">RM {product.sales.toLocaleString()}</td>
                    <td className="text-right py-2">{product.quantity}</td>
                    <td className="text-right py-2">{product.percentage}%</td>
                  </tr>
                ))}
              </tbody>
              <tfoot>
                <tr className="font-bold">
                  <td className="py-2">Total</td>
                  <td className="text-right py-2">RM {salesByProductData.total.toLocaleString()}</td>
                  <td className="text-right py-2">{salesByProductData.totalQuantity}</td>
                  <td className="text-right py-2">100%</td>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>
      );
    }
  };

  const renderSalesByMonthReport = () => {
    if (chartView === 'chart') {
      return (
        <div className="space-y-6">
          <div className="h-64 flex items-end justify-between px-4">
            {salesByMonthData.months.map((month, index) => (
              <div key={month} className="flex flex-col items-center">
                <div 
                  className="w-12 bg-blue-500 rounded-t" 
                  style={{ height: `${(salesByMonthData.sales[index] / Math.max(...salesByMonthData.sales)) * 200}px` }}
                ></div>
                <div className="mt-2 text-xs font-medium">{month}</div>
                {salesByMonthData.growth[index] !== null && (
                  <div className={`text-xs ${salesByMonthData.growth[index]! >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {salesByMonthData.growth[index]! >= 0 ? '+' : ''}{salesByMonthData.growth[index]}%
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      );
    } else {
      return (
        <div className="space-y-4">
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2">Month</th>
                  <th className="text-right py-2">Sales</th>
                  <th className="text-right py-2">Growth</th>
                </tr>
              </thead>
              <tbody>
                {salesByMonthData.months.map((month, index) => (
                  <tr key={month} className="border-b">
                    <td className="py-2">{month}</td>
                    <td className="text-right py-2">RM {salesByMonthData.sales[index].toLocaleString()}</td>
                    <td className="text-right py-2">
                      {salesByMonthData.growth[index] !== null ? (
                        <span className={salesByMonthData.growth[index]! >= 0 ? 'text-green-600' : 'text-red-600'}>
                          {salesByMonthData.growth[index]! >= 0 ? '+' : ''}{salesByMonthData.growth[index]}%
                        </span>
                      ) : '-'}
                    </td>
                  </tr>
                ))}
              </tbody>
              <tfoot>
                <tr className="font-bold">
                  <td className="py-2">Total</td>
                  <td className="text-right py-2">RM {salesByMonthData.total.toLocaleString()}</td>
                  <td className="text-right py-2">-</td>
                </tr>
                <tr>
                  <td className="py-2">Average</td>
                  <td className="text-right py-2">RM {salesByMonthData.averageMonthly.toLocaleString()}</td>
                  <td className="text-right py-2">-</td>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>
      );
    }
  };

  const renderInvoiceStatusReport = () => {
    if (chartView === 'chart') {
      return (
        <div className="space-y-6">
          <div className="h-64 flex items-center justify-center">
            <div className="w-64 h-64 relative">
              <svg viewBox="0 0 100 100" className="w-full h-full">
                <circle cx="50" cy="50" r="40" fill="none" stroke="#e5e7eb" strokeWidth="20" />
                
                {invoiceStatusData.statuses.map((status, index) => {
                  const colors = ['#10b981', '#f59e0b', '#ef4444', '#6b7280'];
                  const offset = invoiceStatusData.statuses
                    .slice(0, index)
                    .reduce((acc, curr) => acc + curr.percentage, 0);
                  
                  return (
                    <circle 
                      key={status.status}
                      cx="50" 
                      cy="50" 
                      r="40" 
                      fill="none" 
                      stroke={colors[index % colors.length]} 
                      strokeWidth="20" 
                      strokeDasharray={`${status.percentage * 2.51} 251.2`} 
                      strokeDashoffset={251.2 - (offset * 2.51)} 
                      transform="rotate(-90 50 50)" 
                    />
                  );
                })}
              </svg>
            </div>
          </div>
          <div className="flex flex-wrap justify-center gap-2">
            {invoiceStatusData.statuses.map((status, index) => {
              const colors = ['bg-green-500', 'bg-yellow-500', 'bg-red-500', 'bg-gray-500'];
              
              return (
                <div key={status.status} className="flex items-center">
                  <div className={`w-3 h-3 ${colors[index % colors.length]} rounded-full mr-1`}></div>
                  <span className="text-xs">{status.status} ({status.percentage}%)</span>
                </div>
              );
            })}
          </div>
        </div>
      );
    } else {
      return (
        <div className="space-y-4">
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2">Status</th>
                  <th className="text-right py-2">Count</th>
                  <th className="text-right py-2">Amount</th>
                  <th className="text-right py-2">Percentage</th>
                </tr>
              </thead>
              <tbody>
                {invoiceStatusData.statuses.map((status) => (
                  <tr key={status.status} className="border-b">
                    <td className="py-2">
                      <Badge 
                        className={
                          status.status === 'Paid' ? 'bg-green-100 text-green-800' :
                          status.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' :
                          status.status === 'Overdue' ? 'bg-red-100 text-red-800' :
                          'bg-gray-100 text-gray-800'
                        }
                      >
                        {status.status}
                      </Badge>
                    </td>
                    <td className="text-right py-2">{status.count}</td>
                    <td className="text-right py-2">RM {status.amount.toLocaleString()}</td>
                    <td className="text-right py-2">{status.percentage}%</td>
                  </tr>
                ))}
              </tbody>
              <tfoot>
                <tr className="font-bold">
                  <td className="py-2">Total</td>
                  <td className="text-right py-2">{invoiceStatusData.total}</td>
                  <td className="text-right py-2">RM {invoiceStatusData.totalAmount.toLocaleString()}</td>
                  <td className="text-right py-2">100%</td>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>
      );
    }
  };

  const renderPaymentCollectionReport = () => {
    if (chartView === 'chart') {
      return (
        <div className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="text-sm font-medium mb-2">Payment Methods</h3>
              <div className="h-32 flex items-center justify-center">
                <div className="w-32 h-32 relative">
                  <svg viewBox="0 0 100 100" className="w-full h-full">
                    <circle cx="50" cy="50" r="40" fill="none" stroke="#e5e7eb" strokeWidth="20" />
                    
                    {paymentCollectionData.methods.map((method, index) => {
                      const colors = ['#3b82f6', '#ef4444', '#10b981'];
                      const offset = paymentCollectionData.methods
                        .slice(0, index)
                        .reduce((acc, curr) => acc + curr.percentage, 0);
                      
                      return (
                        <circle 
                          key={method.method}
                          cx="50" 
                          cy="50" 
                          r="40" 
                          fill="none" 
                          stroke={colors[index % colors.length]} 
                          strokeWidth="20" 
                          strokeDasharray={`${method.percentage * 2.51} 251.2`} 
                          strokeDashoffset={251.2 - (offset * 2.51)} 
                          transform="rotate(-90 50 50)" 
                        />
                      );
                    })}
                  </svg>
                </div>
              </div>
              <div className="flex flex-wrap justify-center gap-2">
                {paymentCollectionData.methods.map((method, index) => {
                  const colors = ['bg-blue-500', 'bg-red-500', 'bg-green-500'];
                  
                  return (
                    <div key={method.method} className="flex items-center">
                      <div className={`w-3 h-3 ${colors[index % colors.length]} rounded-full mr-1`}></div>
                      <span className="text-xs">{method.method}</span>
                    </div>
                  );
                })}
              </div>
            </div>
            <div>
              <h3 className="text-sm font-medium mb-2">Aging Analysis</h3>
              <div className="h-32 flex items-end justify-between px-4">
                {paymentCollectionData.aging.map((age) => (
                  <div key={age.range} className="flex flex-col items-center">
                    <div 
                      className="w-8 bg-blue-500 rounded-t" 
                      style={{ height: `${(age.amount / paymentCollectionData.aging[0].amount) * 100}px` }}
                    ></div>
                    <div className="mt-2 text-xs font-medium truncate w-16 text-center">{age.range}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4 pt-4 border-t">
            <div>
              <div className="text-sm font-medium">Time to Payment</div>
              <div className="text-2xl font-bold">{paymentCollectionData.timeToPayment.average} days</div>
              <div className="text-xs text-gray-500">
                Range: {paymentCollectionData.timeToPayment.fastest}-{paymentCollectionData.timeToPayment.slowest} days
              </div>
            </div>
            <div>
              <div className="text-sm font-medium">Collection Rate</div>
              <div className="text-2xl font-bold">
                {Math.round((paymentCollectionData.totalCollected / (paymentCollectionData.totalCollected + paymentCollectionData.totalOutstanding)) * 100)}%
              </div>
              <div className="text-xs text-gray-500">
                RM {paymentCollectionData.totalCollected.toLocaleString()} of RM {(paymentCollectionData.totalCollected + paymentCollectionData.totalOutstanding).toLocaleString()}
              </div>
            </div>
          </div>
        </div>
      );
    } else {
      return (
        <div className="space-y-4">
          <div>
            <h3 className="text-sm font-medium mb-2">Payment Methods</h3>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-2">Method</th>
                    <th className="text-right py-2">Count</th>
                    <th className="text-right py-2">Amount</th>
                    <th className="text-right py-2">Percentage</th>
                  </tr>
                </thead>
                <tbody>
                  {paymentCollectionData.methods.map((method) => (
                    <tr key={method.method} className="border-b">
                      <td className="py-2">{method.method}</td>
                      <td className="text-right py-2">{method.count}</td>
                      <td className="text-right py-2">RM {method.amount.toLocaleString()}</td>
                      <td className="text-right py-2">{method.percentage}%</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
          
          <div>
            <h3 className="text-sm font-medium mb-2">Aging Analysis</h3>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-2">Age</th>
                    <th className="text-right py-2">Amount</th>
                    <th className="text-right py-2">Percentage</th>
                  </tr>
                </thead>
                <tbody>
                  {paymentCollectionData.aging.map((age) => (
                    <tr key={age.range} className="border-b">
                      <td className="py-2">{age.range}</td>
                      <td className="text-right py-2">RM {age.amount.toLocaleString()}</td>
                      <td className="text-right py-2">{age.percentage}%</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4 pt-4 border-t">
            <div>
              <div className="text-sm font-medium">Time to Payment</div>
              <div className="text-lg font-bold">{paymentCollectionData.timeToPayment.average} days</div>
              <div className="text-xs text-gray-500">
                Range: {paymentCollectionData.timeToPayment.fastest}-{paymentCollectionData.timeToPayment.slowest} days
              </div>
            </div>
            <div>
              <div className="text-sm font-medium">Collection Rate</div>
              <div className="text-lg font-bold">
                {Math.round((paymentCollectionData.totalCollected / (paymentCollectionData.totalCollected + paymentCollectionData.totalOutstanding)) * 100)}%
              </div>
              <div className="text-xs text-gray-500">
                RM {paymentCollectionData.totalCollected.toLocaleString()} of RM {(paymentCollectionData.totalCollected + paymentCollectionData.totalOutstanding).toLocaleString()}
              </div>
            </div>
          </div>
        </div>
      );
    }
  };

  const renderReportContent = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          <span className="ml-2 text-gray-600">Loading report data...</span>
        </div>
      );
    }

    switch (reportType) {
      case 'sales-by-customer':
        return renderSalesByCustomerReport();
      case 'sales-by-product':
        return renderSalesByProductReport();
      case 'sales-by-month':
        return renderSalesByMonthReport();
      case 'invoice-status':
        return renderInvoiceStatusReport();
      case 'payment-collection':
        return renderPaymentCollectionReport();
      default:
        return (
          <div className="flex items-center justify-center h-64">
            <p className="text-gray-500">Select a report type to view data</p>
          </div>
        );
    }
  };

  return (
    <Card className="h-full">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle>{getReportTitle()}</CardTitle>
            <CardDescription>{getDateRangeText()}</CardDescription>
          </div>
          <div className="flex space-x-2">
            <Button 
              variant={chartView === 'chart' ? 'default' : 'outline'} 
              size="sm"
              onClick={() => setChartView('chart')}
            >
              Chart
            </Button>
            <Button 
              variant={chartView === 'table' ? 'default' : 'outline'} 
              size="sm"
              onClick={() => setChartView('table')}
            >
              Table
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {renderReportContent()}
      </CardContent>
      <CardFooter className="flex justify-between">
        <div className="text-sm text-gray-500">
          Generated on {new Date().toLocaleDateString()}
        </div>
        <Button variant="outline" size="sm">
          Export
        </Button>
      </CardFooter>
    </Card>
  );
}
