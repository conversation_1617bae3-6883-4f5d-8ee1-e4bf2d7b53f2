'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { DatePicker } from '@/components/ui/date-picker';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { BarChart, Bar, LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import aiService from '@/lib/api/ai.service';
import { useToast } from '@/components/ui/use-toast';

export default function AIFinancialInsights() {
  const [isLoading, setIsLoading] = useState(false);
  const [startDate, setStartDate] = useState<Date | undefined>(new Date(new Date().setMonth(new Date().getMonth() - 3)));
  const [endDate, setEndDate] = useState<Date | undefined>(new Date());
  const [predictionPeriod, setPredictionPeriod] = useState('3');
  const [data, setData] = useState<any>(null);
  const { toast } = useToast();

  useEffect(() => {
    generateInsights();
  }, []);

  const generateInsights = async () => {
    setIsLoading(true);

    try {
      const params = {
        startDate: startDate?.toISOString(),
        endDate: endDate?.toISOString(),
        predictionPeriod
      };

      const response = await aiService.getFinancialInsights(params);
      setData(response);

      toast({
        title: 'Financial Insights Generated',
        description: 'AI has analyzed your financial data and generated insights.',
        variant: 'default',
      });
    } catch (error) {
      console.error('Error generating financial insights:', error);
      toast({
        title: 'Error',
        description: 'Failed to generate financial insights. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
        <span className="ml-2 text-gray-600">Generating financial insights...</span>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg border border-dashed border-gray-300">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900">Financial Insights</h3>
          <p className="mt-1 text-sm text-gray-500">
            Generate AI-powered financial analysis and forecasting
          </p>
          <Button onClick={generateInsights} className="mt-4">Generate Financial Insights</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h2 className="text-lg font-semibold flex items-center">
          <svg
            className="w-5 h-5 mr-2 text-indigo-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
            />
          </svg>
          AI Financial Insights
        </h2>
        <div className="flex flex-col sm:flex-row gap-4">
          <DatePicker
            date={startDate}
            setDate={setStartDate}
            label="Start Date"
            className="w-full sm:w-40"
          />
          <DatePicker
            date={endDate}
            setDate={setEndDate}
            label="End Date"
            className="w-full sm:w-40"
          />
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Prediction Period</label>
            <Select value={predictionPeriod} onValueChange={setPredictionPeriod}>
              <SelectTrigger className="w-full sm:w-40">
                <SelectValue placeholder="Select period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">1 Month</SelectItem>
                <SelectItem value="3">3 Months</SelectItem>
                <SelectItem value="6">6 Months</SelectItem>
                <SelectItem value="12">12 Months</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <Button onClick={generateInsights} className="mt-auto">Update</Button>
        </div>
      </div>

      {/* Cash Flow Chart */}
      <Card className="shadow-md">
        <CardHeader className="bg-white border-b pb-3">
          <CardTitle className="text-base font-semibold">Cash Flow Analysis & Prediction</CardTitle>
        </CardHeader>
        <CardContent className="pt-4">
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={[...data.cashFlowHistory, ...data.cashFlowPrediction]}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="income"
                  stroke="#4f46e5"
                  strokeWidth={2}
                  activeDot={{ r: 8 }}
                  name="Income"
                />
                <Line
                  type="monotone"
                  dataKey="expenses"
                  stroke="#ef4444"
                  strokeWidth={2}
                  name="Expenses"
                />
                <Line
                  type="monotone"
                  dataKey="balance"
                  stroke="#10b981"
                  strokeWidth={2}
                  name="Balance"
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
          <div className="mt-2 text-xs text-gray-500 flex items-center">
            <span className="inline-block w-3 h-3 bg-gray-200 mr-1"></span>
            Historical Data
            <span className="inline-block w-3 h-3 bg-indigo-100 mr-1 ml-4"></span>
            AI Prediction
          </div>
        </CardContent>
      </Card>

      {/* Top Expenses and Anomalies */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="shadow-md">
          <CardHeader className="bg-white border-b pb-3">
            <CardTitle className="text-base font-semibold">Top Expense Categories</CardTitle>
          </CardHeader>
          <CardContent className="pt-4">
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={data.topExpenseCategories}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="value" fill="#4f46e5" name="Amount (RM)" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-md">
          <CardHeader className="bg-white border-b pb-3">
            <CardTitle className="text-base font-semibold">Anomaly Detection</CardTitle>
          </CardHeader>
          <CardContent className="pt-4">
            <div className="space-y-4">
              {data.anomalies.map((anomaly: any, index: number) => (
                <div key={index} className="bg-red-50 border border-red-100 rounded-md p-3">
                  <div className="flex items-start">
                    <svg
                      className="w-5 h-5 text-red-500 mt-0.5 mr-2 flex-shrink-0"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                      />
                    </svg>
                    <div>
                      <div className="font-medium text-red-800">{anomaly.category} Anomaly</div>
                      <div className="text-sm text-red-700">{anomaly.description}</div>
                      <div className="mt-1 text-xs text-red-600">
                        <span className="font-medium">Date:</span> {anomaly.date} |
                        <span className="font-medium"> Actual:</span> RM {anomaly.amount} |
                        <span className="font-medium"> Expected:</span> RM {anomaly.expected}
                      </div>
                    </div>
                  </div>
                </div>
              ))}

              {data.anomalies.length === 0 && (
                <div className="flex items-center justify-center h-32 bg-green-50 rounded-md border border-green-100">
                  <div className="text-center">
                    <svg
                      className="w-8 h-8 text-green-500 mx-auto"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                    <p className="mt-2 text-sm font-medium text-green-800">No anomalies detected</p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* AI Insights */}
      <Card className="shadow-md">
        <CardHeader className="bg-white border-b pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-base font-semibold">AI-Generated Insights</CardTitle>
            <Badge variant="outline" className="bg-indigo-50 text-indigo-700 border-indigo-200">
              AI Powered
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="pt-4">
          <ul className="space-y-3">
            {data.insights.map((insight: string, index: number) => (
              <li key={index} className="flex items-start">
                <svg
                  className="w-5 h-5 text-indigo-600 mt-0.5 mr-2 flex-shrink-0"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <span>{insight}</span>
              </li>
            ))}
          </ul>
        </CardContent>
      </Card>
    </div>
  );
}
