import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { generateInvoicePDF } from '../services/pdf.service';
import fs from 'fs-extra';
import path from 'path';
import crypto from 'crypto';
import { logger } from '../utils/logger';

// Initialize Prisma client
const prisma = new PrismaClient();

// PDF cache configuration
const PDF_CACHE_DIR = path.join(process.cwd(), 'cache', 'pdf');
const PDF_CACHE_EXPIRY = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

// Ensure the cache directory exists
fs.ensureDirSync(PDF_CACHE_DIR);

/**
 * Generate a cache key for an invoice
 * @param invoiceId The invoice ID
 * @param tenantId The tenant ID
 * @param templateId The template ID (optional)
 * @returns The cache key
 */
const generateCacheKey = (invoiceId: string, tenantId: string, templateId?: string): string => {
  const data = `${invoiceId}:${tenantId}:${templateId || 'default'}`;
  return crypto.createHash('md5').update(data).digest('hex');
};

/**
 * Get a cached PDF file path if it exists and is not expired
 * @param cacheKey The cache key
 * @returns The cached file path or null if not found or expired
 */
const getCachedPDF = async (cacheKey: string): Promise<string | null> => {
  const cachedFilePath = path.join(PDF_CACHE_DIR, `${cacheKey}.pdf`);
  
  try {
    // Check if the file exists
    if (await fs.pathExists(cachedFilePath)) {
      // Check if the file is not expired
      const stats = await fs.stat(cachedFilePath);
      const fileAge = Date.now() - stats.mtimeMs;
      
      if (fileAge < PDF_CACHE_EXPIRY) {
        logger.info('Using cached PDF', { cacheKey, path: cachedFilePath });
        return cachedFilePath;
      } else {
        // File is expired, delete it
        await fs.unlink(cachedFilePath);
        logger.info('Deleted expired cached PDF', { cacheKey, path: cachedFilePath });
      }
    }
  } catch (error) {
    logger.error('Error checking cached PDF', { cacheKey, error });
  }
  
  return null;
};

/**
 * Cache a PDF file
 * @param cacheKey The cache key
 * @param filePath The file path to cache
 * @returns The cached file path
 */
const cachePDF = async (cacheKey: string, filePath: string): Promise<string> => {
  const cachedFilePath = path.join(PDF_CACHE_DIR, `${cacheKey}.pdf`);
  
  try {
    // Copy the file to the cache directory
    await fs.copy(filePath, cachedFilePath);
    logger.info('Cached PDF', { cacheKey, path: cachedFilePath });
    return cachedFilePath;
  } catch (error) {
    logger.error('Error caching PDF', { cacheKey, error });
    return filePath; // Return the original file path if caching fails
  }
};

/**
 * Generate and download an invoice PDF
 * @param req Request
 * @param res Response
 */
export const downloadInvoicePDF = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId } = req.user;
    const { id } = req.params;
    const { templateId } = req.query;

    // Generate a cache key
    const cacheKey = generateCacheKey(id, tenantId, templateId as string);
    
    // Check if the PDF is cached
    const cachedFilePath = await getCachedPDF(cacheKey);
    if (cachedFilePath) {
      // Set headers for file download
      const fileName = `invoice-${id.replace(/\//g, '-')}.pdf`;
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);

      // Stream the cached file to the response
      const fileStream = fs.createReadStream(cachedFilePath);
      fileStream.pipe(res);
      return;
    }

    // Get invoice with related data
    const invoice = await prisma.invoice.findFirst({
      where: {
        id,
        tenantId,
      },
      include: {
        tenant: true,
        customer: true,
        items: true,
      },
    });

    if (!invoice) {
      return res.status(404).json({ message: 'Invoice not found or does not belong to your tenant' });
    }

    // Add template ID if provided
    if (templateId) {
      (invoice as any).templateId = templateId;
    }

    // Generate PDF
    const pdfPath = await generateInvoicePDF(invoice);

    // Cache the PDF
    const finalPath = await cachePDF(cacheKey, pdfPath);

    // Set headers for file download
    const fileName = `invoice-${invoice.invoiceNumber.replace(/\//g, '-')}.pdf`;
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);

    // Stream the file to the response
    const fileStream = fs.createReadStream(finalPath);
    fileStream.pipe(res);

    // Clean up the original file if it's different from the cached file
    if (finalPath !== pdfPath) {
      fileStream.on('end', async () => {
        try {
          await fs.unlink(pdfPath);
          logger.info('Temporary PDF file deleted', { path: pdfPath });
        } catch (error) {
          logger.error('Error deleting temporary PDF file', { path: pdfPath, error });
        }
      });
    }
  } catch (error: any) {
    logger.error('Error generating invoice PDF', { error: error.message, stack: error.stack });
    return res.status(500).json({ message: 'Failed to generate invoice PDF', error: error.message });
  }
};

/**
 * Generate and view an invoice PDF in the browser
 * @param req Request
 * @param res Response
 */
export const viewInvoicePDF = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId } = req.user;
    const { id } = req.params;
    const { templateId } = req.query;

    // Generate a cache key
    const cacheKey = generateCacheKey(id, tenantId, templateId as string);
    
    // Check if the PDF is cached
    const cachedFilePath = await getCachedPDF(cacheKey);
    if (cachedFilePath) {
      // Set headers for inline viewing
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', 'inline');

      // Stream the cached file to the response
      const fileStream = fs.createReadStream(cachedFilePath);
      fileStream.pipe(res);
      return;
    }

    // Get invoice with related data
    const invoice = await prisma.invoice.findFirst({
      where: {
        id,
        tenantId,
      },
      include: {
        tenant: true,
        customer: true,
        items: true,
      },
    });

    if (!invoice) {
      return res.status(404).json({ message: 'Invoice not found or does not belong to your tenant' });
    }

    // Add template ID if provided
    if (templateId) {
      (invoice as any).templateId = templateId;
    }

    // Generate PDF
    const pdfPath = await generateInvoicePDF(invoice);

    // Cache the PDF
    const finalPath = await cachePDF(cacheKey, pdfPath);

    // Set headers for inline viewing
    const fileName = `invoice-${invoice.invoiceNumber.replace(/\//g, '-')}.pdf`;
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `inline; filename="${fileName}"`);

    // Stream the file to the response
    const fileStream = fs.createReadStream(finalPath);
    fileStream.pipe(res);

    // Clean up the original file if it's different from the cached file
    if (finalPath !== pdfPath) {
      fileStream.on('end', async () => {
        try {
          await fs.unlink(pdfPath);
          logger.info('Temporary PDF file deleted', { path: pdfPath });
        } catch (error) {
          logger.error('Error deleting temporary PDF file', { path: pdfPath, error });
        }
      });
    }
  } catch (error: any) {
    logger.error('Error generating invoice PDF for viewing', { error: error.message, stack: error.stack });
    return res.status(500).json({ message: 'Failed to generate invoice PDF', error: error.message });
  }
};

/**
 * Generate multiple PDFs in batch
 * @param req Request
 * @param res Response
 */
export const generateBatchPDFs = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId } = req.user;
    const { invoiceIds, templateId } = req.body;

    if (!invoiceIds || !Array.isArray(invoiceIds) || invoiceIds.length === 0) {
      return res.status(400).json({ message: 'Invoice IDs are required' });
    }

    // Create a temporary directory for the batch
    const batchId = crypto.randomUUID();
    const batchDir = path.join(process.cwd(), 'temp', 'batch', batchId);
    await fs.ensureDir(batchDir);

    // Get all invoices
    const invoices = await prisma.invoice.findMany({
      where: {
        id: { in: invoiceIds },
        tenantId,
      },
      include: {
        tenant: true,
        customer: true,
        items: true,
      },
    });

    if (invoices.length === 0) {
      return res.status(404).json({ message: 'No invoices found or they do not belong to your tenant' });
    }

    // Generate PDFs for each invoice
    const results = await Promise.all(
      invoices.map(async (invoice) => {
        try {
          // Add template ID if provided
          if (templateId) {
            (invoice as any).templateId = templateId;
          }

          // Generate PDF
          const pdfPath = await generateInvoicePDF(invoice);

          // Copy to batch directory
          const batchFilePath = path.join(batchDir, `invoice-${invoice.invoiceNumber.replace(/\//g, '-')}.pdf`);
          await fs.copy(pdfPath, batchFilePath);

          // Delete the temporary file
          await fs.unlink(pdfPath);

          return {
            invoiceId: invoice.id,
            invoiceNumber: invoice.invoiceNumber,
            success: true,
            path: batchFilePath,
          };
        } catch (error: any) {
          logger.error('Error generating PDF in batch', { invoiceId: invoice.id, error });
          return {
            invoiceId: invoice.id,
            invoiceNumber: invoice.invoiceNumber,
            success: false,
            error: error.message,
          };
        }
      })
    );

    // Return the results
    return res.status(200).json({
      batchId,
      totalInvoices: invoiceIds.length,
      processedInvoices: results.length,
      successfulInvoices: results.filter((r) => r.success).length,
      failedInvoices: results.filter((r) => !r.success).length,
      results,
    });
  } catch (error: any) {
    logger.error('Error generating batch PDFs', { error: error.message, stack: error.stack });
    return res.status(500).json({ message: 'Failed to generate batch PDFs', error: error.message });
  }
};
