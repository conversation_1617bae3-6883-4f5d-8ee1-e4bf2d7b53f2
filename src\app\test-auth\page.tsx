'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { getCurrentUser, getAccessToken, isAuthenticated } from '@/lib/auth';

export default function TestAuthPage() {
  const { user, isAuthenticated: contextIsAuthenticated, login } = useAuth();
  const [localStorageUser, setLocalStorageUser] = useState<any>(null);
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [directIsAuthenticated, setDirectIsAuthenticated] = useState<boolean>(false);

  useEffect(() => {
    // Check localStorage directly
    try {
      const userJson = localStorage.getItem('invoix_user');
      setLocalStorageUser(userJson ? JSON.parse(userJson) : null);
      
      setAccessToken(localStorage.getItem('invoix_access_token'));
      setDirectIsAuthenticated(isAuthenticated());
    } catch (error) {
      console.error('Error checking localStorage:', error);
    }
  }, []);

  const handleTestUserLogin = async () => {
    try {
      await login('<EMAIL>', 'password123');
    } catch (error) {
      console.error('Error logging in test user:', error);
    }
  };

  const clearLocalStorage = () => {
    try {
      localStorage.removeItem('invoix_user');
      localStorage.removeItem('invoix_access_token');
      localStorage.removeItem('invoix_refresh_token');
      window.location.reload();
    } catch (error) {
      console.error('Error clearing localStorage:', error);
    }
  };

  return (
    <div className="container mx-auto py-8">
      <Card>
        <CardHeader>
          <CardTitle>Authentication Test Page</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h2 className="text-lg font-semibold">Auth Context State</h2>
            <pre className="bg-gray-100 p-4 rounded mt-2 overflow-auto max-h-40">
              {JSON.stringify({ user, isAuthenticated: contextIsAuthenticated }, null, 2)}
            </pre>
          </div>

          <div>
            <h2 className="text-lg font-semibold">LocalStorage User</h2>
            <pre className="bg-gray-100 p-4 rounded mt-2 overflow-auto max-h-40">
              {JSON.stringify(localStorageUser, null, 2)}
            </pre>
          </div>

          <div>
            <h2 className="text-lg font-semibold">Access Token</h2>
            <pre className="bg-gray-100 p-4 rounded mt-2 overflow-auto max-h-20">
              {accessToken || 'No token found'}
            </pre>
          </div>

          <div>
            <h2 className="text-lg font-semibold">Direct isAuthenticated Check</h2>
            <pre className="bg-gray-100 p-4 rounded mt-2">
              {String(directIsAuthenticated)}
            </pre>
          </div>

          <div className="flex space-x-4">
            <Button onClick={handleTestUserLogin}>
              Login as Test User
            </Button>
            <Button variant="destructive" onClick={clearLocalStorage}>
              Clear LocalStorage
            </Button>
            <Link href="/dashboard">
              <Button variant="outline">
                Go to Dashboard
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
