import { Router } from 'express';
import multer from 'multer';
import path from 'path';
import { 
  getCertificates, 
  uploadCertificate, 
  removeCertificate, 
  activateCertificate, 
  testCertificate 
} from '../controllers/certificate.controller';
import { authenticate } from '../middleware/auth.middleware';
import { ExpressHandler } from '../types/express';

// Set up multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, path.join(process.cwd(), 'temp'));
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  },
});

const upload = multer({
  storage,
  fileFilter: (req, file, cb) => {
    // Accept only .p12 and .pfx files
    if (file.mimetype === 'application/x-pkcs12' || 
        path.extname(file.originalname).toLowerCase() === '.p12' || 
        path.extname(file.originalname).toLowerCase() === '.pfx') {
      cb(null, true);
    } else {
      cb(new Error('Only PKCS#12 (.p12, .pfx) files are allowed'));
    }
  },
  limits: {
    fileSize: 1024 * 1024 * 5, // 5MB
  },
});

const router = Router();

// All routes are protected and require admin access
router.get('/', authenticate as ExpressHandler, getCertificates as ExpressHandler);
router.post('/', authenticate as ExpressHandler, upload.single('certificate'), uploadCertificate as ExpressHandler);
router.delete('/:id', authenticate as ExpressHandler, removeCertificate as ExpressHandler);
router.post('/:id/activate', authenticate as ExpressHandler, activateCertificate as ExpressHandler);
router.post('/:id/test', authenticate as ExpressHandler, testCertificate as ExpressHandler);

export default router;
