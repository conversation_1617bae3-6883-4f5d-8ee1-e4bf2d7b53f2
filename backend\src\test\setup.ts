// This file runs before each test file
import { prisma } from '../index';

// Increase test timeout
jest.setTimeout(30000);

// Mock environment variables
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.JWT_REFRESH_SECRET = 'test-jwt-refresh-secret';
process.env.NODE_ENV = 'test';

// Clear database between tests
beforeEach(async () => {
  // Skip database cleanup if using the --watch flag
  if (!process.argv.includes('--watch')) {
    const tables = await prisma.$queryRaw<
      Array<{ tablename: string }>
    >`SELECT tablename FROM pg_tables WHERE schemaname='public'`;

    for (const { tablename } of tables) {
      if (tablename !== '_prisma_migrations') {
        try {
          await prisma.$executeRawUnsafe(`TRUNCATE TABLE "public"."${tablename}" CASCADE;`);
        } catch (error) {
          console.log(`Error truncating ${tablename}`, error);
        }
      }
    }
  }
});

// Close database connection after all tests
afterAll(async () => {
  await prisma.$disconnect();
});
