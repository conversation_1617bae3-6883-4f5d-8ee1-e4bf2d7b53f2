import { Request, Response } from 'express';
import { prisma } from '../index';
import { getActiveCertificate, retrieveCertificate } from '../services/certificate-storage.service';
import { logger } from '../utils/logger';

/**
 * Basic health check endpoint
 */
export const basicHealth = async (req: Request, res: Response) => {
  try {
    // Check database connection
    await prisma.$queryRaw`SELECT 1`;
    
    return res.status(200).json({
      status: 'ok',
      timestamp: new Date().toISOString(),
    });
  } catch (error: any) {
    logger.error('Health check failed:', { error: error.message });
    
    return res.status(500).json({
      status: 'error',
      message: 'Database connection failed',
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * Detailed health check including LHDN integration status
 */
export const detailedHealth = async (req: Request, res: Response) => {
  try {
    // Check database connection
    let dbStatus = 'ok';
    try {
      await prisma.$queryRaw`SELECT 1`;
    } catch (dbError: any) {
      dbStatus = 'error';
      logger.error('Database health check failed:', { error: dbError.message });
    }
    
    // Check LHDN certificate
    let lhdnStatus = 'ok';
    let lhdnMessage = 'LHDN certificate is valid';
    
    try {
      const certificateId = await getActiveCertificate();
      
      if (!certificateId) {
        lhdnStatus = 'warning';
        lhdnMessage = 'No active LHDN certificate found';
      } else {
        // Try to retrieve the certificate
        await retrieveCertificate(certificateId);
      }
    } catch (certError: any) {
      lhdnStatus = 'error';
      lhdnMessage = `LHDN certificate error: ${certError.message}`;
      logger.error('LHDN certificate health check failed:', { error: certError.message });
    }
    
    // Get system info
    const systemInfo = {
      nodeVersion: process.version,
      platform: process.platform,
      memory: {
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        rss: Math.round(process.memoryUsage().rss / 1024 / 1024),
      },
      uptime: Math.round(process.uptime()),
    };
    
    return res.status(dbStatus === 'ok' && lhdnStatus !== 'error' ? 200 : 500).json({
      status: dbStatus === 'ok' && lhdnStatus === 'ok' ? 'ok' : 'degraded',
      timestamp: new Date().toISOString(),
      components: {
        database: {
          status: dbStatus,
        },
        lhdn: {
          status: lhdnStatus,
          message: lhdnMessage,
        },
      },
      system: systemInfo,
    });
  } catch (error: any) {
    logger.error('Detailed health check failed:', { error: error.message });
    
    return res.status(500).json({
      status: 'error',
      message: error.message,
      timestamp: new Date().toISOString(),
    });
  }
};
