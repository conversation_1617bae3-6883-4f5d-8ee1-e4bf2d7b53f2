'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { apiClient } from '@/lib/api/client';
import { useToast } from '@/components/ui/use-toast';

// Mock user data
const mockUsers = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'ADMI<PERSON>',
    status: 'ACTIVE',
    lastLogin: '2023-06-15T10:30:00Z',
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'MANAGER',
    status: 'ACTIVE',
    lastLogin: '2023-06-14T14:45:00Z',
  },
  {
    id: '3',
    name: 'Bob Johnson',
    email: '<EMAIL>',
    role: 'USER',
    status: 'INVITED',
    lastLogin: null,
  },
  {
    id: '4',
    name: 'Alice Brown',
    email: '<EMAIL>',
    role: 'USER',
    status: 'ACTIVE',
    lastLogin: '2023-06-10T09:15:00Z',
  },
];

export default function UsersPage() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [users, setUsers] = useState(mockUsers);
  const [isLoading, setIsLoading] = useState(false);
  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);
  const [inviteFormData, setInviteFormData] = useState({
    email: '',
    name: '',
    role: 'USER',
  });
  const [editingUserId, setEditingUserId] = useState<string | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editFormData, setEditFormData] = useState({
    name: '',
    role: '',
  });

  const handleInviteChange = (field: string, value: string) => {
    setInviteFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleEditChange = (field: string, value: string) => {
    setEditFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleInviteUser = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // In a real app, this would call the API to invite a user
      // await apiClient.post('/users/invite', inviteFormData);
      
      // Mock invitation
      const newUser = {
        id: `${users.length + 1}`,
        name: inviteFormData.name,
        email: inviteFormData.email,
        role: inviteFormData.role,
        status: 'INVITED',
        lastLogin: null,
      };
      
      setUsers([...users, newUser]);
      setIsInviteDialogOpen(false);
      setInviteFormData({
        email: '',
        name: '',
        role: 'USER',
      });
      
      toast({
        title: 'User Invited',
        description: `An invitation has been sent to ${inviteFormData.email}`,
        variant: 'default',
      });
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to invite user',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditUser = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // In a real app, this would call the API to update a user
      // await apiClient.put(`/users/${editingUserId}`, editFormData);
      
      // Mock update
      const updatedUsers = users.map(user => {
        if (user.id === editingUserId) {
          return {
            ...user,
            name: editFormData.name,
            role: editFormData.role,
          };
        }
        return user;
      });
      
      setUsers(updatedUsers);
      setIsEditDialogOpen(false);
      setEditingUserId(null);
      
      toast({
        title: 'User Updated',
        description: 'User information has been updated successfully',
        variant: 'default',
      });
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update user',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendInvitation = async (userId: string) => {
    setIsLoading(true);
    const userToResend = users.find(u => u.id === userId);

    try {
      // In a real app, this would call the API to resend the invitation
      // await apiClient.post(`/users/${userId}/resend-invitation`);
      
      toast({
        title: 'Invitation Resent',
        description: `Invitation has been resent to ${userToResend?.email}`,
        variant: 'default',
      });
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to resend invitation',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const openEditDialog = (userId: string) => {
    const userToEdit = users.find(u => u.id === userId);
    if (userToEdit) {
      setEditFormData({
        name: userToEdit.name,
        role: userToEdit.role,
      });
      setEditingUserId(userId);
      setIsEditDialogOpen(true);
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Never';
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  return (
    <div className="space-y-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between bg-white p-6 rounded-lg shadow-sm mb-6">
        <div>
          <h1 className="text-2xl font-bold">User Management</h1>
          <p className="text-text-secondary">Manage users and their permissions</p>
        </div>
        <div className="mt-4 md:mt-0">
          <Dialog open={isInviteDialogOpen} onOpenChange={setIsInviteDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm" className="bg-indigo-600 hover:bg-indigo-700">
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Invite User
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Invite New User</DialogTitle>
                <DialogDescription>
                  Send an invitation to join your organization.
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleInviteUser} className="space-y-4 mt-4">
                <Input
                  label="Email"
                  type="email"
                  value={inviteFormData.email}
                  onChange={(e) => handleInviteChange('email', e.target.value)}
                  required
                />
                <Input
                  label="Name"
                  value={inviteFormData.name}
                  onChange={(e) => handleInviteChange('name', e.target.value)}
                  required
                />
                <div className="space-y-2">
                  <label className="text-sm font-medium">Role</label>
                  <Select
                    value={inviteFormData.role}
                    onValueChange={(value) => handleInviteChange('role', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ADMIN">Admin</SelectItem>
                      <SelectItem value="MANAGER">Manager</SelectItem>
                      <SelectItem value="USER">User</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <DialogFooter>
                  <Button type="submit" disabled={isLoading}>
                    {isLoading ? 'Sending...' : 'Send Invitation'}
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Card className="border-none shadow-md">
        <CardHeader className="pb-2 border-b">
          <CardTitle className="text-lg font-bold text-text-primary">Users</CardTitle>
          <CardDescription className="text-text-secondary">
            Manage users and their roles in your organization
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm text-left" className="w-full border-collapse">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4 font-medium text-text-secondary">Name</th>
                  <th className="text-left py-3 px-4 font-medium text-text-secondary">Email</th>
                  <th className="text-left py-3 px-4 font-medium text-text-secondary">Role</th>
                  <th className="text-left py-3 px-4 font-medium text-text-secondary">Status</th>
                  <th className="text-left py-3 px-4 font-medium text-text-secondary">Last Login</th>
                  <th className="text-right py-3 px-4 font-medium text-text-secondary">Actions</th>
                </tr>
              </thead>
              <tbody>
                {users.map((user) => (
                  <tr key={user.id} className="border-b ">
                    <td className="py-3 px-4">{user.name}</td>
                    <td className="py-3 px-4">{user.email}</td>
                    <td className="py-3 px-4">
                      <Badge variant="outline" className="font-normal">
                        {user.role}
                      </Badge>
                    </td>
                    <td className="py-3 px-4">
                      {user.status === 'ACTIVE' ? (
                        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                          Active
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                          Invited
                        </Badge>
                      )}
                    </td>
                    <td className="py-3 px-4 text-sm text-text-secondary">
                      {formatDate(user.lastLogin)}
                    </td>
                    <td className="py-3 px-4 text-right">
                      <div className="flex justify-end space-x-2">
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => openEditDialog(user.id)}
                        >
                          Edit
                        </Button>
                        {user.status === 'INVITED' && (
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => handleResendInvitation(user.id)}
                            disabled={isLoading}
                          >
                            Resend
                          </Button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Edit User Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
            <DialogDescription>
              Update user information and permissions
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleEditUser} className="space-y-4 mt-4">
            <Input
              label="Name"
              value={editFormData.name}
              onChange={(e) => handleEditChange('name', e.target.value)}
              required
            />
            <div className="space-y-2">
              <label className="text-sm font-medium">Role</label>
              <Select
                value={editFormData.role}
                onValueChange={(value) => handleEditChange('role', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ADMIN">Admin</SelectItem>
                  <SelectItem value="MANAGER">Manager</SelectItem>
                  <SelectItem value="USER">User</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <DialogFooter>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Saving...' : 'Save Changes'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
