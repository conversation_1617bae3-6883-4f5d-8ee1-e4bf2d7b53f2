import { Router } from 'express';
import { getTemplates, getTemplateById, createTemplate, updateTemplate, deleteTemplate } from '../controllers/template.controller';
import { authenticate, authorize } from '../middleware/auth.middleware';
import { ExpressHandler } from '../types/express';

const router = Router();

// All routes are protected
router.get('/', authenticate as ExpressHandler, getTemplates as ExpressHandler);
router.get('/:id', authenticate as ExpressHandler, getTemplateById as ExpressHandler);
router.post('/', authenticate as ExpressHandler, createTemplate as ExpressHandler);
router.put('/:id', authenticate as ExpressHandler, updateTemplate as ExpressHandler);
router.delete('/:id', authenticate as ExpressHandler, deleteTemplate as ExpressHandler);

export default router;
