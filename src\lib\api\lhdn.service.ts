import { apiClient } from './client';

// Types
export interface LHDNCertificate {
  id: string;
  certificateNumber: string;
  issuedDate: string;
  expiryDate: string;
  status: 'ACTIVE' | 'EXPIRED' | 'REVOKED';
  certificateFile?: string;
}

export interface LHDNInvoiceSubmission {
  id: string;
  invoiceId: string;
  invoiceNumber: string;
  submissionDate: string;
  status: 'PENDING' | 'SUBMITTED' | 'ACCEPTED' | 'REJECTED';
  referenceNumber?: string;
  errorMessage?: string;
}

export interface LHDNSettings {
  enabled: boolean;
  autoSubmit: boolean;
  apiKey?: string;
  clientId?: string;
  clientSecret?: string;
  environment: 'SANDBOX' | 'PRODUCTION';
}

/**
 * LHDN MyInvois API Service
 * Provides methods for interacting with the LHDN MyInvois API
 */
class LHDNService {
  /**
   * Get LHDN certificate
   */
  async getCertificate(): Promise<LHDNCertificate> {
    return apiClient.get('/lhdn/certificate');
  }

  /**
   * Upload LHDN certificate
   */
  async uploadCertificate(file: File): Promise<LHDNCertificate> {
    const formData = new FormData();
    formData.append('certificate', file);

    return apiClient.post('/lhdn/certificate', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  /**
   * Revoke LHDN certificate
   */
  async revokeCertificate(): Promise<{ success: boolean }> {
    return apiClient.delete('/lhdn/certificate');
  }

  /**
   * Get LHDN settings
   */
  async getSettings(): Promise<LHDNSettings> {
    return apiClient.get('/lhdn/settings');
  }

  /**
   * Update LHDN settings
   */
  async updateSettings(settings: Partial<LHDNSettings>): Promise<LHDNSettings> {
    return apiClient.put('/lhdn/settings', settings);
  }

  /**
   * Submit invoice to LHDN
   */
  async submitInvoice(invoiceId: string): Promise<LHDNInvoiceSubmission> {
    return apiClient.post(`/lhdn/invoices/${invoiceId}/submit`);
  }

  /**
   * Get invoice submission status
   */
  async getInvoiceSubmissionStatus(invoiceId: string): Promise<LHDNInvoiceSubmission> {
    return apiClient.get(`/lhdn/invoices/${invoiceId}/status`);
  }

  /**
   * Get all invoice submissions
   */
  async getInvoiceSubmissions(params?: {
    page?: number;
    limit?: number;
    status?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<{
    data: LHDNInvoiceSubmission[];
    total: number;
    page: number;
    limit: number;
  }> {
    return apiClient.get('/lhdn/submissions', { params });
  }

  /**
   * Test LHDN API connection
   */
  async testConnection(): Promise<{ success: boolean; message: string }> {
    return apiClient.post('/lhdn/test-connection');
  }

  /**
   * Generate LHDN report
   */
  async generateReport(params: {
    startDate: string;
    endDate: string;
    type: 'SUMMARY' | 'DETAILED';
  }): Promise<{ reportUrl: string }> {
    return apiClient.post('/lhdn/reports', params);
  }
}

export const lhdnService = new LHDNService();
export default lhdnService;
