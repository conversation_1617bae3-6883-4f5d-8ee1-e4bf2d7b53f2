import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { handleError } from '../utils/errorHandler';

const prisma = new PrismaClient();

// Supplier Controllers
export const createSupplier = async (req: Request, res: Response) => {
  try {
    const {
      name,
      contactPerson,
      email,
      phone,
      address,
      taxId,
      paymentTerms
    } = req.body;

    const tenantId = req.user.tenantId;

    const supplier = await prisma.supplier.create({
      data: {
        name,
        contactPerson,
        email,
        phone,
        address,
        taxId,
        paymentTerms: parseInt(paymentTerms || 30),
        tenant: {
          connect: { id: tenantId }
        }
      }
    });

    res.status(201).json(supplier);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const getSuppliers = async (req: Request, res: Response) => {
  try {
    const tenantId = req.user.tenantId;
    const suppliers = await prisma.supplier.findMany({
      where: {
        tenantId
      },
      orderBy: {
        name: 'asc'
      }
    });

    res.status(200).json(suppliers);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const getSupplier = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenantId;

    const supplier = await prisma.supplier.findFirst({
      where: {
        id,
        tenantId
      },
      include: {
        purchaseOrders: {
          orderBy: {
            createdAt: 'desc'
          },
          take: 10
        }
      }
    });

    if (!supplier) {
      return res.status(404).json({ message: 'Supplier not found' });
    }

    res.status(200).json(supplier);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const updateSupplier = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const {
      name,
      contactPerson,
      email,
      phone,
      address,
      taxId,
      paymentTerms,
      isActive
    } = req.body;

    const tenantId = req.user.tenantId;

    const supplier = await prisma.supplier.findFirst({
      where: {
        id,
        tenantId
      }
    });

    if (!supplier) {
      return res.status(404).json({ message: 'Supplier not found' });
    }

    const updatedSupplier = await prisma.supplier.update({
      where: { id },
      data: {
        name,
        contactPerson,
        email,
        phone,
        address,
        taxId,
        paymentTerms: parseInt(paymentTerms || 30),
        isActive: isActive !== undefined ? isActive : true
      }
    });

    res.status(200).json(updatedSupplier);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const deleteSupplier = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenantId;

    const supplier = await prisma.supplier.findFirst({
      where: {
        id,
        tenantId
      },
      include: {
        purchaseOrders: true
      }
    });

    if (!supplier) {
      return res.status(404).json({ message: 'Supplier not found' });
    }

    // Check if supplier has purchase orders
    if (supplier.purchaseOrders.length > 0) {
      return res.status(400).json({
        message: 'Cannot delete supplier with purchase orders. Please archive the supplier instead.'
      });
    }

    await prisma.supplier.delete({
      where: { id }
    });

    res.status(200).json({ message: 'Supplier deleted successfully' });
  } catch (error) {
    handleError(error, req, res);
  }
};

// Purchase Order Controllers
export const createPurchaseOrder = async (req: Request, res: Response) => {
  try {
    const {
      supplierId,
      poNumber,
      orderDate,
      expectedDelivery,
      notes,
      items
    } = req.body;

    const tenantId = req.user.tenantId;

    // Calculate total amount
    let totalAmount = 0;
    let totalTax = 0;

    // Validate items
    if (!items || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({ message: 'Purchase order must have at least one item' });
    }

    // Calculate totals
    items.forEach(item => {
      const amount = parseFloat(item.unitPrice) * parseInt(item.quantity);
      totalAmount += amount;
      // Add tax if applicable
      if (item.taxRate) {
        totalTax += amount * (parseFloat(item.taxRate) / 100);
      }
    });

    // Create purchase order with items in a transaction
    const result = await prisma.$transaction(async (prisma) => {
      // Create the purchase order
      const purchaseOrder = await prisma.purchaseOrder.create({
        data: {
          poNumber,
          orderDate: orderDate ? new Date(orderDate) : new Date(),
          expectedDelivery: expectedDelivery ? new Date(expectedDelivery) : null,
          status: 'DRAFT',
          totalAmount,
          tax: totalTax,
          notes,
          supplier: {
            connect: { id: supplierId }
          },
          tenant: {
            connect: { id: tenantId }
          }
        }
      });

      // Create purchase order items
      for (const item of items) {
        await prisma.purchaseOrderItem.create({
          data: {
            description: item.description,
            quantity: parseInt(item.quantity),
            unitPrice: parseFloat(item.unitPrice),
            amount: parseFloat(item.unitPrice) * parseInt(item.quantity),
            purchaseOrder: {
              connect: { id: purchaseOrder.id }
            },
            product: {
              connect: { id: item.productId }
            },
            tenant: {
              connect: { id: tenantId }
            }
          }
        });
      }

      return purchaseOrder;
    });

    res.status(201).json(result);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const getPurchaseOrders = async (req: Request, res: Response) => {
  try {
    const tenantId = req.user.tenantId;
    const { supplierId, status } = req.query;

    const whereClause: any = { tenantId };

    if (supplierId) {
      whereClause.supplierId = supplierId as string;
    }

    if (status) {
      whereClause.status = status as string;
    }

    const purchaseOrders = await prisma.purchaseOrder.findMany({
      where: whereClause,
      include: {
        supplier: true,
        items: {
          include: {
            product: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    res.status(200).json(purchaseOrders);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const getPurchaseOrder = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenantId;

    const purchaseOrder = await prisma.purchaseOrder.findFirst({
      where: {
        id,
        tenantId
      },
      include: {
        supplier: true,
        items: {
          include: {
            product: true
          }
        },
        receipts: {
          include: {
            items: true
          }
        }
      }
    });

    if (!purchaseOrder) {
      return res.status(404).json({ message: 'Purchase order not found' });
    }

    res.status(200).json(purchaseOrder);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const updatePurchaseOrder = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const {
      poNumber,
      orderDate,
      expectedDelivery,
      status,
      notes
    } = req.body;

    const tenantId = req.user.tenantId;

    const purchaseOrder = await prisma.purchaseOrder.findFirst({
      where: {
        id,
        tenantId
      }
    });

    if (!purchaseOrder) {
      return res.status(404).json({ message: 'Purchase order not found' });
    }

    // Don't allow updating if already received
    if (purchaseOrder.status === 'RECEIVED' && status !== 'RECEIVED') {
      return res.status(400).json({
        message: 'Cannot update a purchase order that has been fully received'
      });
    }

    const updatedPurchaseOrder = await prisma.purchaseOrder.update({
      where: { id },
      data: {
        poNumber,
        orderDate: orderDate ? new Date(orderDate) : undefined,
        expectedDelivery: expectedDelivery ? new Date(expectedDelivery) : null,
        status,
        notes
      }
    });

    res.status(200).json(updatedPurchaseOrder);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const deletePurchaseOrder = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenantId;

    const purchaseOrder = await prisma.purchaseOrder.findFirst({
      where: {
        id,
        tenantId
      },
      include: {
        receipts: true
      }
    });

    if (!purchaseOrder) {
      return res.status(404).json({ message: 'Purchase order not found' });
    }

    // Don't allow deleting if has receipts
    if (purchaseOrder.receipts.length > 0) {
      return res.status(400).json({
        message: 'Cannot delete a purchase order with goods receipts. Please cancel it instead.'
      });
    }

    // Don't allow deleting if not in DRAFT status
    if (purchaseOrder.status !== 'DRAFT') {
      return res.status(400).json({
        message: 'Cannot delete a purchase order that has been sent. Please cancel it instead.'
      });
    }

    // Delete purchase order items first
    await prisma.purchaseOrderItem.deleteMany({
      where: {
        purchaseOrderId: id
      }
    });

    // Then delete the purchase order
    await prisma.purchaseOrder.delete({
      where: { id }
    });

    res.status(200).json({ message: 'Purchase order deleted successfully' });
  } catch (error) {
    handleError(error, req, res);
  }
};

// Goods Receipt Controllers
export const createGoodsReceipt = async (req: Request, res: Response) => {
  try {
    const {
      purchaseOrderId,
      receiptNumber,
      receiveDate,
      notes,
      items
    } = req.body;

    const tenantId = req.user.tenantId;

    // Validate items
    if (!items || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({ message: 'Goods receipt must have at least one item' });
    }

    // Get purchase order
    const purchaseOrder = await prisma.purchaseOrder.findFirst({
      where: {
        id: purchaseOrderId,
        tenantId
      },
      include: {
        items: true
      }
    });

    if (!purchaseOrder) {
      return res.status(404).json({ message: 'Purchase order not found' });
    }

    // Don't allow receiving if cancelled
    if (purchaseOrder.status === 'CANCELLED') {
      return res.status(400).json({ message: 'Cannot receive goods for a cancelled purchase order' });
    }

    // Create goods receipt with items in a transaction
    const result = await prisma.$transaction(async (prisma) => {
      // Create the goods receipt
      const goodsReceipt = await prisma.goodsReceipt.create({
        data: {
          receiptNumber,
          receiveDate: receiveDate ? new Date(receiveDate) : new Date(),
          notes,
          purchaseOrder: {
            connect: { id: purchaseOrderId }
          },
          tenant: {
            connect: { id: tenantId }
          }
        }
      });

      // Create goods receipt items and update inventory
      for (const item of items) {
        // Create receipt item
        await prisma.goodsReceiptItem.create({
          data: {
            productId: item.productId,
            quantity: parseInt(item.quantity),
            notes: item.notes,
            goodsReceipt: {
              connect: { id: goodsReceipt.id }
            },
            tenant: {
              connect: { id: tenantId }
            }
          }
        });

        // Update purchase order item received quantity
        const poItem = purchaseOrder.items.find(i => i.productId === item.productId);
        if (poItem) {
          await prisma.purchaseOrderItem.update({
            where: { id: poItem.id },
            data: {
              receivedQuantity: poItem.receivedQuantity + parseInt(item.quantity)
            }
          });
        }

        // Find or create inventory item
        let inventoryItem = await prisma.inventoryItem.findFirst({
          where: {
            productId: item.productId,
            warehouseId: item.warehouseId,
            tenantId
          }
        });

        if (inventoryItem) {
          // Update existing inventory
          await prisma.inventoryItem.update({
            where: { id: inventoryItem.id },
            data: {
              quantity: inventoryItem.quantity + parseInt(item.quantity)
            }
          });
        } else {
          // Create new inventory item
          inventoryItem = await prisma.inventoryItem.create({
            data: {
              quantity: parseInt(item.quantity),
              product: {
                connect: { id: item.productId }
              },
              warehouse: {
                connect: { id: item.warehouseId }
              },
              tenant: {
                connect: { id: tenantId }
              }
            }
          });
        }

        // Create inventory transaction
        await prisma.inventoryTransaction.create({
          data: {
            type: 'PURCHASE',
            quantity: parseInt(item.quantity),
            notes: `Received from PO #${purchaseOrder.poNumber}`,
            referenceId: goodsReceipt.id,
            referenceType: 'GOODS_RECEIPT',
            inventoryItem: {
              connect: { id: inventoryItem.id }
            },
            tenant: {
              connect: { id: tenantId }
            }
          }
        });
      }

      // Update purchase order status
      const allItemsReceived = purchaseOrder.items.every(item =>
        item.receivedQuantity + (items.find(i => i.productId === item.productId)?.quantity || 0) >= item.quantity
      );

      const someItemsReceived = purchaseOrder.items.some(item =>
        item.receivedQuantity + (items.find(i => i.productId === item.productId)?.quantity || 0) > 0
      );

      let newStatus = purchaseOrder.status;
      if (allItemsReceived) {
        newStatus = 'RECEIVED';
      } else if (someItemsReceived) {
        newStatus = 'PARTIALLY_RECEIVED';
      }

      await prisma.purchaseOrder.update({
        where: { id: purchaseOrderId },
        data: {
          status: newStatus
        }
      });

      return goodsReceipt;
    });

    res.status(201).json(result);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const getGoodsReceipts = async (req: Request, res: Response) => {
  try {
    const tenantId = req.user.tenantId;
    const { purchaseOrderId } = req.query;

    const whereClause: any = { tenantId };

    if (purchaseOrderId) {
      whereClause.purchaseOrderId = purchaseOrderId as string;
    }

    const goodsReceipts = await prisma.goodsReceipt.findMany({
      where: whereClause,
      include: {
        purchaseOrder: {
          include: {
            supplier: true
          }
        },
        items: true
      },
      orderBy: {
        receiveDate: 'desc'
      }
    });

    res.status(200).json(goodsReceipts);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const getGoodsReceipt = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenantId;

    const goodsReceipt = await prisma.goodsReceipt.findFirst({
      where: {
        id,
        tenantId
      },
      include: {
        purchaseOrder: {
          include: {
            supplier: true
          }
        },
        items: true
      }
    });

    if (!goodsReceipt) {
      return res.status(404).json({ message: 'Goods receipt not found' });
    }

    res.status(200).json(goodsReceipt);
  } catch (error) {
    handleError(error, req, res);
  }
};
