# Invoix Architecture

This document outlines the architecture and design decisions for the Invoix multi-tenant invoicing SaaS platform.

## System Architecture

Invoix follows a modern full-stack architecture with a clear separation between frontend and backend:

```
┌─────────────────┐      ┌─────────────────┐      ┌─────────────────┐
│                 │      │                 │      │                 │
│    Frontend     │◄────►│     Backend     │◄────►│    Database     │
│    (Next.js)    │      │   (Express.js)  │      │  (PostgreSQL)   │
│                 │      │                 │      │                 │
└─────────────────┘      └─────────────────┘      └─────────────────┘
        ▲                        ▲                        ▲
        │                        │                        │
        ▼                        ▼                        ▼
┌─────────────────┐      ┌─────────────────┐      ┌─────────────────┐
│                 │      │                 │      │                 │
│  External APIs  │      │   AI Services   │      │  File Storage   │
│  (Twilio, etc.) │      │                 │      │                 │
│                 │      │                 │      │                 │
└─────────────────┘      └─────────────────┘      └─────────────────┘
```

### Frontend Architecture

The frontend is built with Next.js using the App Router architecture, which provides server-side rendering, static site generation, and client-side rendering capabilities.

#### Key Components:

1. **Pages and Layouts**:
   - Public pages (landing, login, register)
   - Dashboard layouts with shared navigation
   - Tenant-specific views

2. **State Management**:
   - React Context for global state
   - React Query for server state management
   - Local component state for UI interactions

3. **Authentication**:
   - JWT-based authentication
   - Protected routes
   - Role-based access control

### Backend Architecture

The backend follows a layered architecture with clear separation of concerns:

```
┌─────────────────────────────────────────────────────────────┐
│                         Controllers                         │
└─────────────────────────────────────────────────────────────┘
                               ▲
                               │
                               ▼
┌─────────────────────────────────────────────────────────────┐
│                          Services                           │
└─────────────────────────────────────────────────────────────┘
                               ▲
                               │
                               ▼
┌─────────────────────────────────────────────────────────────┐
│                       Data Access Layer                     │
└─────────────────────────────────────────────────────────────┘
                               ▲
                               │
                               ▼
┌─────────────────────────────────────────────────────────────┐
│                          Database                           │
└─────────────────────────────────────────────────────────────┘
```

#### Key Components:

1. **Controllers**: Handle HTTP requests and responses
2. **Services**: Implement business logic
3. **Data Access Layer**: Interact with the database using Prisma ORM
4. **Middleware**: Handle cross-cutting concerns like authentication, logging, etc.

## Multi-Tenancy Implementation

Invoix implements a schema-based multi-tenancy approach:

1. **Database Isolation**:
   - Each tenant has its own schema in the PostgreSQL database
   - Prisma dynamically connects to the appropriate schema based on the tenant ID

2. **Tenant Identification**:
   - Tenant ID is included in the JWT token
   - Middleware extracts and validates tenant ID for each request
   - API routes are scoped to the tenant's data

3. **Tenant Onboarding**:
   - New tenant registration creates a new schema
   - Initial admin user is created
   - Default templates and settings are provisioned

## Security Architecture

Security is implemented at multiple levels:

1. **Authentication**:
   - JWT-based authentication with short-lived tokens
   - Secure password hashing with bcrypt
   - HTTPS for all communications

2. **Authorization**:
   - Role-based access control (RBAC)
   - Tenant-level isolation
   - Resource-level permissions

3. **Data Protection**:
   - Input validation and sanitization
   - Protection against common web vulnerabilities (XSS, CSRF, etc.)
   - Sensitive data encryption

## Integration Architecture

Invoix integrates with several external services:

1. **WhatsApp Integration (Twilio/360dialog)**:
   - Webhook-based communication
   - Message templates for invoices and reminders
   - Interactive responses for customer queries

2. **Payment Processing (Stripe/Razorpay)**:
   - Payment intent creation
   - Webhook handling for payment events
   - Reconciliation with invoices

3. **LHDN MyInvois Validation**:
   - Platform-level integration with centralized certificate management
   - API-based validation of invoice format
   - Digital signature verification
   - Compliance reporting
   - Clear separation between admin and user responsibilities

## AI/ML Architecture

AI features are implemented using a hybrid approach:

1. **Client-Side ML**:
   - TensorFlow.js for lightweight predictions
   - Pre-trained models for common patterns

2. **Server-Side ML**:
   - Python-based ML services for complex analytics
   - Scheduled batch processing for insights
   - Real-time fraud detection

## Scalability Considerations

The architecture is designed for horizontal scalability:

1. **Stateless Backend**:
   - No session state stored on servers
   - Easy to scale with multiple instances

2. **Database Scaling**:
   - Read replicas for high-read scenarios
   - Connection pooling for efficient resource usage

3. **Caching Strategy**:
   - Redis for shared cache
   - Browser caching for static assets
   - API response caching where appropriate

## Monitoring and Observability

The system includes comprehensive monitoring:

1. **Application Monitoring**:
   - Performance metrics
   - Error tracking
   - User behavior analytics

2. **Infrastructure Monitoring**:
   - Server health
   - Database performance
   - API response times

3. **Business Metrics**:
   - Tenant activity
   - Invoice volume
   - Payment success rates

## Deployment Architecture

Invoix is designed for cloud deployment:

1. **Containerization**:
   - Docker containers for consistent environments
   - Kubernetes for orchestration

2. **CI/CD Pipeline**:
   - Automated testing
   - Continuous deployment
   - Environment promotion (dev, staging, production)

3. **Infrastructure as Code**:
   - Terraform for provisioning
   - Environment configuration management
   - Disaster recovery planning
