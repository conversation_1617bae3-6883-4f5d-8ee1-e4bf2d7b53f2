import { Request, Response } from 'express';
import { prisma } from '../index';
import { hashPassword } from '../utils/auth';
import { validateBusinessRegNo } from '../services/lhdn.service';

// Create a new tenant with admin user
export const createTenant = async (req: Request, res: Response) => {
  try {
    const {
      name,
      businessName: initialBusinessName,
      businessRegNo,
      email,
      phone,
      address,
      logo,
      adminName,
      adminEmail,
      adminPassword,
    } = req.body;

    // Make businessName mutable
    let businessName = initialBusinessName;

    // Check if tenant with email already exists
    const existingTenant = await prisma.tenant.findUnique({
      where: { email },
    });

    if (existingTenant) {
      return res.status(400).json({ message: 'Tenant with this email already exists' });
    }

    // Check if admin user with email already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: adminEmail },
    });

    if (existingUser) {
      return res.status(400).json({ message: 'User with this email already exists' });
    }

    // Validate business registration number if provided
    let businessRegValid = false;
    if (businessRegNo) {
      try {
        const validationResult = await validateBusinessRegNo(businessRegNo);
        businessRegValid = validationResult.isValid;

        // If business name is not provided but validation returned a name, use it
        if (validationResult.isValid && validationResult.businessName && !businessName) {
          businessName = validationResult.businessName;
        }
      } catch (error) {
        console.error('Error validating business registration number:', error);
        // Continue with tenant creation even if validation fails
      }
    }

    // Hash the admin password
    const hashedPassword = await hashPassword(adminPassword);

    // Create tenant and admin user in a transaction
    const result = await prisma.$transaction(async (tx: any) => {
      // Create tenant
      const tenant = await tx.tenant.create({
        data: {
          name,
          businessName,
          businessRegNo,
          businessRegValid,
          email,
          phone,
          address,
          logo,
        },
      });

      // Create admin user
      const user = await tx.user.create({
        data: {
          name: adminName,
          email: adminEmail,
          password: hashedPassword,
          role: 'ADMIN',
          tenantId: tenant.id,
        },
      });

      return { tenant, user };
    });

    // Return success response without sensitive data
    return res.status(201).json({
      message: 'Tenant created successfully',
      tenant: {
        id: result.tenant.id,
        name: result.tenant.name,
        businessName: result.tenant.businessName,
        businessRegNo: result.tenant.businessRegNo,
        businessRegValid: result.tenant.businessRegValid,
        email: result.tenant.email,
      },
      admin: {
        id: result.user.id,
        name: result.user.name,
        email: result.user.email,
      },
    });
  } catch (error: any) {
    console.error('Error creating tenant:', error);
    return res.status(500).json({ message: 'Failed to create tenant', error: error.message });
  }
};

// Get tenant by ID
export const getTenantById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const tenant = await prisma.tenant.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            users: true,
            // customers: true, // Removed from schema
            // invoices: true,  // Removed from schema
          },
        },
      },
    });

    if (!tenant) {
      return res.status(404).json({ message: 'Tenant not found' });
    }

    return res.status(200).json(tenant);
  } catch (error: any) {
    console.error('Error getting tenant:', error);
    return res.status(500).json({ message: 'Failed to get tenant', error: error.message });
  }
};

// Update tenant
export const updateTenant = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { name, businessName: initialBusinessName, email, phone, address, logo } = req.body;
    // const businessRegNo = req.body.businessRegNo; // Removed from schema

    // Make businessName mutable
    let businessName = initialBusinessName;

    // Check if tenant exists
    const existingTenant = await prisma.tenant.findUnique({
      where: { id },
      select: {
        id: true,
        name: true,
        businessName: true,
        // businessRegNo: true,     // Removed from schema
        // businessRegValid: true,  // Removed from schema
        email: true,
        phone: true,
        address: true,
        logo: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!existingTenant) {
      return res.status(404).json({ message: 'Tenant not found' });
    }

    // Business registration validation removed as fields are no longer in schema
    // let businessRegValid = existingTenant.businessRegValid;
    // if (businessRegNo && businessRegNo !== existingTenant.businessRegNo) {
    //   try {
    //     const validationResult = await validateBusinessRegNo(businessRegNo);
    //     businessRegValid = validationResult.isValid;
    //
    //     // If business name is not provided but validation returned a name, use it
    //     if (validationResult.isValid && validationResult.businessName && !businessName) {
    //       businessName = validationResult.businessName;
    //     }
    //   } catch (error) {
    //     console.error('Error validating business registration number:', error);
    //     // Continue with tenant update even if validation fails
    //   }
    // }

    // Update tenant
    const updatedTenant = await prisma.tenant.update({
      where: { id },
      data: {
        name,
        businessName,
        // businessRegNo,     // Removed from schema
        // businessRegValid,  // Removed from schema
        email,
        phone,
        address,
        logo,
      },
    });

    return res.status(200).json({
      message: 'Tenant updated successfully',
      tenant: updatedTenant,
    });
  } catch (error: any) {
    console.error('Error updating tenant:', error);
    return res.status(500).json({ message: 'Failed to update tenant', error: error.message });
  }
};

// Delete tenant
export const deleteTenant = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    // Check if tenant exists
    const existingTenant = await prisma.tenant.findUnique({
      where: { id },
    });

    if (!existingTenant) {
      return res.status(404).json({ message: 'Tenant not found' });
    }

    // Delete tenant (cascade will delete all related records)
    await prisma.tenant.delete({
      where: { id },
    });

    return res.status(200).json({ message: 'Tenant deleted successfully' });
  } catch (error: any) {
    console.error('Error deleting tenant:', error);
    return res.status(500).json({ message: 'Failed to delete tenant', error: error.message });
  }
};
