import { apiClient } from './client';

// Types for Chart of Accounts
export interface Account {
  id: string;
  accountCode: string;
  accountName: string;
  accountType: AccountType;
  accountSubType: string;
  description?: string;
  isActive: boolean;
  balance: number;
  taxCode?: string;
  isSystemAccount?: boolean;
  createdAt: string;
  updatedAt: string;
}

export enum AccountType {
  ASSET = 'ASSET',
  LIABILITY = 'LIABILITY',
  EQUITY = 'EQUITY',
  REVENUE = 'REVENUE',
  EXPENSE = 'EXPENSE'
}

export interface CreateAccountRequest {
  accountCode: string;
  accountName: string;
  accountType: AccountType;
  accountSubType: string;
  description?: string;
  isActive?: boolean;
  taxCode?: string;
}

export interface UpdateAccountRequest {
  accountName?: string;
  accountSubType?: string;
  description?: string;
  isActive?: boolean;
  taxCode?: string;
}

// Types for Journal Entries
export interface JournalEntry {
  id: string;
  entryNumber: string;
  entryDate: string;
  description: string;
  reference?: string;
  status: JournalEntryStatus;
  amount: number;
  createdBy: string;
  approvedBy?: string;
  createdAt: string;
  updatedAt: string;
  sourceType?: string;
  sourceId?: string;
  isRecurring?: boolean;
  recurringScheduleId?: string;
  lines: JournalLine[];
}

export enum JournalEntryStatus {
  DRAFT = 'DRAFT',
  POSTED = 'POSTED',
  REVERSED = 'REVERSED'
}

export interface JournalLine {
  id: string;
  journalEntryId: string;
  accountId: string;
  accountCode?: string;
  accountName?: string;
  description?: string;
  debitAmount: number;
  creditAmount: number;
  projectId?: string;
  departmentId?: string;
  customerId?: string;
  supplierId?: string;
  taxAmount?: number;
  taxCodeId?: string;
}

export interface CreateJournalEntryRequest {
  entryDate: string;
  description: string;
  reference?: string;
  lines: CreateJournalLineRequest[];
  isRecurring?: boolean;
  recurringSchedule?: RecurringScheduleRequest;
}

export interface CreateJournalLineRequest {
  accountId: string;
  description?: string;
  debitAmount: number;
  creditAmount: number;
  projectId?: string;
  departmentId?: string;
  customerId?: string;
  supplierId?: string;
  taxAmount?: number;
  taxCodeId?: string;
}

export interface UpdateJournalEntryRequest {
  entryDate?: string;
  description?: string;
  reference?: string;
  lines?: CreateJournalLineRequest[];
}

export interface RecurringScheduleRequest {
  frequency: RecurringFrequency;
  nextRunDate: string;
  endDate?: string;
}

export enum RecurringFrequency {
  DAILY = 'DAILY',
  WEEKLY = 'WEEKLY',
  MONTHLY = 'MONTHLY',
  QUARTERLY = 'QUARTERLY',
  ANNUALLY = 'ANNUALLY'
}

// Types for Financial Reports
export interface FinancialReportRequest {
  reportType: ReportType;
  startDate: string;
  endDate: string;
  comparisonPeriod?: ComparisonPeriod;
}

export enum ReportType {
  PROFIT_LOSS = 'PROFIT_LOSS',
  BALANCE_SHEET = 'BALANCE_SHEET',
  CASH_FLOW = 'CASH_FLOW',
  TRIAL_BALANCE = 'TRIAL_BALANCE',
  GENERAL_LEDGER = 'GENERAL_LEDGER',
  ACCOUNTS_RECEIVABLE_AGING = 'ACCOUNTS_RECEIVABLE_AGING',
  ACCOUNTS_PAYABLE_AGING = 'ACCOUNTS_PAYABLE_AGING',
  TAX_SUMMARY = 'TAX_SUMMARY'
}

export enum ComparisonPeriod {
  PREVIOUS_PERIOD = 'PREVIOUS_PERIOD',
  PREVIOUS_YEAR = 'PREVIOUS_YEAR',
  YEAR_TO_DATE = 'YEAR_TO_DATE',
  CUSTOM = 'CUSTOM'
}

/**
 * Bookkeeping API Service
 * Provides methods for interacting with bookkeeping-related API endpoints
 */
class BookkeepingService {
  /**
   * Get all accounts
   */
  async getAccounts(): Promise<Account[]> {
    return apiClient.get('/bookkeeping/accounts');
  }

  /**
   * Get account by ID
   */
  async getAccount(accountId: string): Promise<Account> {
    return apiClient.get(`/bookkeeping/accounts/${accountId}`);
  }

  /**
   * Create a new account
   */
  async createAccount(data: CreateAccountRequest): Promise<Account> {
    return apiClient.post('/bookkeeping/accounts', data);
  }

  /**
   * Update an account
   */
  async updateAccount(accountId: string, data: UpdateAccountRequest): Promise<Account> {
    return apiClient.put(`/bookkeeping/accounts/${accountId}`, data);
  }

  /**
   * Delete an account
   */
  async deleteAccount(accountId: string): Promise<{ message: string }> {
    return apiClient.delete(`/bookkeeping/accounts/${accountId}`);
  }

  /**
   * Get account transactions
   */
  async getAccountTransactions(accountId: string, startDate?: string, endDate?: string): Promise<JournalLine[]> {
    let endpoint = `/bookkeeping/accounts/${accountId}/transactions`;
    
    // Add query parameters if provided
    const params = new URLSearchParams();
    if (startDate) params.append('startDate', startDate);
    if (endDate) params.append('endDate', endDate);
    
    const queryString = params.toString();
    if (queryString) endpoint += `?${queryString}`;
    
    return apiClient.get(endpoint);
  }

  /**
   * Get all journal entries
   */
  async getJournalEntries(status?: JournalEntryStatus, startDate?: string, endDate?: string): Promise<JournalEntry[]> {
    let endpoint = '/bookkeeping/journal-entries';
    
    // Add query parameters if provided
    const params = new URLSearchParams();
    if (status) params.append('status', status);
    if (startDate) params.append('startDate', startDate);
    if (endDate) params.append('endDate', endDate);
    
    const queryString = params.toString();
    if (queryString) endpoint += `?${queryString}`;
    
    return apiClient.get(endpoint);
  }

  /**
   * Get journal entry by ID
   */
  async getJournalEntry(entryId: string): Promise<JournalEntry> {
    return apiClient.get(`/bookkeeping/journal-entries/${entryId}`);
  }

  /**
   * Create a new journal entry
   */
  async createJournalEntry(data: CreateJournalEntryRequest): Promise<JournalEntry> {
    return apiClient.post('/bookkeeping/journal-entries', data);
  }

  /**
   * Update a journal entry
   */
  async updateJournalEntry(entryId: string, data: UpdateJournalEntryRequest): Promise<JournalEntry> {
    return apiClient.put(`/bookkeeping/journal-entries/${entryId}`, data);
  }

  /**
   * Delete a journal entry
   */
  async deleteJournalEntry(entryId: string): Promise<{ message: string }> {
    return apiClient.delete(`/bookkeeping/journal-entries/${entryId}`);
  }

  /**
   * Post a journal entry
   */
  async postJournalEntry(entryId: string): Promise<JournalEntry> {
    return apiClient.post(`/bookkeeping/journal-entries/${entryId}/post`, {});
  }

  /**
   * Reverse a journal entry
   */
  async reverseJournalEntry(entryId: string, reversalDate: string): Promise<JournalEntry> {
    return apiClient.post(`/bookkeeping/journal-entries/${entryId}/reverse`, { reversalDate });
  }

  /**
   * Generate a financial report
   */
  async generateReport(data: FinancialReportRequest): Promise<any> {
    return apiClient.post('/bookkeeping/reports', data);
  }

  /**
   * Get trial balance
   */
  async getTrialBalance(asOfDate: string): Promise<any> {
    return apiClient.get(`/bookkeeping/reports/trial-balance?asOfDate=${asOfDate}`);
  }

  /**
   * Get general ledger
   */
  async getGeneralLedger(startDate: string, endDate: string, accountId?: string): Promise<any> {
    let endpoint = `/bookkeeping/reports/general-ledger?startDate=${startDate}&endDate=${endDate}`;
    if (accountId) endpoint += `&accountId=${accountId}`;
    return apiClient.get(endpoint);
  }
}

export const bookkeepingService = new BookkeepingService();
export default bookkeepingService;
