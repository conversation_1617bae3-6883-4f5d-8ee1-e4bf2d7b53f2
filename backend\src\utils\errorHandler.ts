import { Request, Response } from 'express';
import { logger } from './logger';
import { Prisma } from '@prisma/client';

/**
 * Centralized error handler for API controllers
 * @param error The error that occurred
 * @param req The request object
 * @param res The response object
 */
export const handleError = (error: any, req: Request, res: Response) => {
  // Log the error
  logger.error('API Error', {
    error: error.message,
    stack: error.stack,
    method: req.method,
    url: req.originalUrl || req.url,
    body: req.body,
    params: req.params,
    query: req.query,
    ip: req.ip || req.connection.remoteAddress,
  });

  // Handle Prisma errors
  if (error instanceof Prisma.PrismaClientKnownRequestError) {
    switch (error.code) {
      case 'P2002': // Unique constraint violation
        return res.status(409).json({
          message: 'A record with this information already exists',
          error: process.env.NODE_ENV === 'development' ? error.message : undefined,
        });
      case 'P2025': // Record not found
        return res.status(404).json({
          message: 'Record not found',
          error: process.env.NODE_ENV === 'development' ? error.message : undefined,
        });
      case 'P2003': // Foreign key constraint failed
        return res.status(400).json({
          message: 'Invalid reference to a related record',
          error: process.env.NODE_ENV === 'development' ? error.message : undefined,
        });
      default:
        return res.status(500).json({
          message: 'Database error occurred',
          error: process.env.NODE_ENV === 'development' ? error.message : undefined,
        });
    }
  }

  // Handle validation errors
  if (error.name === 'ValidationError') {
    return res.status(400).json({
      message: 'Validation error',
      errors: error.errors,
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }

  // Handle JWT errors
  if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
    return res.status(401).json({
      message: 'Authentication error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }

  // Handle other errors
  return res.status(500).json({
    message: 'An unexpected error occurred',
    error: process.env.NODE_ENV === 'development' ? error.message : undefined,
  });
};
