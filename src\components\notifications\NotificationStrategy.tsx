'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';

export default function NotificationStrategy() {
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [config, setConfig] = useState({
    invoiceCreated: {
      enabled: true,
      primary: 'email', // email, whatsapp, both
      fallback: true,
      fallbackDelay: 24, // hours
    },
    paymentReminder: {
      enabled: true,
      primary: 'both', // email, whatsapp, both
      fallback: false,
      fallbackDelay: 24, // hours
    },
    paymentOverdue: {
      enabled: true,
      primary: 'both', // email, whatsapp, both
      escalation: true,
      escalationSchedule: [
        { days: 1, method: 'email' },
        { days: 3, method: 'both' },
        { days: 7, method: 'both' },
        { days: 14, method: 'both' },
      ]
    },
    paymentReceived: {
      enabled: true,
      primary: 'email', // email, whatsapp, both
      fallback: false,
    },
    businessHours: {
      restrictToBusinessHours: true,
      startTime: '09:00',
      endTime: '17:00',
      workDays: [1, 2, 3, 4, 5], // Monday to Friday
    }
  });

  const handleSaveConfig = () => {
    // In a real implementation, this would call your API
    setSuccessMessage('Notification strategy saved successfully');
    
    // Clear success message after 3 seconds
    setTimeout(() => {
      setSuccessMessage(null);
    }, 3000);
  };

  return (
    <div className="space-y-6">
      {successMessage && (
        <Alert className="bg-green-50 text-green-800 border-green-200">
          <AlertDescription>{successMessage}</AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Invoice Created Notifications</CardTitle>
          <CardDescription>
            Configure how customers are notified when a new invoice is created
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="invoiceCreatedEnabled">Enable Notifications</Label>
              <Switch
                id="invoiceCreatedEnabled"
                checked={config.invoiceCreated.enabled}
                onCheckedChange={(checked) => setConfig({
                  ...config,
                  invoiceCreated: {
                    ...config.invoiceCreated,
                    enabled: checked
                  }
                })}
              />
            </div>

            {config.invoiceCreated.enabled && (
              <>
                <div className="space-y-2">
                  <Label>Primary Notification Method</Label>
                  <RadioGroup 
                    value={config.invoiceCreated.primary}
                    onValueChange={(value) => setConfig({
                      ...config,
                      invoiceCreated: {
                        ...config.invoiceCreated,
                        primary: value
                      }
                    })}
                    className="flex flex-col space-y-1"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="email" id="invoiceCreated-email" />
                      <Label htmlFor="invoiceCreated-email">Email only</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="whatsapp" id="invoiceCreated-whatsapp" />
                      <Label htmlFor="invoiceCreated-whatsapp">WhatsApp only</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="both" id="invoiceCreated-both" />
                      <Label htmlFor="invoiceCreated-both">Both Email and WhatsApp</Label>
                    </div>
                  </RadioGroup>
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="invoiceCreatedFallback">Enable Fallback</Label>
                  <Switch
                    id="invoiceCreatedFallback"
                    checked={config.invoiceCreated.fallback}
                    onCheckedChange={(checked) => setConfig({
                      ...config,
                      invoiceCreated: {
                        ...config.invoiceCreated,
                        fallback: checked
                      }
                    })}
                  />
                </div>

                {config.invoiceCreated.fallback && (
                  <div className="space-y-2 pl-6 border-l-2 border-gray-100">
                    <Label htmlFor="fallbackDelay">Fallback Delay (hours)</Label>
                    <Input 
                      id="fallbackDelay" 
                      type="number"
                      min="1"
                      max="72"
                      value={config.invoiceCreated.fallbackDelay.toString()} 
                      onChange={(e) => setConfig({
                        ...config,
                        invoiceCreated: {
                          ...config.invoiceCreated,
                          fallbackDelay: parseInt(e.target.value) || 24
                        }
                      })}
                    />
                    <p className="text-sm text-gray-500">
                      If the primary method fails or isn't read, try the alternative method after this delay
                    </p>
                  </div>
                )}
              </>
            )}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Payment Reminder Notifications</CardTitle>
          <CardDescription>
            Configure how customers are reminded about upcoming payments
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="paymentReminderEnabled">Enable Reminders</Label>
              <Switch
                id="paymentReminderEnabled"
                checked={config.paymentReminder.enabled}
                onCheckedChange={(checked) => setConfig({
                  ...config,
                  paymentReminder: {
                    ...config.paymentReminder,
                    enabled: checked
                  }
                })}
              />
            </div>

            {config.paymentReminder.enabled && (
              <div className="space-y-2">
                <Label>Primary Notification Method</Label>
                <RadioGroup 
                  value={config.paymentReminder.primary}
                  onValueChange={(value) => setConfig({
                    ...config,
                    paymentReminder: {
                      ...config.paymentReminder,
                      primary: value
                    }
                  })}
                  className="flex flex-col space-y-1"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="email" id="paymentReminder-email" />
                    <Label htmlFor="paymentReminder-email">Email only</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="whatsapp" id="paymentReminder-whatsapp" />
                    <Label htmlFor="paymentReminder-whatsapp">WhatsApp only</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="both" id="paymentReminder-both" />
                    <Label htmlFor="paymentReminder-both">Both Email and WhatsApp</Label>
                  </div>
                </RadioGroup>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Overdue Payment Notifications</CardTitle>
          <CardDescription>
            Configure escalation strategy for overdue invoices
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="overdueEnabled">Enable Overdue Notifications</Label>
              <Switch
                id="overdueEnabled"
                checked={config.paymentOverdue.enabled}
                onCheckedChange={(checked) => setConfig({
                  ...config,
                  paymentOverdue: {
                    ...config.paymentOverdue,
                    enabled: checked
                  }
                })}
              />
            </div>

            {config.paymentOverdue.enabled && (
              <>
                <div className="flex items-center justify-between">
                  <Label htmlFor="escalationEnabled">Enable Escalation</Label>
                  <Switch
                    id="escalationEnabled"
                    checked={config.paymentOverdue.escalation}
                    onCheckedChange={(checked) => setConfig({
                      ...config,
                      paymentOverdue: {
                        ...config.paymentOverdue,
                        escalation: checked
                      }
                    })}
                  />
                </div>

                {config.paymentOverdue.escalation && (
                  <div className="space-y-2 pl-6 border-l-2 border-gray-100">
                    <Label>Escalation Schedule</Label>
                    <div className="space-y-2">
                      {config.paymentOverdue.escalationSchedule.map((step, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <div className="w-16">
                            <Input 
                              type="number"
                              min="1"
                              value={step.days.toString()}
                              onChange={(e) => {
                                const newSchedule = [...config.paymentOverdue.escalationSchedule];
                                newSchedule[index] = {
                                  ...newSchedule[index],
                                  days: parseInt(e.target.value) || 1
                                };
                                setConfig({
                                  ...config,
                                  paymentOverdue: {
                                    ...config.paymentOverdue,
                                    escalationSchedule: newSchedule
                                  }
                                });
                              }}
                            />
                          </div>
                          <span>days:</span>
                          <Select 
                            value={step.method}
                            onValueChange={(value) => {
                              const newSchedule = [...config.paymentOverdue.escalationSchedule];
                              newSchedule[index] = {
                                ...newSchedule[index],
                                method: value
                              };
                              setConfig({
                                ...config,
                                paymentOverdue: {
                                  ...config.paymentOverdue,
                                  escalationSchedule: newSchedule
                                }
                              });
                            }}
                          >
                            <SelectTrigger className="w-32">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="email">Email</SelectItem>
                              <SelectItem value="whatsapp">WhatsApp</SelectItem>
                              <SelectItem value="both">Both</SelectItem>
                            </SelectContent>
                          </Select>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => {
                              const newSchedule = [...config.paymentOverdue.escalationSchedule];
                              newSchedule.splice(index, 1);
                              setConfig({
                                ...config,
                                paymentOverdue: {
                                  ...config.paymentOverdue,
                                  escalationSchedule: newSchedule
                                }
                              });
                            }}
                          >
                            ✕
                          </Button>
                        </div>
                      ))}
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => {
                          const newSchedule = [...config.paymentOverdue.escalationSchedule];
                          const lastDay = newSchedule.length > 0 
                            ? newSchedule[newSchedule.length - 1].days + 7 
                            : 1;
                          newSchedule.push({ days: lastDay, method: 'both' });
                          setConfig({
                            ...config,
                            paymentOverdue: {
                              ...config.paymentOverdue,
                              escalationSchedule: newSchedule
                            }
                          });
                        }}
                      >
                        + Add Step
                      </Button>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Business Hours</CardTitle>
          <CardDescription>
            Restrict when notifications are sent
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="restrictBusinessHours">Restrict to Business Hours</Label>
              <Switch
                id="restrictBusinessHours"
                checked={config.businessHours.restrictToBusinessHours}
                onCheckedChange={(checked) => setConfig({
                  ...config,
                  businessHours: {
                    ...config.businessHours,
                    restrictToBusinessHours: checked
                  }
                })}
              />
            </div>

            {config.businessHours.restrictToBusinessHours && (
              <div className="space-y-4 pl-6 border-l-2 border-gray-100">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="startTime">Start Time</Label>
                    <Input 
                      id="startTime" 
                      type="time"
                      value={config.businessHours.startTime} 
                      onChange={(e) => setConfig({
                        ...config,
                        businessHours: {
                          ...config.businessHours,
                          startTime: e.target.value
                        }
                      })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="endTime">End Time</Label>
                    <Input 
                      id="endTime" 
                      type="time"
                      value={config.businessHours.endTime} 
                      onChange={(e) => setConfig({
                        ...config,
                        businessHours: {
                          ...config.businessHours,
                          endTime: e.target.value
                        }
                      })}
                    />
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
        <CardFooter>
          <Button onClick={handleSaveConfig}>Save Strategy Settings</Button>
        </CardFooter>
      </Card>
    </div>
  );
}
