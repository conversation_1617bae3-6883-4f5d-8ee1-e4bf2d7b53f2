'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/components/ui/use-toast';
import { AccountType, CreateAccountRequest, UpdateAccountRequest, Account } from '@/lib/api/bookkeeping.service';
import bookkeepingService from '@/lib/api/bookkeeping.service';

// Define the form schema with Zod
const accountFormSchema = z.object({
  accountCode: z.string()
    .min(1, { message: 'Account code is required' })
    .regex(/^[0-9-]+$/, { message: 'Account code must contain only numbers and hyphens' }),
  accountName: z.string()
    .min(1, { message: 'Account name is required' })
    .max(100, { message: 'Account name must be less than 100 characters' }),
  accountType: z.nativeEnum(AccountType, {
    errorMap: () => ({ message: 'Please select an account type' }),
  }),
  accountSubType: z.string()
    .min(1, { message: 'Account subtype is required' }),
  description: z.string().optional(),
  isActive: z.boolean().default(true),
  taxCode: z.string().optional(),
});

type AccountFormValues = z.infer<typeof accountFormSchema>;

// Define the account subtypes for each account type
const accountSubTypes: Record<AccountType, string[]> = {
  [AccountType.ASSET]: [
    'Current Asset',
    'Fixed Asset',
    'Non-Current Asset',
    'Intangible Asset',
  ],
  [AccountType.LIABILITY]: [
    'Current Liability',
    'Non-Current Liability',
    'Long-term Liability',
  ],
  [AccountType.EQUITY]: [
    'Capital',
    'Retained Earnings',
    'Reserves',
  ],
  [AccountType.REVENUE]: [
    'Operating Revenue',
    'Non-Operating Revenue',
    'Other Income',
  ],
  [AccountType.EXPENSE]: [
    'Operating Expense',
    'Direct Cost',
    'Indirect Cost',
    'Administrative Expense',
    'Selling Expense',
    'Financial Expense',
  ],
};

interface AccountFormProps {
  account?: Account;
  onSuccess?: (account: Account) => void;
  onCancel?: () => void;
}

export function AccountForm({ account, onSuccess, onCancel }: AccountFormProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedAccountType, setSelectedAccountType] = useState<AccountType>(
    account?.accountType || AccountType.ASSET
  );

  // Initialize the form with default values or existing account data
  const form = useForm<AccountFormValues>({
    resolver: zodResolver(accountFormSchema),
    defaultValues: account
      ? {
          accountCode: account.accountCode,
          accountName: account.accountName,
          accountType: account.accountType,
          accountSubType: account.accountSubType,
          description: account.description || '',
          isActive: account.isActive,
          taxCode: account.taxCode || '',
        }
      : {
          accountCode: '',
          accountName: '',
          accountType: AccountType.ASSET,
          accountSubType: 'Current Asset',
          description: '',
          isActive: true,
          taxCode: '',
        },
  });

  // Handle form submission
  const onSubmit = async (values: AccountFormValues) => {
    setIsSubmitting(true);
    try {
      let result: Account;

      if (account) {
        // Update existing account
        const updateData: UpdateAccountRequest = {
          accountName: values.accountName,
          accountSubType: values.accountSubType,
          description: values.description,
          isActive: values.isActive,
          taxCode: values.taxCode,
        };
        result = await bookkeepingService.updateAccount(account.id, updateData);
        toast({
          title: 'Account Updated',
          description: 'The account has been updated successfully.',
          variant: 'default',
        });
      } else {
        // Create new account
        const createData: CreateAccountRequest = {
          accountCode: values.accountCode,
          accountName: values.accountName,
          accountType: values.accountType,
          accountSubType: values.accountSubType,
          description: values.description,
          isActive: values.isActive,
          taxCode: values.taxCode,
        };
        result = await bookkeepingService.createAccount(createData);
        toast({
          title: 'Account Created',
          description: 'The account has been created successfully.',
          variant: 'default',
        });
      }

      // Call the onSuccess callback if provided
      if (onSuccess) {
        onSuccess(result);
      }
    } catch (error: any) {
      toast({
        title: account ? 'Update Failed' : 'Creation Failed',
        description: error.message || 'An error occurred. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Account Code */}
          <FormField
            control={form.control}
            name="accountCode"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Account Code</FormLabel>
                <FormControl>
                  <Input
                    placeholder="e.g., 1-1000"
                    {...field}
                    disabled={!!account} // Disable editing for existing accounts
                  />
                </FormControl>
                <FormDescription>
                  Enter a unique code for this account (e.g., 1-1000 for Cash)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Account Name */}
          <FormField
            control={form.control}
            name="accountName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Account Name</FormLabel>
                <FormControl>
                  <Input placeholder="e.g., Cash" {...field} />
                </FormControl>
                <FormDescription>
                  Enter a descriptive name for this account
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Account Type */}
          <FormField
            control={form.control}
            name="accountType"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Account Type</FormLabel>
                <Select
                  onValueChange={(value) => {
                    field.onChange(value);
                    setSelectedAccountType(value as AccountType);
                    // Reset subtype when type changes
                    form.setValue('accountSubType', accountSubTypes[value as AccountType][0]);
                  }}
                  defaultValue={field.value}
                  disabled={!!account} // Disable editing for existing accounts
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select account type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {Object.values(AccountType).map((type) => (
                      <SelectItem key={type} value={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormDescription>
                  Select the type of account
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Account Subtype */}
          <FormField
            control={form.control}
            name="accountSubType"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Account Subtype</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select account subtype" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {accountSubTypes[selectedAccountType].map((subType) => (
                      <SelectItem key={subType} value={subType}>
                        {subType}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormDescription>
                  Select a more specific category for this account
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Tax Code */}
          <FormField
            control={form.control}
            name="taxCode"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Tax Code</FormLabel>
                <FormControl>
                  <Input placeholder="e.g., SST-10" {...field} />
                </FormControl>
                <FormDescription>
                  Enter the tax code for this account (if applicable)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Is Active */}
          <FormField
            control={form.control}
            name="isActive"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">Active Account</FormLabel>
                  <FormDescription>
                    Inactive accounts will not appear in dropdown menus
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </div>

        {/* Description */}
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Enter a description for this account"
                  className="resize-none"
                  {...field}
                />
              </FormControl>
              <FormDescription>
                Provide additional details about this account (optional)
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Form Actions */}
        <div className="flex justify-end space-x-4">
          {onCancel && (
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
          )}
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? 'Saving...' : account ? 'Update Account' : 'Create Account'}
          </Button>
        </div>
      </form>
    </Form>
  );
}
