import Link from 'next/link';
import LoginForm from '@/components/forms/LoginForm';
import Image from 'next/image';

export default function LoginPage() {
  return (
    <div className="min-h-screen flex flex-col md:flex-row">
      {/* Left side - Login form */}
      <div className="flex-1 flex flex-col justify-center items-center p-8 md:p-12">
        <div className="w-full max-w-md">
          <div className="mb-8 text-center">
            <Link href="/" className="inline-block">
              <h1 className="text-3xl font-bold text-indigo-600">Invoix</h1>
            </Link>
          </div>
          <LoginForm />
        </div>
      </div>
      
      {/* Right side - Image and info */}
      <div className="hidden md:flex flex-1 bg-indigo-600 text-white p-12 flex-col justify-center">
        <div className="max-w-md mx-auto">
          <h2 className="text-3xl font-bold mb-6">Welcome back to Invoix</h2>
          <p className="text-lg mb-8">
            Log in to access your invoicing dashboard, manage customers, and track payments.
          </p>
          <div className="bg-indigo-500/30 p-6 rounded-lg">
            <h3 className="text-xl font-semibold mb-4">Why choose Invoix?</h3>
            <ul className="space-y-2">
              <li className="flex items-start">
                <svg className="h-6 w-6 mr-2 text-indigo-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span>LHDN MyInvois validation for tax compliance</span>
              </li>
              <li className="flex items-start">
                <svg className="h-6 w-6 mr-2 text-indigo-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span>WhatsApp integration for customer communication</span>
              </li>
              <li className="flex items-start">
                <svg className="h-6 w-6 mr-2 text-indigo-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span>AI-powered insights for better financial decisions</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
