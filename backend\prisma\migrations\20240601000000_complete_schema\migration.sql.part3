-- Supplier and Procurement Module

-- Create Supplier table if not exists
CREATE TABLE IF NOT EXISTS "Supplier" (
  "id" TEXT NOT NULL,
  "name" TEXT NOT NULL,
  "contact<PERSON>erson" TEXT,
  "email" TEXT NOT NULL,
  "phone" TEXT,
  "address" TEXT,
  "taxId" TEXT,
  "paymentTerms" INTEGER NOT NULL DEFAULT 30,
  "isActive" BOOLEAN NOT NULL DEFAULT true,
  "tenantId" TEXT NOT NULL,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  CONSTRAINT "Supplier_pkey" PRIMARY KEY ("id")
);

-- Create unique constraint on Supplier
ALTER TABLE "Supplier" ADD CONSTRAINT "Supplier_email_tenantId_key" UNIQUE ("email", "tenantId");

-- Create PurchaseOrder table if not exists
CREATE TABLE IF NOT EXISTS "PurchaseOrder" (
  "id" TEXT NOT NULL,
  "poNumber" TEXT NOT NULL,
  "orderDate" TIMESTAMP(3) NOT NULL,
  "expectedDelivery" TIMESTAMP(3),
  "status" "PurchaseOrderStatus" NOT NULL DEFAULT 'DRAFT',
  "totalAmount" DOUBLE PRECISION NOT NULL,
  "notes" TEXT,
  "supplierId" TEXT NOT NULL,
  "tenantId" TEXT NOT NULL,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  CONSTRAINT "PurchaseOrder_pkey" PRIMARY KEY ("id")
);

-- Create unique constraint on PurchaseOrder
ALTER TABLE "PurchaseOrder" ADD CONSTRAINT "PurchaseOrder_poNumber_tenantId_key" UNIQUE ("poNumber", "tenantId");

-- Create PurchaseOrderItem table if not exists
CREATE TABLE IF NOT EXISTS "PurchaseOrderItem" (
  "id" TEXT NOT NULL,
  "purchaseOrderId" TEXT NOT NULL,
  "productId" TEXT NOT NULL,
  "description" TEXT NOT NULL,
  "quantity" DOUBLE PRECISION NOT NULL,
  "unitPrice" DOUBLE PRECISION NOT NULL,
  "amount" DOUBLE PRECISION NOT NULL,
  "receivedQuantity" DOUBLE PRECISION NOT NULL DEFAULT 0,
  "tenantId" TEXT NOT NULL,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  CONSTRAINT "PurchaseOrderItem_pkey" PRIMARY KEY ("id")
);

-- Create GoodsReceipt table if not exists
CREATE TABLE IF NOT EXISTS "GoodsReceipt" (
  "id" TEXT NOT NULL,
  "receiptNumber" TEXT NOT NULL,
  "receiptDate" TIMESTAMP(3) NOT NULL,
  "purchaseOrderId" TEXT NOT NULL,
  "notes" TEXT,
  "tenantId" TEXT NOT NULL,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  CONSTRAINT "GoodsReceipt_pkey" PRIMARY KEY ("id")
);

-- Create unique constraint on GoodsReceipt
ALTER TABLE "GoodsReceipt" ADD CONSTRAINT "GoodsReceipt_receiptNumber_tenantId_key" UNIQUE ("receiptNumber", "tenantId");

-- Create GoodsReceiptItem table if not exists
CREATE TABLE IF NOT EXISTS "GoodsReceiptItem" (
  "id" TEXT NOT NULL,
  "goodsReceiptId" TEXT NOT NULL,
  "purchaseOrderItemId" TEXT NOT NULL,
  "quantity" DOUBLE PRECISION NOT NULL,
  "notes" TEXT,
  "tenantId" TEXT NOT NULL,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  CONSTRAINT "GoodsReceiptItem_pkey" PRIMARY KEY ("id")
);

-- Fixed Asset Management Module

-- Create FixedAsset table if not exists
CREATE TABLE IF NOT EXISTS "FixedAsset" (
  "id" TEXT NOT NULL,
  "assetNumber" TEXT NOT NULL,
  "name" TEXT NOT NULL,
  "description" TEXT,
  "category" "AssetCategory" NOT NULL,
  "purchaseDate" TIMESTAMP(3) NOT NULL,
  "purchasePrice" DOUBLE PRECISION NOT NULL,
  "currentValue" DOUBLE PRECISION NOT NULL,
  "location" TEXT,
  "assignedTo" TEXT,
  "status" "AssetStatus" NOT NULL DEFAULT 'ACTIVE',
  "disposalDate" TIMESTAMP(3),
  "disposalValue" DOUBLE PRECISION,
  "tenantId" TEXT NOT NULL,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  CONSTRAINT "FixedAsset_pkey" PRIMARY KEY ("id")
);

-- Create unique constraint on FixedAsset
ALTER TABLE "FixedAsset" ADD CONSTRAINT "FixedAsset_assetNumber_tenantId_key" UNIQUE ("assetNumber", "tenantId");

-- Create AssetDepreciation table if not exists
CREATE TABLE IF NOT EXISTS "AssetDepreciation" (
  "id" TEXT NOT NULL,
  "assetId" TEXT NOT NULL,
  "depreciationDate" TIMESTAMP(3) NOT NULL,
  "depreciationAmount" DOUBLE PRECISION NOT NULL,
  "bookValue" DOUBLE PRECISION NOT NULL,
  "tenantId" TEXT NOT NULL,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  CONSTRAINT "AssetDepreciation_pkey" PRIMARY KEY ("id")
);

-- Create AssetMaintenance table if not exists
CREATE TABLE IF NOT EXISTS "AssetMaintenance" (
  "id" TEXT NOT NULL,
  "assetId" TEXT NOT NULL,
  "maintenanceDate" TIMESTAMP(3) NOT NULL,
  "description" TEXT NOT NULL,
  "cost" DOUBLE PRECISION NOT NULL,
  "provider" TEXT,
  "nextMaintenanceDate" TIMESTAMP(3),
  "tenantId" TEXT NOT NULL,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  CONSTRAINT "AssetMaintenance_pkey" PRIMARY KEY ("id")
);
