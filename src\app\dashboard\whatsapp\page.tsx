'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';

export default function WhatsAppPage() {
  const [activeTab, setActiveTab] = useState('config');
  const [searchTerm, setSearchTerm] = useState('');
  const [isConnected, setIsConnected] = useState(false);
  const [provider, setProvider] = useState<'twilio' | '360dialog'>('twilio');
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  
  // Mock configuration data
  const [config, setConfig] = useState({
    twilio: {
      accountSid: '',
      authToken: '',
      phoneNumber: '',
    },
    '360dialog': {
      apiKey: '',
      phoneNumber: '',
    },
    templates: [
      {
        id: 'invoice_created',
        name: 'Invoice Created',
        status: 'approved',
        content: 'Hello {{1}}, your invoice #{{2}} for {{3}} has been created. You can view it at {{4}}.',
        variables: ['customer_name', 'invoice_number', 'amount', 'invoice_url'],
      },
      {
        id: 'payment_reminder',
        name: 'Payment Reminder',
        status: 'approved',
        content: 'Hello {{1}}, this is a reminder that invoice #{{2}} for {{3}} is due on {{4}}. Please make payment at your earliest convenience.',
        variables: ['customer_name', 'invoice_number', 'amount', 'due_date'],
      },
      {
        id: 'payment_received',
        name: 'Payment Received',
        status: 'pending',
        content: 'Hello {{1}}, we have received your payment of {{2}} for invoice #{{3}}. Thank you for your business!',
        variables: ['customer_name', 'amount', 'invoice_number'],
      },
    ],
    settings: {
      enableAutoSend: true,
      enablePaymentReminders: true,
      reminderDays: 3,
    }
  });

  // Mock message history data
  const [messages, setMessages] = useState([
    { 
      id: 'msg-001', 
      templateName: 'Invoice Created',
      recipient: '+***********',
      recipientName: 'John Doe',
      status: 'delivered',
      sentAt: '2023-06-20T10:30:00Z',
      deliveredAt: '2023-06-20T10:30:05Z',
      readAt: null,
      invoiceId: 'INV-2023-001',
    },
    { 
      id: 'msg-002', 
      templateName: 'Payment Reminder',
      recipient: '+***********',
      recipientName: 'Jane Smith',
      status: 'read',
      sentAt: '2023-06-19T14:15:00Z',
      deliveredAt: '2023-06-19T14:15:10Z',
      readAt: '2023-06-19T15:20:00Z',
      invoiceId: 'INV-2023-002',
    },
    { 
      id: 'msg-003', 
      templateName: 'Invoice Created',
      recipient: '+60187654321',
      recipientName: 'Acme Corporation',
      status: 'sent',
      sentAt: '2023-06-20T09:45:00Z',
      deliveredAt: null,
      readAt: null,
      invoiceId: 'INV-2023-003',
    },
    { 
      id: 'msg-004', 
      templateName: 'Payment Received',
      recipient: '+60176543210',
      recipientName: 'Wayne Enterprises',
      status: 'failed',
      sentAt: '2023-06-18T16:20:00Z',
      deliveredAt: null,
      readAt: null,
      invoiceId: 'INV-2023-004',
      error: 'Invalid phone number format',
    },
  ]);

  const handleSaveConfig = () => {
    // In a real implementation, this would call your API
    setSuccessMessage('WhatsApp configuration saved successfully');
    
    // Clear success message after 3 seconds
    setTimeout(() => {
      setSuccessMessage(null);
    }, 3000);
  };

  const handleConnect = () => {
    // In a real implementation, this would call your API to connect to WhatsApp
    setIsConnected(true);
    setSuccessMessage('Connected to WhatsApp successfully');
    
    // Clear success message after 3 seconds
    setTimeout(() => {
      setSuccessMessage(null);
    }, 3000);
  };

  const handleDisconnect = () => {
    // In a real implementation, this would call your API to disconnect from WhatsApp
    setIsConnected(false);
    setSuccessMessage('Disconnected from WhatsApp successfully');
    
    // Clear success message after 3 seconds
    setTimeout(() => {
      setSuccessMessage(null);
    }, 3000);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'delivered':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'read':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between bg-white p-6 rounded-lg shadow-sm mb-6">
        <div>
          <h1 className="text-2xl font-bold text-text-primary">WhatsApp Integration</h1>
          <p className="mt-1 text-sm text-text-secondary">
            Configure and manage your WhatsApp integration
          </p>
        </div>
        <div className="mt-4 md:mt-0">
          {isConnected ? (
            <Button variant="destructive" onClick={handleDisconnect}>
              Disconnect WhatsApp
            </Button>
          ) : (
            <Button onClick={handleConnect}>
              Connect WhatsApp
            </Button>
          )}
        </div>
      </div>

      {successMessage && (
        <Alert variant="success" className="bg-green-50 text-green-800 border-green-200">
          <AlertDescription>{successMessage}</AlertDescription>
        </Alert>
      )}

      {errorMessage && (
        <Alert variant="destructive">
          <AlertDescription>{errorMessage}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="config" onValueChange={setActiveTab}>
        <TabsList className="mb-6 bg-white p-1 rounded-lg border">
          <TabsTrigger value="config" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">Configuration</TabsTrigger>
          <TabsTrigger value="templates" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">Message Templates</TabsTrigger>
          <TabsTrigger value="history" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">Message History</TabsTrigger>
        </TabsList>
        
        <TabsContent value="config" className="space-y-6">
          <Card className="border-none shadow-md">
            <CardHeader className="pb-2 border-b">
              <CardTitle className="text-lg font-bold text-text-primary">Provider Settings</CardTitle>
              <CardDescription className="text-text-secondary">
                Configure your WhatsApp provider settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="twilio" onValueChange={(value) => setProvider(value as 'twilio' | '360dialog')}>
                <TabsList className="mb-6 bg-white p-1 rounded-lg border">
                  <TabsTrigger value="twilio" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">Twilio</TabsTrigger>
                  <TabsTrigger value="360dialog" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">360dialog</TabsTrigger>
                </TabsList>
                
                <TabsContent value="twilio" className="space-y-4 mt-4">
                  <div className="space-y-2">
                    <Label htmlFor="accountSid">Account SID</Label>
                    <Input 
                      id="accountSid" 
                      value={config.twilio.accountSid} 
                      onChange={(e) => setConfig({
                        ...config,
                        twilio: {
                          ...config.twilio,
                          accountSid: e.target.value
                        }
                      })}
                      placeholder="Enter your Twilio Account SID"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="authToken">Auth Token</Label>
                    <Input 
                      id="authToken" 
                      type="password"
                      value={config.twilio.authToken} 
                      onChange={(e) => setConfig({
                        ...config,
                        twilio: {
                          ...config.twilio,
                          authToken: e.target.value
                        }
                      })}
                      placeholder="Enter your Twilio Auth Token"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="phoneNumber">WhatsApp Phone Number</Label>
                    <Input 
                      id="phoneNumber" 
                      value={config.twilio.phoneNumber} 
                      onChange={(e) => setConfig({
                        ...config,
                        twilio: {
                          ...config.twilio,
                          phoneNumber: e.target.value
                        }
                      })}
                      placeholder="Enter your WhatsApp phone number (with country code)"
                    />
                  </div>
                </TabsContent>
                
                <TabsContent value="360dialog" className="space-y-4 mt-4">
                  <div className="space-y-2">
                    <Label htmlFor="apiKey">API Key</Label>
                    <Input 
                      id="apiKey" 
                      value={config['360dialog'].apiKey} 
                      onChange={(e) => setConfig({
                        ...config,
                        '360dialog': {
                          ...config['360dialog'],
                          apiKey: e.target.value
                        }
                      })}
                      placeholder="Enter your 360dialog API Key"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="phoneNumber360">WhatsApp Phone Number</Label>
                    <Input 
                      id="phoneNumber360" 
                      value={config['360dialog'].phoneNumber} 
                      onChange={(e) => setConfig({
                        ...config,
                        '360dialog': {
                          ...config['360dialog'],
                          phoneNumber: e.target.value
                        }
                      })}
                      placeholder="Enter your WhatsApp phone number (with country code)"
                    />
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSaveConfig}>Save Provider Settings</Button>
            </CardFooter>
          </Card>

          <Card className="border-none shadow-md">
            <CardHeader className="pb-2 border-b">
              <CardTitle className="text-lg font-bold text-text-primary">Notification Settings</CardTitle>
              <CardDescription className="text-text-secondary">
                Configure your WhatsApp notification settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="autoSend">Automatic Notifications</Label>
                    <p className="text-sm text-text-secondary">
                      Automatically send WhatsApp notifications when invoices are created
                    </p>
                  </div>
                  <Switch
                    id="autoSend"
                    checked={config.settings.enableAutoSend}
                    onCheckedChange={(checked) => setConfig({
                      ...config,
                      settings: {
                        ...config.settings,
                        enableAutoSend: checked
                      }
                    })}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="paymentReminders">Payment Reminders</Label>
                    <p className="text-sm text-text-secondary">
                      Send payment reminders via WhatsApp before due date
                    </p>
                  </div>
                  <Switch
                    id="paymentReminders"
                    checked={config.settings.enablePaymentReminders}
                    onCheckedChange={(checked) => setConfig({
                      ...config,
                      settings: {
                        ...config.settings,
                        enablePaymentReminders: checked
                      }
                    })}
                  />
                </div>
                
                {config.settings.enablePaymentReminders && (
                  <div className="space-y-2 pl-6 border-l-2 border-gray-100">
                    <Label htmlFor="reminderDays">Days Before Due Date</Label>
                    <Input 
                      id="reminderDays" 
                      type="number"
                      min="1"
                      max="30"
                      value={config.settings.reminderDays.toString()} 
                      onChange={(e) => setConfig({
                        ...config,
                        settings: {
                          ...config.settings,
                          reminderDays: parseInt(e.target.value) || 0
                        }
                      })}
                    />
                  </div>
                )}
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSaveConfig}>Save Notification Settings</Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        <TabsContent value="templates" className="space-y-6">
          <Card className="border-none shadow-md">
            <CardHeader className="pb-2 border-b">
              <CardTitle className="text-lg font-bold text-text-primary">Message Templates</CardTitle>
              <CardDescription className="text-text-secondary">
                Configure your WhatsApp message templates
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {config.templates.map((template) => (
                  <div key={template.id} className="p-4 border rounded-md">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-medium">{template.name}</h3>
                        <Badge 
                          variant={template.status === 'approved' ? 'default' : 'secondary'}
                          className="mt-1"
                        >
                          {template.status}
                        </Badge>
                      </div>
                      <Button variant="outline" size="sm">
                        Edit
                      </Button>
                    </div>
                    <div className="text-sm text-text-primary mt-2">
                      {template.content}
                    </div>
                    <div className="mt-3">
                      <h4 className="text-xs font-medium text-text-secondary mb-1">Variables:</h4>
                      <div className="flex flex-wrap gap-2">
                        {template.variables.map((variable, index) => (
                          <Badge key={index} variant="outline">
                            {variable}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
                
                <Button variant="outline" className="w-full">
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Add New Template
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="history" className="space-y-6">
          <Card className="border-none shadow-md">
            <CardHeader className="pb-2 border-b">
              <CardTitle className="text-lg font-bold text-text-primary">Message History</CardTitle>
              <CardDescription className="text-text-secondary">
                Recent WhatsApp messages sent to your customers
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {messages.map((message) => (
                  <div key={message.id} className="p-4 border rounded-md">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-medium">{message.templateName}</h3>
                        <p className="text-sm text-text-secondary">{message.recipientName} • {message.recipient}</p>
                      </div>
                      <Badge variant="outline" className={getStatusColor(message.status)}>
                        {message.status.charAt(0).toUpperCase() + message.status.slice(1)}
                      </Badge>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4 text-sm">
                      <div>
                        <div className="font-medium text-text-secondary">Sent</div>
                        <div>{formatDate(message.sentAt)}</div>
                      </div>
                      <div>
                        <div className="font-medium text-text-secondary">Delivered</div>
                        <div>{formatDate(message.deliveredAt)}</div>
                      </div>
                      <div>
                        <div className="font-medium text-text-secondary">Read</div>
                        <div>{formatDate(message.readAt)}</div>
                      </div>
                    </div>
                    {message.error && (
                      <Alert variant="destructive" className="mt-4">
                        <AlertDescription>{message.error}</AlertDescription>
                      </Alert>
                    )}
                    <div className="mt-4 text-sm text-text-secondary">
                      Invoice: <span className="font-medium">{message.invoiceId}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
