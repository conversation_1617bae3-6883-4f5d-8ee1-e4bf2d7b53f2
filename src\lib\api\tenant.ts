/**
 * Upload a tenant logo
 * @param file The logo file to upload
 * @returns Promise with the upload result
 */
export const uploadTenantLogo = async (file: File): Promise<{ logo: string }> => {
  const formData = new FormData();
  formData.append('logo', file);

  const response = await fetch('/api/tenants/logo', {
    method: 'POST',
    body: formData,
  });

  if (!response.ok) {
    const data = await response.json();
    throw new Error(data.error || 'Failed to upload logo');
  }

  return response.json();
};

/**
 * Delete a tenant logo
 * @returns Promise with the delete result
 */
export const deleteTenantLogo = async (): Promise<{ message: string }> => {
  const response = await fetch('/api/tenants/logo', {
    method: 'DELETE',
  });

  if (!response.ok) {
    const data = await response.json();
    throw new Error(data.error || 'Failed to delete logo');
  }

  return response.json();
};

/**
 * Get the URL for a tenant logo
 * @param filename The logo filename
 * @returns The URL for the logo
 */
export const getTenantLogoUrl = (filename: string): string => {
  if (!filename) return '';
  return `/api/tenants/logo/${filename}`;
};

/**
 * Get the current tenant data
 * @returns Promise with the tenant data
 */
export const getCurrentTenant = async (): Promise<any> => {
  const response = await fetch('/api/tenants/current');

  if (!response.ok) {
    const data = await response.json();
    throw new Error(data.error || 'Failed to fetch tenant data');
  }

  return response.json();
};
