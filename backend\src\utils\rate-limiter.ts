import { logger } from './logger';

/**
 * Simple in-memory rate limiter
 */
class RateLimiter {
  private limits: Map<string, { count: number; resetAt: number }> = new Map();
  private readonly defaultMaxRequests: number;
  private readonly defaultWindowMs: number;

  /**
   * Create a new rate limiter
   * @param defaultMaxRequests Maximum number of requests allowed in the time window
   * @param defaultWindowMs Time window in milliseconds
   */
  constructor(defaultMaxRequests = 100, defaultWindowMs = 60000) {
    this.defaultMaxRequests = defaultMaxRequests;
    this.defaultWindowMs = defaultWindowMs;
    
    // Clean up expired entries every minute
    setInterval(() => this.cleanup(), 60000);
  }

  /**
   * Check if a request is allowed
   * @param key Identifier for the rate limit (e.g., API endpoint, IP address)
   * @param maxRequests Maximum number of requests allowed in the time window
   * @param windowMs Time window in milliseconds
   * @returns Whether the request is allowed
   */
  isAllowed(
    key: string,
    maxRequests = this.defaultMaxRequests,
    windowMs = this.defaultWindowMs
  ): boolean {
    const now = Date.now();
    const limit = this.limits.get(key);
    
    // If no limit exists or it has expired, create a new one
    if (!limit || limit.resetAt <= now) {
      this.limits.set(key, {
        count: 1,
        resetAt: now + windowMs,
      });
      return true;
    }
    
    // If under the limit, increment the count
    if (limit.count < maxRequests) {
      limit.count++;
      return true;
    }
    
    // Rate limit exceeded
    logger.warn(`Rate limit exceeded for ${key}`, {
      key,
      maxRequests,
      windowMs,
      currentCount: limit.count,
      resetAt: new Date(limit.resetAt).toISOString(),
    });
    
    return false;
  }

  /**
   * Get the remaining requests for a key
   * @param key Identifier for the rate limit
   * @returns Remaining requests and time until reset
   */
  getRemainingRequests(key: string): { remaining: number; resetIn: number } {
    const now = Date.now();
    const limit = this.limits.get(key);
    
    if (!limit || limit.resetAt <= now) {
      return {
        remaining: this.defaultMaxRequests,
        resetIn: this.defaultWindowMs,
      };
    }
    
    return {
      remaining: Math.max(0, this.defaultMaxRequests - limit.count),
      resetIn: limit.resetAt - now,
    };
  }

  /**
   * Clean up expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    let expiredCount = 0;
    
    for (const [key, limit] of this.limits.entries()) {
      if (limit.resetAt <= now) {
        this.limits.delete(key);
        expiredCount++;
      }
    }
    
    if (expiredCount > 0) {
      logger.debug(`Cleaned up ${expiredCount} expired rate limit entries`);
    }
  }
}

// Create rate limiters for different LHDN API endpoints
export const lhdnRateLimiters = {
  validateBusinessRegNo: new RateLimiter(50, 60000), // 50 requests per minute
  validateInvoice: new RateLimiter(30, 60000), // 30 requests per minute
  getDocumentStatus: new RateLimiter(100, 60000), // 100 requests per minute
  cancelDocument: new RateLimiter(20, 60000), // 20 requests per minute
  rejectDocument: new RateLimiter(20, 60000), // 20 requests per minute
  searchDocuments: new RateLimiter(30, 60000), // 30 requests per minute
};
