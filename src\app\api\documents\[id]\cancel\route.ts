import { NextRequest, NextResponse } from 'next/server';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    const { reason } = body;
    
    if (!reason) {
      return NextResponse.json(
        { message: 'Cancellation reason is required' },
        { status: 400 }
      );
    }
    
    // In a real implementation, this would call your backend API
    // const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/documents/${id}/cancel`, {
    //   method: 'POST',
    //   headers: {
    //     'Content-Type': 'application/json',
    //     'Authorization': `Bearer ${session.accessToken}`,
    //   },
    //   body: JSON.stringify({ reason }),
    // });
    // const data = await response.json();
    
    // For development, use mock data
    return NextResponse.json({
      message: 'Invoice cancelled successfully',
      invoice: {
        id,
        status: 'CANCELLED',
      },
      lhdn: {
        success: true,
        documentId: id,
        status: 'CANCELLED',
        timestamp: new Date().toISOString(),
        message: 'Document cancelled successfully (mock)',
      },
    });
  } catch (error) {
    console.error('Error cancelling document:', error);
    return NextResponse.json(
      { message: 'Failed to cancel document' },
      { status: 500 }
    );
  }
}
