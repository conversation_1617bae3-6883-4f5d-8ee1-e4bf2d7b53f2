import nodemailer from 'nodemailer';
import { Transporter } from 'nodemailer';
import Handlebars from 'handlebars';
import { prisma } from '../index';
import { logger } from '../utils/logger';
import { decrypt } from '../utils/encryption';
import path from 'path';
import fs from 'fs';

/**
 * Email Service
 * Handles email sending for the application
 */
export class EmailService {
  private defaultTransporter: Transporter | null = null;
  private tenantTransporters: Map<string, Transporter> = new Map();

  constructor() {
    // Initialize default transporter if environment variables are set
    if (process.env.EMAIL_HOST && process.env.EMAIL_PORT) {
      this.defaultTransporter = nodemailer.createTransport({
        host: process.env.EMAIL_HOST,
        port: parseInt(process.env.EMAIL_PORT, 10),
        secure: parseInt(process.env.EMAIL_PORT, 10) === 465,
        auth: {
          user: process.env.EMAIL_USER,
          pass: process.env.EMAIL_PASS,
        },
      });
    }
  }

  /**
   * Get email transporter for a tenant
   * @param tenantId Tenant ID
   * @returns Nodemailer transporter
   */
  private async getTransporter(tenantId: string): Promise<Transporter> {
    // Check if we already have a transporter for this tenant
    if (this.tenantTransporters.has(tenantId)) {
      return this.tenantTransporters.get(tenantId)!;
    }

    // Get tenant email settings
    const emailSettings = await prisma.emailSetting.findUnique({
      where: { tenantId },
    });

    // If no settings found or not active, use default transporter
    if (!emailSettings || !emailSettings.isActive) {
      if (!this.defaultTransporter) {
        throw new Error('No email transporter available');
      }
      return this.defaultTransporter;
    }

    // Create transporter for tenant
    const transporter = nodemailer.createTransport({
      host: emailSettings.host,
      port: emailSettings.port,
      secure: emailSettings.port === 465,
      auth: {
        user: emailSettings.username,
        pass: decrypt(emailSettings.password),
      },
    });

    // Cache transporter
    this.tenantTransporters.set(tenantId, transporter);

    return transporter;
  }

  /**
   * Get email template
   * @param templateName Template name
   * @param tenantId Tenant ID
   * @returns Email template
   */
  private async getTemplate(templateName: string, tenantId: string): Promise<{ subject: string; body: string } | null> {
    // Try to get tenant-specific template
    const template = await prisma.emailTemplate.findFirst({
      where: {
        name: templateName,
        tenantId,
      },
    });

    if (template) {
      return {
        subject: template.subject,
        body: template.body,
      };
    }

    // Try to get default template
    const defaultTemplate = await prisma.emailTemplate.findFirst({
      where: {
        name: templateName,
        isDefault: true,
      },
    });

    if (defaultTemplate) {
      return {
        subject: defaultTemplate.subject,
        body: defaultTemplate.body,
      };
    }

    // Try to get from file system
    try {
      const templatePath = path.join(__dirname, '../templates/emails', `${templateName}.html`);
      if (fs.existsSync(templatePath)) {
        const templateContent = fs.readFileSync(templatePath, 'utf8');
        return {
          subject: templateName.charAt(0).toUpperCase() + templateName.slice(1).replace(/-/g, ' '),
          body: templateContent,
        };
      }
    } catch (error) {
      logger.error(`Error reading template file: ${error}`);
    }

    return null;
  }

  /**
   * Get sender information
   * @param tenantId Tenant ID
   * @returns Sender information
   */
  private async getSender(tenantId: string): Promise<{ email: string; name: string }> {
    // Get tenant email settings
    const emailSettings = await prisma.emailSetting.findUnique({
      where: { tenantId },
    });

    // If settings found, use them
    if (emailSettings) {
      return {
        email: emailSettings.fromEmail,
        name: emailSettings.fromName,
      };
    }

    // Use default sender
    return {
      email: process.env.EMAIL_FROM_EMAIL || '<EMAIL>',
      name: process.env.EMAIL_FROM_NAME || 'Invoix',
    };
  }

  /**
   * Send email
   * @param options Email options
   * @returns Send result
   */
  public async sendEmail(options: {
    to: string;
    subject: string;
    html: string;
    tenantId: string;
    attachments?: Array<{
      filename: string;
      path: string;
      contentType?: string;
    }>;
  }): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      // Get transporter
      const transporter = await this.getTransporter(options.tenantId);

      // Get sender
      const sender = await this.getSender(options.tenantId);

      // Send email
      const result = await transporter.sendMail({
        from: `${sender.name} <${sender.email}>`,
        to: options.to,
        subject: options.subject,
        html: options.html,
        attachments: options.attachments,
      });

      logger.info(`Email sent to ${options.to}: ${options.subject}`);

      return {
        success: true,
        messageId: result.messageId,
      };
    } catch (error: any) {
      logger.error(`Error sending email to ${options.to}: ${error.message}`);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Send templated email
   * @param options Email options
   * @returns Send result
   */
  public async sendTemplatedEmail(options: {
    to: string;
    templateName: string;
    templateData: Record<string, any>;
    tenantId: string;
    attachments?: Array<{
      filename: string;
      path: string;
      contentType?: string;
    }>;
  }): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      // Get template
      const template = await this.getTemplate(options.templateName, options.tenantId);

      if (!template) {
        throw new Error(`Template not found: ${options.templateName}`);
      }

      // Compile template
      const subjectTemplate = Handlebars.compile(template.subject);
      const bodyTemplate = Handlebars.compile(template.body);

      // Add common data
      const data = {
        ...options.templateData,
        year: new Date().getFullYear(),
      };

      // Render template
      const subject = subjectTemplate(data);
      const html = bodyTemplate(data);

      // Send email
      return this.sendEmail({
        to: options.to,
        subject,
        html,
        tenantId: options.tenantId,
        attachments: options.attachments,
      });
    } catch (error: any) {
      logger.error(`Error sending templated email: ${error.message}`);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Send invoice email
   * @param options Invoice email options
   * @returns Send result
   */
  public async sendInvoiceEmail(options: {
    invoiceId: string;
    tenantId: string;
    attachPdf?: boolean;
  }): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      // Get invoice with customer
      const invoice = await prisma.invoice.findUnique({
        where: { id: options.invoiceId },
        include: {
          customer: true,
          items: true,
        },
      });

      if (!invoice) {
        throw new Error(`Invoice not found: ${options.invoiceId}`);
      }

      // Get tenant
      const tenant = await prisma.tenant.findUnique({
        where: { id: options.tenantId },
      });

      if (!tenant) {
        throw new Error(`Tenant not found: ${options.tenantId}`);
      }

      // Prepare template data
      const templateData = {
        invoice_number: invoice.invoiceNumber,
        due_date: new Date(invoice.dueDate).toLocaleDateString(),
        customer_name: invoice.customer.name,
        company_name: tenant.name,
        total_amount: invoice.items.reduce((sum, item) => sum + item.amount, 0).toFixed(2),
        invoice_link: `${process.env.FRONTEND_URL}/invoices/${invoice.id}`,
      };

      // Send email
      return this.sendTemplatedEmail({
        to: invoice.customer.email,
        templateName: 'invoice_created',
        templateData,
        tenantId: options.tenantId,
        attachments: options.attachPdf
          ? [
              {
                filename: `Invoice-${invoice.invoiceNumber}.pdf`,
                path: `${process.env.STORAGE_PATH}/invoices/${invoice.id}.pdf`,
                contentType: 'application/pdf',
              },
            ]
          : undefined,
      });
    } catch (error: any) {
      logger.error(`Error sending invoice email: ${error.message}`);
      return {
        success: false,
        error: error.message,
      };
    }
  }
}

// Export singleton instance
export const emailService = new EmailService();
export default emailService;
