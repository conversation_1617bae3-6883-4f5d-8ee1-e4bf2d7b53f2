import { apiClient } from './client';

/**
 * Validate a business registration number
 * @param regNo Business registration number to validate
 */
export const validateBusinessRegistration = async (regNo: string) => {
  try {
    // Use the API client for all environments
    const response = await apiClient.get(`/validation/business/${regNo}`);

    // For development, mock a successful response
    if (process.env.NODE_ENV === 'development') {
      // Simulate API validation with a mock response
      return {
        isValid: regNo.length >= 8, // Simple validation for development
        businessName: regNo.length >= 8 ? `Business ${regNo}` : undefined,
      };
    }

    return response;
  } catch (error: any) {
    console.error('Error validating business registration:', error);
    throw new Error(error.message || 'Failed to validate business registration');
  }
};
