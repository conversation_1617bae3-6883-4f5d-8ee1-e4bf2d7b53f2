import nodemailer from 'nodemailer';
import fs from 'fs';
import path from 'path';
import Handlebars from 'handlebars';
import { logger } from './logger';

// Email configuration
const EMAIL_HOST = process.env.EMAIL_HOST || 'smtp.example.com';
const EMAIL_PORT = parseInt(process.env.EMAIL_PORT || '587', 10);
const EMAIL_USER = process.env.EMAIL_USER || '<EMAIL>';
const EMAIL_PASS = process.env.EMAIL_PASS || 'password';
const EMAIL_FROM = process.env.EMAIL_FROM || 'Invoix <<EMAIL>>';
const FRONTEND_URL = process.env.FRONTEND_URL || 'http://localhost:3000';
const LOGO_URL = `${FRONTEND_URL}/logo.png`;

// Create email transport
const transporter = nodemailer.createTransport({
  host: EMAIL_HOST,
  port: EMAIL_PORT,
  secure: EMAIL_PORT === 465, // true for 465, false for other ports
  auth: {
    user: EMAIL_USER,
    pass: EMAIL_PASS,
  },
});

// Interface for email options
interface EmailOptions {
  to: string;
  subject: string;
  template: string;
  data?: Record<string, any>;
  attachments?: Array<{
    filename: string;
    path: string;
    contentType?: string;
  }>;
}

/**
 * Send an email using a template
 * @param options Email options
 */
export const sendEmail = async (options: EmailOptions): Promise<void> => {
  try {
    // Get template
    const templatePath = path.join(__dirname, '../templates/emails', `${options.template}.html`);
    const templateSource = fs.readFileSync(templatePath, 'utf8');
    
    // Compile template
    const template = Handlebars.compile(templateSource);
    
    // Add common data
    const data = {
      ...options.data,
      logoUrl: LOGO_URL,
      year: new Date().getFullYear(),
    };
    
    // Render template
    const html = template(data);
    
    // Send email
    await transporter.sendMail({
      from: EMAIL_FROM,
      to: options.to,
      subject: options.subject,
      html,
      attachments: options.attachments,
    });
    
    logger.info(`Email sent to ${options.to}: ${options.subject}`);
  } catch (error) {
    logger.error('Error sending email:', error);
    throw error;
  }
};

/**
 * Send welcome email with verification link
 * @param email User email
 * @param name User name
 * @param token Verification token
 */
export const sendWelcomeEmail = async (email: string, name: string, token: string): Promise<void> => {
  const verificationUrl = `${FRONTEND_URL}/verify-email/${token}`;
  
  await sendEmail({
    to: email,
    subject: 'Welcome to Invoix - Verify Your Email',
    template: 'welcome',
    data: {
      name,
      verificationUrl,
    },
  });
};

/**
 * Send password reset email
 * @param email User email
 * @param token Reset token
 */
export const sendPasswordResetEmail = async (email: string, token: string): Promise<void> => {
  const resetUrl = `${FRONTEND_URL}/reset-password/${token}`;
  
  await sendEmail({
    to: email,
    subject: 'Reset Your Invoix Password',
    template: 'password-reset',
    data: {
      resetUrl,
    },
  });
};

/**
 * Send invitation email
 * @param email Invitee email
 * @param token Invitation token
 * @param organizationName Organization name
 * @param role User role
 * @param inviterName Inviter name
 * @param inviterEmail Inviter email
 */
export const sendInvitationEmail = async (
  email: string,
  token: string,
  organizationName: string,
  role: string,
  inviterName: string,
  inviterEmail: string
): Promise<void> => {
  const invitationUrl = `${FRONTEND_URL}/invite/${token}`;
  
  await sendEmail({
    to: email,
    subject: `You've Been Invited to Join ${organizationName} on Invoix`,
    template: 'invitation',
    data: {
      organizationName,
      role,
      inviterName,
      inviterEmail,
      invitationUrl,
    },
  });
};
