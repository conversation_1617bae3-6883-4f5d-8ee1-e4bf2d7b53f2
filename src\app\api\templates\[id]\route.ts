import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

/**
 * API route for getting a template by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the template ID from the URL params
    const { id } = params;

    // Get the user session
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    // Get the access token from the session
    const accessToken = session.accessToken;

    // Call the backend API to get the template
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/templates/${id}`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      },
    });

    // If the response is not OK, return an error
    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      return NextResponse.json(
        { error: errorData?.message || 'Failed to fetch template' },
        { status: response.status }
      );
    }

    // Return the template
    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching template:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

/**
 * API route for updating a template
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the template ID from the URL params
    const { id } = params;

    // Get the user session
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    // Get the access token from the session
    const accessToken = session.accessToken;

    // Get the request body
    const body = await request.json();

    // Call the backend API to update the template
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/templates/${id}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    // If the response is not OK, return an error
    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      return NextResponse.json(
        { error: errorData?.message || 'Failed to update template' },
        { status: response.status }
      );
    }

    // Return the updated template
    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error updating template:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

/**
 * API route for deleting a template
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the template ID from the URL params
    const { id } = params;

    // Get the user session
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    // Get the access token from the session
    const accessToken = session.accessToken;

    // Call the backend API to delete the template
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/templates/${id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
      },
    });

    // If the response is not OK, return an error
    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      return NextResponse.json(
        { error: errorData?.message || 'Failed to delete template' },
        { status: response.status }
      );
    }

    // Return the delete result
    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error deleting template:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
