# Invoix API Documentation

This document outlines the API endpoints available in the Invoix platform.

## Base URL

All API endpoints are prefixed with `/api`.

## Authentication

Most endpoints require authentication using a JWT token. Include the token in the Authorization header:

```
Authorization: Bearer <your_jwt_token>
```

## Endpoints

### Authentication

#### Register Tenant

```
POST /api/tenants
```

Create a new tenant with an admin user.

**Request Body:**
```json
{
  "name": "Company Name",
  "businessName": "Legal Business Name",
  "email": "<EMAIL>",
  "phone": "+***********",
  "address": "123 Main St, Kuala Lumpur",
  "logo": "https://example.com/logo.png",
  "adminName": "Admin User",
  "adminEmail": "<EMAIL>",
  "adminPassword": "securePassword123"
}
```

**Response:**
```json
{
  "message": "Tenant created successfully",
  "tenant": {
    "id": "tenant_id",
    "name": "Company Name",
    "businessName": "Legal Business Name",
    "email": "<EMAIL>"
  },
  "admin": {
    "id": "user_id",
    "name": "Admin User",
    "email": "<EMAIL>"
  }
}
```

#### Login

```
POST /api/auth/login
```

Authenticate a user and get a JWT token.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "message": "Login successful",
  "user": {
    "id": "user_id",
    "name": "User Name",
    "email": "<EMAIL>",
    "role": "ADMIN",
    "tenant": {
      "id": "tenant_id",
      "name": "Company Name",
      "businessName": "Legal Business Name"
    }
  },
  "token": "jwt_token_here"
}
```

#### Get User Profile

```
GET /api/auth/profile
```

Get the current user's profile.

**Response:**
```json
{
  "id": "user_id",
  "name": "User Name",
  "email": "<EMAIL>",
  "role": "ADMIN",
  "tenant": {
    "id": "tenant_id",
    "name": "Company Name",
    "businessName": "Legal Business Name"
  }
}
```

### Invoices

#### Create Invoice

```
POST /api/invoices
```

Create a new invoice.

**Request Body:**
```json
{
  "invoiceNumber": "INV-2025-001",
  "dueDate": "2025-11-01T00:00:00Z",
  "customerId": "customer_id",
  "items": [
    {
      "description": "Product A",
      "quantity": 2,
      "unitPrice": 100.00
    },
    {
      "description": "Service B",
      "quantity": 1,
      "unitPrice": 50.00
    }
  ],
  "tax": 15.00,
  "notes": "Payment due within 30 days"
}
```

**Response:**
```json
{
  "message": "Invoice created successfully",
  "invoice": {
    "id": "invoice_id",
    "invoiceNumber": "INV-2025-001",
    "status": "DRAFT",
    "dueDate": "2025-11-01T00:00:00Z",
    "issueDate": "2025-10-01T00:00:00Z",
    "totalAmount": 250.00,
    "tax": 15.00,
    "notes": "Payment due within 30 days",
    "tenantId": "tenant_id",
    "customerId": "customer_id"
  }
}
```

#### Get Invoices

```
GET /api/invoices
```

Get all invoices for the current tenant.

**Query Parameters:**
- `status`: Filter by status (DRAFT, SENT, PAID, OVERDUE, CANCELLED)
- `customerId`: Filter by customer
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)

**Response:**
```json
{
  "invoices": [
    {
      "id": "invoice_id",
      "invoiceNumber": "INV-2025-001",
      "status": "DRAFT",
      "dueDate": "2025-11-01T00:00:00Z",
      "issueDate": "2025-10-01T00:00:00Z",
      "totalAmount": 250.00,
      "customer": {
        "id": "customer_id",
        "name": "Customer Name",
        "email": "<EMAIL>"
      },
      "_count": {
        "items": 2,
        "payments": 0
      }
    }
  ],
  "pagination": {
    "total": 1,
    "page": 1,
    "limit": 10,
    "totalPages": 1
  }
}
```

#### Get Invoice by ID

```
GET /api/invoices/:id
```

Get a specific invoice by ID.

**Response:**
```json
{
  "id": "invoice_id",
  "invoiceNumber": "INV-2025-001",
  "status": "DRAFT",
  "dueDate": "2025-11-01T00:00:00Z",
  "issueDate": "2025-10-01T00:00:00Z",
  "totalAmount": 250.00,
  "tax": 15.00,
  "notes": "Payment due within 30 days",
  "customer": {
    "id": "customer_id",
    "name": "Customer Name",
    "email": "<EMAIL>",
    "phone": "+***********"
  },
  "items": [
    {
      "id": "item_id",
      "description": "Product A",
      "quantity": 2,
      "unitPrice": 100.00,
      "amount": 200.00
    },
    {
      "id": "item_id",
      "description": "Service B",
      "quantity": 1,
      "unitPrice": 50.00,
      "amount": 50.00
    }
  ],
  "payments": []
}
```

### WhatsApp Integration

#### Send Invoice via WhatsApp

```
POST /api/whatsapp/send-invoice
```

Send an invoice to a customer via WhatsApp.

**Request Body:**
```json
{
  "invoiceId": "invoice_id",
  "message": "Here is your invoice. Thank you for your business!"
}
```

**Response:**
```json
{
  "message": "Invoice sent via WhatsApp successfully",
  "messageSid": "message_sid"
}
```

#### Send Payment Reminder

```
POST /api/whatsapp/send-reminder
```

Send a payment reminder for an invoice via WhatsApp.

**Request Body:**
```json
{
  "invoiceId": "invoice_id",
  "message": "This is a friendly reminder that your payment is due soon."
}
```

**Response:**
```json
{
  "message": "Payment reminder sent via WhatsApp successfully",
  "messageSid": "message_sid"
}
```

### LHDN MyInvois Validation

#### Validate Invoice

```
POST /api/lhdn/validate/:invoiceId
```

Validate an invoice with LHDN MyInvois.

**Response:**
```json
{
  "message": "Invoice validated successfully with LHDN MyInvois",
  "validationId": "validation_id"
}
```

#### Get Certificate Status

```
GET /api/lhdn/certificate/status
```

Get the status of the LHDN digital certificate (admin only).

**Response:**
```json
{
  "status": "valid",
  "expiryDate": "2025-12-31T23:59:59Z",
  "issuedTo": "Invoix Platform",
  "issuedBy": "LHDN Certificate Authority"
}
```

#### Upload Certificate

```
POST /api/lhdn/certificate/upload
```

Upload a new LHDN digital certificate (admin only).

**Request Body:**
```json
{
  "certificate": "base64_encoded_certificate_data",
  "password": "certificate_password"
}
```

**Response:**
```json
{
  "message": "Certificate uploaded successfully",
  "status": "valid",
  "expiryDate": "2025-12-31T23:59:59Z"
}
```

### AI Features

#### Get Financial Insights

```
GET /api/ai/insights
```

Get AI-powered financial insights for the tenant.

**Response:**
```json
{
  "predictedCashFlow": {
    "nextMonth": 5000.00,
    "next3Months": 15000.00
  },
  "paymentTrends": {
    "averagePaymentTime": 15,
    "latePaymentPercentage": 10.5
  },
  "topCustomers": [
    {
      "id": "customer_id",
      "name": "Top Customer",
      "totalSpent": 10000.00
    }
  ],
  "recommendations": [
    "Your average payment time is 15 days. This is good, but you could improve cash flow by offering early payment discounts."
  ]
}
```

#### Detect Invoice Fraud

```
GET /api/ai/fraud/:invoiceId
```

Check an invoice for potential fraud.

**Response:**
```json
{
  "invoiceId": "invoice_id",
  "invoiceNumber": "INV-2025-001",
  "isSuspicious": false,
  "riskScore": 15,
  "reasons": []
}
```

## Error Responses

All endpoints return appropriate HTTP status codes:

- `200 OK`: Request succeeded
- `201 Created`: Resource created successfully
- `400 Bad Request`: Invalid request parameters
- `401 Unauthorized`: Authentication required or failed
- `403 Forbidden`: Permission denied
- `404 Not Found`: Resource not found
- `500 Internal Server Error`: Server error

Error response body:

```json
{
  "message": "Error message here",
  "error": "Detailed error information (only in development mode)"
}
```
