import { Request, Response } from 'express';
import { prisma } from '../index';
import { validateInvoice, validateBusinessRegNo } from '../services/lhdn.service';

// Validate invoice with LHDN MyInvois
export const validateInvoiceWithLHDN = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId } = req.user;
    const { invoiceId } = req.params;

    // Get invoice details
    const invoice = await prisma.invoice.findFirst({
      where: {
        id: invoiceId,
        tenantId,
      },
      include: {
        customer: true,
        items: true,
        tenant: true,
      },
    });

    if (!invoice) {
      return res.status(404).json({ message: 'Invoice not found or does not belong to your tenant' });
    }

    // Prepare invoice data for validation
    const invoiceData = {
      invoiceNumber: invoice.invoiceNumber,
      issueDate: invoice.issueDate,
      dueDate: invoice.dueDate,
      totalAmount: invoice.totalAmount,
      tax: invoice.tax,
      businessName: invoice.tenant.businessName,
      businessTaxId: '', // Would come from tenant data in a real implementation
      customerName: invoice.customer.name,
      customerTaxId: '', // Would come from customer data in a real implementation
      items: invoice.items.map((item: any) => ({
        description: item.description,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        amount: item.amount,
      })),
    };

    // Validate invoice with LHDN MyInvois
    const validationResult = await validateInvoice(invoiceData);

    if (validationResult.isValid) {
      // Update invoice with validation status and ID
      await prisma.invoice.update({
        where: { id: invoiceId },
        data: {
          lhdnValidated: true,
          lhdnValidationId: validationResult.validationId,
        },
      });

      return res.status(200).json({
        message: 'Invoice validated successfully with LHDN MyInvois',
        validationId: validationResult.validationId,
      });
    } else {
      return res.status(400).json({
        message: 'Invoice validation failed',
        errors: validationResult.errors,
      });
    }
  } catch (error: any) {
    console.error('Error validating invoice with LHDN MyInvois:', error);
    return res.status(500).json({ message: 'Failed to validate invoice', error: error.message });
  }
};

// Validate business registration number
export const validateBusinessRegNoController = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { regNo } = req.params;

    if (!regNo) {
      return res.status(400).json({ message: 'Business registration number is required' });
    }

    // Validate business registration number with LHDN MyInvois
    const validationResult = await validateBusinessRegNo(regNo);

    return res.status(200).json({
      regNo,
      isValid: validationResult.isValid,
      businessName: validationResult.businessName,
      businessType: validationResult.businessType,
    });
  } catch (error: any) {
    console.error('Error validating business registration number:', error);
    return res.status(500).json({ message: 'Failed to validate business registration number', error: error.message });
  }
};

// Check validation status
export const checkValidationStatus = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId } = req.user;
    const { invoiceId } = req.params;

    // Get invoice validation status
    const invoice = await prisma.invoice.findFirst({
      where: {
        id: invoiceId,
        tenantId,
      },
      select: {
        id: true,
        invoiceNumber: true,
        lhdnValidated: true,
        lhdnValidationId: true,
      },
    });

    if (!invoice) {
      return res.status(404).json({ message: 'Invoice not found or does not belong to your tenant' });
    }

    return res.status(200).json({
      invoiceId: invoice.id,
      invoiceNumber: invoice.invoiceNumber,
      isValidated: invoice.lhdnValidated,
      validationId: invoice.lhdnValidationId,
    });
  } catch (error: any) {
    console.error('Error checking validation status:', error);
    return res.status(500).json({ message: 'Failed to check validation status', error: error.message });
  }
};
