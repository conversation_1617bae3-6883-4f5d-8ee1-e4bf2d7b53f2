import { Request, Response } from 'express';
import { prisma } from '../index';
import fs from 'fs';
import path from 'path';
import { 
  storeCertificate, 
  retrieveCertificate, 
  deleteCertificate, 
  getActiveCertificate, 
  setActiveCertificate 
} from '../services/certificate-storage.service';

/**
 * Get all certificates
 */
export const getCertificates = async (req: Request, res: Response) => {
  try {
    // Check if user is admin
    if (!req.user || req.user.role !== 'ADMIN') {
      return res.status(403).json({ message: 'Unauthorized. Admin access required.' });
    }

    // Get all certificates from database
    const certificates = await prisma.certificate.findMany({
      select: {
        id: true,
        name: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return res.status(200).json({ certificates });
  } catch (error: any) {
    console.error('Error getting certificates:', error);
    return res.status(500).json({ message: 'Failed to get certificates', error: error.message });
  }
};

/**
 * Upload a new certificate
 */
export const uploadCertificate = async (req: Request, res: Response) => {
  try {
    // Check if user is admin
    if (!req.user || req.user.role !== 'ADMIN') {
      return res.status(403).json({ message: 'Unauthorized. Admin access required.' });
    }

    // Check if request has the required fields
    if (!req.file) {
      return res.status(400).json({ message: 'Certificate file is required' });
    }

    const { name, password } = req.body;

    if (!name) {
      return res.status(400).json({ message: 'Certificate name is required' });
    }

    if (!password) {
      return res.status(400).json({ message: 'Certificate password is required' });
    }

    // Read certificate file
    const certificateData = fs.readFileSync(req.file.path);

    // Store certificate securely
    const certificateId = await storeCertificate(name, certificateData, password);

    // Delete temporary file
    fs.unlinkSync(req.file.path);

    // Get certificate details
    const certificate = await prisma.certificate.findUnique({
      where: { id: certificateId },
      select: {
        id: true,
        name: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return res.status(201).json({
      message: 'Certificate uploaded successfully',
      certificate,
    });
  } catch (error: any) {
    console.error('Error uploading certificate:', error);
    
    // Delete temporary file if it exists
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    
    return res.status(500).json({ message: 'Failed to upload certificate', error: error.message });
  }
};

/**
 * Delete a certificate
 */
export const removeCertificate = async (req: Request, res: Response) => {
  try {
    // Check if user is admin
    if (!req.user || req.user.role !== 'ADMIN') {
      return res.status(403).json({ message: 'Unauthorized. Admin access required.' });
    }

    const { id } = req.params;

    // Check if certificate exists
    const certificate = await prisma.certificate.findUnique({
      where: { id },
    });

    if (!certificate) {
      return res.status(404).json({ message: 'Certificate not found' });
    }

    // Check if certificate is active
    if (certificate.isActive) {
      return res.status(400).json({ message: 'Cannot delete active certificate. Set another certificate as active first.' });
    }

    // Delete certificate
    await deleteCertificate(id);

    return res.status(200).json({ message: 'Certificate deleted successfully' });
  } catch (error: any) {
    console.error('Error deleting certificate:', error);
    return res.status(500).json({ message: 'Failed to delete certificate', error: error.message });
  }
};

/**
 * Set a certificate as active
 */
export const activateCertificate = async (req: Request, res: Response) => {
  try {
    // Check if user is admin
    if (!req.user || req.user.role !== 'ADMIN') {
      return res.status(403).json({ message: 'Unauthorized. Admin access required.' });
    }

    const { id } = req.params;

    // Check if certificate exists
    const certificate = await prisma.certificate.findUnique({
      where: { id },
    });

    if (!certificate) {
      return res.status(404).json({ message: 'Certificate not found' });
    }

    // Set certificate as active
    await setActiveCertificate(id);

    return res.status(200).json({ message: 'Certificate activated successfully' });
  } catch (error: any) {
    console.error('Error activating certificate:', error);
    return res.status(500).json({ message: 'Failed to activate certificate', error: error.message });
  }
};

/**
 * Test a certificate
 */
export const testCertificate = async (req: Request, res: Response) => {
  try {
    // Check if user is admin
    if (!req.user || req.user.role !== 'ADMIN') {
      return res.status(403).json({ message: 'Unauthorized. Admin access required.' });
    }

    const { id } = req.params;

    // Check if certificate exists
    const certificate = await prisma.certificate.findUnique({
      where: { id },
    });

    if (!certificate) {
      return res.status(404).json({ message: 'Certificate not found' });
    }

    // Try to retrieve the certificate
    try {
      await retrieveCertificate(id);
      
      // If no error, certificate is valid
      return res.status(200).json({
        success: true,
        message: 'Certificate is valid and can be used for LHDN MyInvois integration',
      });
    } catch (certError: any) {
      return res.status(400).json({
        success: false,
        message: 'Certificate is invalid or corrupted',
        error: certError.message,
      });
    }
  } catch (error: any) {
    console.error('Error testing certificate:', error);
    return res.status(500).json({ 
      success: false,
      message: 'Failed to test certificate', 
      error: error.message 
    });
  }
};
