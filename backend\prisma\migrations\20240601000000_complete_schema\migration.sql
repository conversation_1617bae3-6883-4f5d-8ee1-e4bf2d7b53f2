-- This migration adds all the missing tables and relationships for the complete ERP system

-- Create enum types
CREATE TYPE "UserRole" AS ENUM ('ADMIN', 'MANAGER', 'USER');
CREATE TYPE "InvoiceStatus" AS ENUM ('DRAFT', 'SENT', 'PAID', 'OVERDUE', 'CANCELLED', 'REJECTED');
CREATE TYPE "ValidationStatus" AS ENUM ('PENDING', 'VALID', 'INVALID', 'FAILED');
CREATE TYPE "PaymentMethod" AS ENUM ('BANK_TRANSFER', 'CREDIT_CARD', 'CASH', 'CHEQUE', 'ONLINE');
CREATE TYPE "TransactionType" AS ENUM ('PURCHASE', 'SALE', 'ADJUSTMENT', 'TRANSFER_IN', 'TRANSFER_OUT', 'RETURN');
CREATE TYPE "AttendanceStatus" AS ENUM ('PRESENT', 'ABSENT', 'LATE', 'HALF_DAY', 'ON_LEAVE');
CREATE TYPE "LeaveType" AS ENUM ('ANNUAL', 'SICK', 'MATERNITY', 'PATERNITY', 'UNPAID', 'OTHER');
CREATE TYPE "ApprovalStatus" AS ENUM ('PENDING', 'APPROVED', 'REJECTED');
CREATE TYPE "PaymentStatus" AS ENUM ('PENDING', 'PAID', 'CANCELLED');
CREATE TYPE "PurchaseOrderStatus" AS ENUM ('DRAFT', 'SENT', 'CONFIRMED', 'RECEIVED', 'CANCELLED', 'CLOSED');
CREATE TYPE "AssetCategory" AS ENUM ('LAND', 'BUILDING', 'EQUIPMENT', 'VEHICLE', 'FURNITURE', 'COMPUTER', 'SOFTWARE', 'OTHER');
CREATE TYPE "AssetStatus" AS ENUM ('ACTIVE', 'MAINTENANCE', 'DISPOSED', 'INACTIVE');
CREATE TYPE "ProjectStatus" AS ENUM ('PLANNING', 'ACTIVE', 'ON_HOLD', 'COMPLETED', 'CANCELLED');
CREATE TYPE "TaskStatus" AS ENUM ('TODO', 'IN_PROGRESS', 'REVIEW', 'DONE', 'CANCELLED');
CREATE TYPE "Priority" AS ENUM ('LOW', 'MEDIUM', 'HIGH', 'URGENT');
CREATE TYPE "ExpenseStatus" AS ENUM ('PENDING', 'APPROVED', 'REJECTED', 'REIMBURSED');

-- Update Tenant table
ALTER TABLE "Tenant" ADD COLUMN IF NOT EXISTS "isActive" BOOLEAN NOT NULL DEFAULT true;
ALTER TABLE "Tenant" ADD COLUMN IF NOT EXISTS "businessRegValid" BOOLEAN NOT NULL DEFAULT false;

-- Update User table
ALTER TABLE "User" ADD COLUMN IF NOT EXISTS "refreshToken" TEXT;
ALTER TABLE "User" ADD COLUMN IF NOT EXISTS "lastLogin" TIMESTAMP(3);
ALTER TABLE "User" ALTER COLUMN "role" TYPE "UserRole" USING "role"::"UserRole";

-- Create Customer table if not exists
CREATE TABLE IF NOT EXISTS "Customer" (
  "id" TEXT NOT NULL,
  "name" TEXT NOT NULL,
  "email" TEXT NOT NULL,
  "phone" TEXT,
  "address" TEXT,
  "taxId" TEXT,
  "taxIdValidated" BOOLEAN NOT NULL DEFAULT false,
  "creditLimit" DOUBLE PRECISION,
  "notes" TEXT,
  "tenantId" TEXT NOT NULL,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  CONSTRAINT "Customer_pkey" PRIMARY KEY ("id")
);

-- Create unique constraint on Customer
ALTER TABLE "Customer" ADD CONSTRAINT "Customer_email_tenantId_key" UNIQUE ("email", "tenantId");

-- Create Invoice table if not exists
CREATE TABLE IF NOT EXISTS "Invoice" (
  "id" TEXT NOT NULL,
  "invoiceNumber" TEXT NOT NULL,
  "issueDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "dueDate" TIMESTAMP(3) NOT NULL,
  "status" "InvoiceStatus" NOT NULL DEFAULT 'DRAFT',
  "totalAmount" DOUBLE PRECISION NOT NULL,
  "tax" DOUBLE PRECISION NOT NULL DEFAULT 0,
  "notes" TEXT,
  "tenantId" TEXT NOT NULL,
  "customerId" TEXT NOT NULL,
  "validationId" TEXT,
  "validationStatus" "ValidationStatus",
  "validationDate" TIMESTAMP(3),
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  CONSTRAINT "Invoice_pkey" PRIMARY KEY ("id")
);

-- Create unique constraint on Invoice
ALTER TABLE "Invoice" ADD CONSTRAINT "Invoice_invoiceNumber_tenantId_key" UNIQUE ("invoiceNumber", "tenantId");

-- Create InvoiceItem table if not exists
CREATE TABLE IF NOT EXISTS "InvoiceItem" (
  "id" TEXT NOT NULL,
  "description" TEXT NOT NULL,
  "quantity" DOUBLE PRECISION NOT NULL,
  "unitPrice" DOUBLE PRECISION NOT NULL,
  "amount" DOUBLE PRECISION NOT NULL,
  "invoiceId" TEXT NOT NULL,
  "productId" TEXT,
  "tenantId" TEXT NOT NULL,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  CONSTRAINT "InvoiceItem_pkey" PRIMARY KEY ("id")
);

-- Create Payment table if not exists
CREATE TABLE IF NOT EXISTS "Payment" (
  "id" TEXT NOT NULL,
  "amount" DOUBLE PRECISION NOT NULL,
  "paymentDate" TIMESTAMP(3) NOT NULL,
  "paymentMethod" "PaymentMethod" NOT NULL,
  "referenceNumber" TEXT,
  "notes" TEXT,
  "invoiceId" TEXT NOT NULL,
  "tenantId" TEXT NOT NULL,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  CONSTRAINT "Payment_pkey" PRIMARY KEY ("id")
);

-- Create InvoiceTemplate table if not exists
CREATE TABLE IF NOT EXISTS "InvoiceTemplate" (
  "id" TEXT NOT NULL,
  "name" TEXT NOT NULL,
  "template" TEXT NOT NULL,
  "isDefault" BOOLEAN NOT NULL DEFAULT false,
  "tenantId" TEXT NOT NULL,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  CONSTRAINT "InvoiceTemplate_pkey" PRIMARY KEY ("id")
);

-- Create PaymentSetting table if not exists
CREATE TABLE IF NOT EXISTS "PaymentSetting" (
  "id" TEXT NOT NULL,
  "provider" TEXT NOT NULL,
  "apiKey" TEXT NOT NULL,
  "apiSecret" TEXT NOT NULL,
  "isActive" BOOLEAN NOT NULL DEFAULT true,
  "tenantId" TEXT NOT NULL,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  CONSTRAINT "PaymentSetting_pkey" PRIMARY KEY ("id")
);

-- Create WhatsappSetting table if not exists
CREATE TABLE IF NOT EXISTS "WhatsappSetting" (
  "id" TEXT NOT NULL,
  "provider" TEXT NOT NULL,
  "apiKey" TEXT NOT NULL,
  "phoneNumber" TEXT NOT NULL,
  "isActive" BOOLEAN NOT NULL DEFAULT true,
  "tenantId" TEXT NOT NULL,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  CONSTRAINT "WhatsappSetting_pkey" PRIMARY KEY ("id")
);

-- Create unique constraint on WhatsappSetting
ALTER TABLE "WhatsappSetting" ADD CONSTRAINT "WhatsappSetting_tenantId_key" UNIQUE ("tenantId");

-- Create LHDNConfig table if not exists
CREATE TABLE IF NOT EXISTS "LHDNConfig" (
  "id" TEXT NOT NULL,
  "apiBaseUrl" TEXT NOT NULL DEFAULT 'https://sandbox.myinvois.hasil.gov.my/einvoicing',
  "certificatePath" TEXT,
  "certificatePassword" TEXT,
  "isActive" BOOLEAN NOT NULL DEFAULT false,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  CONSTRAINT "LHDNConfig_pkey" PRIMARY KEY ("id")
);

-- Create LHDNCertificate table if not exists
CREATE TABLE IF NOT EXISTS "LHDNCertificate" (
  "id" TEXT NOT NULL,
  "name" TEXT NOT NULL,
  "filename" TEXT NOT NULL,
  "encryptedData" TEXT NOT NULL,
  "isActive" BOOLEAN NOT NULL DEFAULT false,
  "expiryDate" TIMESTAMP(3) NOT NULL,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  CONSTRAINT "LHDNCertificate_pkey" PRIMARY KEY ("id")
);
