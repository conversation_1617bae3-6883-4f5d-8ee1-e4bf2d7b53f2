'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

export default function ReportsPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the new dashboard page
    router.push('/dashboard/reports/dashboard');
  }, [router]);

  return (
    <div className="flex items-center justify-center h-screen">
      <Card className="w-full max-w-md">
        <CardContent className="p-6">
          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
            <p className="text-lg font-medium">Redirecting to new Reports Dashboard...</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
