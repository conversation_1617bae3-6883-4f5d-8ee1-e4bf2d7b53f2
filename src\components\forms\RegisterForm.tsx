'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import Link from 'next/link';
import BusinessRegistrationField from './BusinessRegistrationField';
import { register as registerTenant, isAuthenticated } from '@/lib/auth';
import { apiClient } from '@/lib/api/client';

export default function RegisterForm() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [step, setStep] = useState(1);

  // Check if user is already authenticated
  useEffect(() => {
    if (isAuthenticated()) {
      router.push('/dashboard');
    }
  }, [router]);
  const [formData, setFormData] = useState({
    // Company details
    name: '',
    businessName: '',
    businessRegNo: '',
    isBusinessRegValid: false,
    email: '',
    phone: '',

    // Admin user details
    adminName: '',
    adminEmail: '',
    adminPassword: '',
    confirmPassword: '',
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleBusinessRegChange = (value: string, isValid: boolean, businessName?: string) => {
    setFormData((prev) => ({
      ...prev,
      businessRegNo: value,
      isBusinessRegValid: isValid,
      // If business name is provided and the current name is empty, auto-fill it
      businessName: businessName && !prev.businessName ? businessName : prev.businessName,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (step === 1) {
      // Validate business registration before proceeding
      if (!formData.businessRegNo) {
        setError('Business registration number is required');
        return;
      }

      if (!formData.isBusinessRegValid) {
        setError('Please enter a valid business registration number');
        return;
      }

      setError(null);
      setStep(2);
      return;
    }

    // Validate passwords match
    if (formData.adminPassword !== formData.confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // First, create the tenant
      const tenantResponse = await apiClient.post('/tenants', {
        name: formData.name,
        businessName: formData.businessName,
        businessRegNo: formData.businessRegNo,
        email: formData.email,
        phone: formData.phone,
      }, { skipAuth: true });

      // Then register the admin user
      await registerTenant(
        formData.adminName,
        formData.adminEmail,
        formData.adminPassword,
        tenantResponse.id
      );

      // Redirect to dashboard (the register function will handle authentication)
      router.push('/dashboard');
    } catch (err: any) {
      console.error('Registration error:', err);
      setError(err.message || 'Failed to register. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl font-bold text-center">Create an Account</CardTitle>
        <CardDescription className="text-center">
          {step === 1
            ? 'Enter your company details to get started'
            : 'Create your admin account'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {error && (
            <div className="bg-red-50 text-red-500 p-3 rounded-md text-sm">
              {error}
            </div>
          )}

          {step === 1 ? (
            // Step 1: Company details
            <>
              <Input
                label="Company Name"
                type="text"
                name="name"
                placeholder="Your Company"
                value={formData.name}
                onChange={handleChange}
                required
              />
              <Input
                label="Legal Business Name"
                type="text"
                name="businessName"
                placeholder="Legal Business Name"
                value={formData.businessName}
                onChange={handleChange}
                required
              />
              <BusinessRegistrationField
                value={formData.businessRegNo}
                onChange={handleBusinessRegChange}
                autoValidate={true}
                required
              />
              <Input
                label="Company Email"
                type="email"
                name="email"
                placeholder="<EMAIL>"
                value={formData.email}
                onChange={handleChange}
                required
              />
              <Input
                label="Phone Number"
                type="tel"
                name="phone"
                placeholder="+***********"
                value={formData.phone}
                onChange={handleChange}
                required
              />
            </>
          ) : (
            // Step 2: Admin user details
            <>
              <Input
                label="Admin Name"
                type="text"
                name="adminName"
                placeholder="Your Name"
                value={formData.adminName}
                onChange={handleChange}
                required
              />
              <Input
                label="Admin Email"
                type="email"
                name="adminEmail"
                placeholder="<EMAIL>"
                value={formData.adminEmail}
                onChange={handleChange}
                required
              />
              <Input
                label="Password"
                type="password"
                name="adminPassword"
                placeholder="••••••••"
                value={formData.adminPassword}
                onChange={handleChange}
                required
              />
              <Input
                label="Confirm Password"
                type="password"
                name="confirmPassword"
                placeholder="••••••••"
                value={formData.confirmPassword}
                onChange={handleChange}
                required
              />
            </>
          )}

          <div className="flex gap-2">
            {step === 2 && (
              <Button
                type="button"
                variant="outline"
                onClick={() => setStep(1)}
                className="flex-1"
              >
                Back
              </Button>
            )}
            <Button
              type="submit"
              className="flex-1"
              disabled={isLoading}
            >
              {step === 1
                ? 'Next'
                : isLoading ? 'Creating Account...' : 'Create Account'}
            </Button>
          </div>
        </form>
      </CardContent>
      <CardFooter className="flex justify-center">
        <div className="text-sm text-center text-gray-500">
          Already have an account?{' '}
          <Link href="/login" className="text-indigo-600 hover:underline">
            Login
          </Link>
        </div>
      </CardFooter>
    </Card>
  );
}
