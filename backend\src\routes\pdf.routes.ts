import express from 'express';
import { downloadInvoicePDF, viewInvoicePDF, generateBatchPDFs } from '../controllers/pdf.controller';
import { authenticate } from '../middleware/auth.middleware';
import { ExpressHandler } from '../types/express';

const router = express.Router();

// All routes are protected
router.get('/invoices/:id/download', authenticate as ExpressHandler, downloadInvoicePDF as ExpressHandler);
router.get('/invoices/:id/view', authenticate as ExpressHandler, viewInvoicePDF as ExpressHandler);
router.post('/invoices/batch', authenticate as ExpressHandler, generateBatchPDFs as ExpressHandler);

export default router;
