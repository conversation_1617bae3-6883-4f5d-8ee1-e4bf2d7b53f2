'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

export default function WhatsAppMessageHistory() {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [messages, setMessages] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState<'all' | 'sent' | 'delivered' | 'read' | 'failed'>('all');

  useEffect(() => {
    // In a real implementation, this would fetch data from your API
    const fetchMessages = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        // Simulate API call with timeout
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock data for demonstration
        const mockMessages = [
          { 
            id: 'msg-001', 
            templateName: 'Invoice Created',
            recipient: '+60123456789',
            recipientName: 'John Doe',
            status: 'delivered',
            sentAt: '2023-06-20T10:30:00Z',
            deliveredAt: '2023-06-20T10:30:05Z',
            readAt: null,
            invoiceId: 'INV-2023-001',
            variables: {
              customer_name: 'John Doe',
              invoice_number: 'INV-2023-001',
              amount: 'RM 1,200.00',
              invoice_url: 'https://invoix.com/inv/abc123'
            }
          },
          { 
            id: 'msg-002', 
            templateName: 'Payment Reminder',
            recipient: '+60198765432',
            recipientName: 'Jane Smith',
            status: 'read',
            sentAt: '2023-06-19T14:15:00Z',
            deliveredAt: '2023-06-19T14:15:10Z',
            readAt: '2023-06-19T15:20:00Z',
            invoiceId: 'INV-2023-002',
            variables: {
              customer_name: 'Jane Smith',
              invoice_number: 'INV-2023-002',
              amount: 'RM 3,500.00',
              due_date: '2023-06-25'
            }
          },
          { 
            id: 'msg-003', 
            templateName: 'Invoice Created',
            recipient: '+60187654321',
            recipientName: 'Acme Corporation',
            status: 'sent',
            sentAt: '2023-06-20T09:45:00Z',
            deliveredAt: null,
            readAt: null,
            invoiceId: 'INV-2023-003',
            variables: {
              customer_name: 'Acme Corporation',
              invoice_number: 'INV-2023-003',
              amount: 'RM 2,800.00',
              invoice_url: 'https://invoix.com/inv/def456'
            }
          },
          { 
            id: 'msg-004', 
            templateName: 'Payment Received',
            recipient: '+60176543210',
            recipientName: 'Wayne Enterprises',
            status: 'failed',
            sentAt: '2023-06-18T16:20:00Z',
            deliveredAt: null,
            readAt: null,
            invoiceId: 'INV-2023-004',
            error: 'Invalid phone number format',
            variables: {
              customer_name: 'Wayne Enterprises',
              amount: 'RM 5,200.00',
              invoice_number: 'INV-2023-004'
            }
          },
          { 
            id: 'msg-005', 
            templateName: 'Payment Reminder',
            recipient: '+60165432109',
            recipientName: 'Stark Industries',
            status: 'read',
            sentAt: '2023-06-17T11:10:00Z',
            deliveredAt: '2023-06-17T11:10:15Z',
            readAt: '2023-06-17T13:45:00Z',
            invoiceId: 'INV-2023-005',
            variables: {
              customer_name: 'Stark Industries',
              invoice_number: 'INV-2023-005',
              amount: 'RM 8,500.00',
              due_date: '2023-06-22'
            }
          }
        ];
        
        setMessages(mockMessages);
      } catch (err: any) {
        console.error('Error fetching WhatsApp messages:', err);
        setError(err.message || 'Failed to load WhatsApp messages');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchMessages();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'delivered':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'read':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const filteredMessages = messages
    .filter(message => {
      // Apply status filter
      if (filter !== 'all' && message.status !== filter) {
        return false;
      }
      
      // Apply search filter
      if (searchTerm) {
        const search = searchTerm.toLowerCase();
        return (
          message.recipientName.toLowerCase().includes(search) ||
          message.recipient.includes(search) ||
          message.templateName.toLowerCase().includes(search) ||
          message.invoiceId.toLowerCase().includes(search)
        );
      }
      
      return true;
    });

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  };

  if (isLoading && messages.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        <span className="ml-2 text-gray-600">Loading WhatsApp messages...</span>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">WhatsApp Message History</h1>
          <p className="mt-1 text-sm text-gray-500">
            View and manage your WhatsApp message history
          </p>
        </div>
        <div className="mt-4 md:mt-0">
          <Button variant="outline">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
            </svg>
            Export History
          </Button>
        </div>
      </div>

      {/* Filters and search */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex-1 relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <Input
            type="text"
            placeholder="Search messages..."
            className="pl-10"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="flex space-x-2">
          <Button 
            variant={filter === 'all' ? 'default' : 'outline'} 
            onClick={() => setFilter('all')}
          >
            All
          </Button>
          <Button 
            variant={filter === 'sent' ? 'default' : 'outline'} 
            onClick={() => setFilter('sent')}
          >
            Sent
          </Button>
          <Button 
            variant={filter === 'delivered' ? 'default' : 'outline'} 
            onClick={() => setFilter('delivered')}
          >
            Delivered
          </Button>
          <Button 
            variant={filter === 'read' ? 'default' : 'outline'} 
            onClick={() => setFilter('read')}
          >
            Read
          </Button>
          <Button 
            variant={filter === 'failed' ? 'default' : 'outline'} 
            onClick={() => setFilter('failed')}
          >
            Failed
          </Button>
        </div>
      </div>

      {/* Messages list */}
      <div className="space-y-4">
        {filteredMessages.map((message) => (
          <Card key={message.id}>
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg">{message.templateName}</CardTitle>
                  <CardDescription>
                    {message.recipientName} • {message.recipient}
                  </CardDescription>
                </div>
                <Badge variant="outline" className={getStatusColor(message.status)}>
                  {message.status.charAt(0).toUpperCase() + message.status.slice(1)}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <div className="font-medium text-gray-500">Sent</div>
                    <div>{formatDate(message.sentAt)}</div>
                  </div>
                  <div>
                    <div className="font-medium text-gray-500">Delivered</div>
                    <div>{formatDate(message.deliveredAt)}</div>
                  </div>
                  <div>
                    <div className="font-medium text-gray-500">Read</div>
                    <div>{formatDate(message.readAt)}</div>
                  </div>
                </div>
                
                <div>
                  <div className="font-medium text-gray-500 mb-2">Variables</div>
                  <div className="flex flex-wrap gap-2">
                    {Object.entries(message.variables).map(([key, value]) => (
                      <Badge key={key} variant="outline">
                        {key}: {value as string}
                      </Badge>
                    ))}
                  </div>
                </div>
                
                {message.error && (
                  <Alert variant="destructive">
                    <AlertDescription>{message.error}</AlertDescription>
                  </Alert>
                )}
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <div className="text-sm text-gray-500">
                Invoice: <span className="font-medium">{message.invoiceId}</span>
              </div>
              <div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                      </svg>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                    <DropdownMenuItem>View Details</DropdownMenuItem>
                    <DropdownMenuItem>View Invoice</DropdownMenuItem>
                    <DropdownMenuSeparator />
                    {message.status === 'failed' && (
                      <DropdownMenuItem>Retry Sending</DropdownMenuItem>
                    )}
                    <DropdownMenuItem className="text-red-600">Delete</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </CardFooter>
          </Card>
        ))}
        
        {filteredMessages.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No messages found. Try adjusting your filters or search term.
          </div>
        )}
      </div>
    </div>
  );
}
