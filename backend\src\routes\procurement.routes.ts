import express from 'express';
import { authenticateJWT } from '../middleware/auth';
import {
  createSupplier,
  getSuppliers,
  getSupplier,
  updateSupplier,
  deleteSupplier,
  createPurchaseOrder,
  getPurchaseOrders,
  getPurchaseOrder,
  updatePurchaseOrder,
  deletePurchaseOrder,
  createGoodsReceipt,
  getGoodsReceipts,
  getGoodsReceipt
} from '../controllers/procurement.controller';

const router = express.Router();

// Apply authentication middleware to all procurement routes
router.use(authenticateJWT);

// Supplier routes
router.post('/suppliers', createSupplier);
router.get('/suppliers', getSuppliers);
router.get('/suppliers/:id', getSupplier);
router.put('/suppliers/:id', updateSupplier);
router.delete('/suppliers/:id', deleteSupplier);

// Purchase Order routes
router.post('/purchase-orders', createPurchaseOrder);
router.get('/purchase-orders', getPurchaseOrders);
router.get('/purchase-orders/:id', getPurchaseOrder);
router.put('/purchase-orders/:id', updatePurchaseOrder);
router.delete('/purchase-orders/:id', deletePurchaseOrder);

// Goods Receipt routes
router.post('/goods-receipts', createGoodsReceipt);
router.get('/goods-receipts', getGoodsReceipts);
router.get('/goods-receipts/:id', getGoodsReceipt);

export default router;
