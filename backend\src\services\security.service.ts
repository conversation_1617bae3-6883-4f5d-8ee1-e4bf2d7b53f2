import { prisma } from '../index';
import { Request } from 'express';
import { logger } from '../utils/logger';
import { sendEmail } from '../utils/email';
import { User } from '@prisma/client';
import geoip from 'geoip-lite';
import UAParser from 'ua-parser-js';

/**
 * Security Service
 * Handles security-related functionality like login attempt tracking,
 * suspicious activity detection, and security notifications
 */
export class SecurityService {
  /**
   * Track login attempt
   * @param email Email address used in login attempt
   * @param success Whether the login attempt was successful
   * @param req Express request object
   */
  async trackLoginAttempt(email: string, success: boolean, req: Request): Promise<void> {
    try {
      const ip = this.getClientIp(req);
      const userAgent = req.headers['user-agent'] || '';
      const parser = new UAParser(userAgent);
      const browser = parser.getBrowser();
      const os = parser.getOS();
      const device = parser.getDevice();
      
      // Get location from IP
      const geo = geoip.lookup(ip);
      const location = geo ? `${geo.city}, ${geo.country}` : 'Unknown';
      
      // Create login attempt record
      await prisma.loginAttempt.create({
        data: {
          email,
          success,
          ipAddress: ip,
          userAgent,
          browser: `${browser.name} ${browser.version}`,
          operatingSystem: `${os.name} ${os.version}`,
          device: device.type || 'desktop',
          location,
        },
      });
      
      // If login failed, check for suspicious activity
      if (!success) {
        await this.checkForSuspiciousActivity(email, ip);
      } else {
        // If login successful, check if it's from a new location/device
        const user = await prisma.user.findUnique({
          where: { email },
        });
        
        if (user) {
          await this.checkNewLocationOrDevice(user, ip, userAgent, location);
        }
      }
    } catch (error) {
      logger.error('Error tracking login attempt:', error);
    }
  }
  
  /**
   * Check for suspicious activity (multiple failed login attempts)
   * @param email Email address
   * @param ip IP address
   */
  private async checkForSuspiciousActivity(email: string, ip: string): Promise<void> {
    try {
      // Count failed login attempts in the last hour
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
      
      const failedAttempts = await prisma.loginAttempt.count({
        where: {
          email,
          success: false,
          createdAt: {
            gte: oneHourAgo,
          },
        },
      });
      
      // If more than 5 failed attempts in the last hour, send notification
      if (failedAttempts >= 5) {
        const user = await prisma.user.findUnique({
          where: { email },
        });
        
        if (user) {
          await this.sendSecurityNotification(
            user,
            'Multiple Failed Login Attempts',
            'We detected multiple failed login attempts on your account.',
            'FAILED_LOGIN',
            ip
          );
          
          // Optionally lock the account temporarily
          // await this.lockAccount(user.id, 30); // Lock for 30 minutes
        }
      }
    } catch (error) {
      logger.error('Error checking for suspicious activity:', error);
    }
  }
  
  /**
   * Check if login is from a new location or device
   * @param user User object
   * @param ip IP address
   * @param userAgent User agent string
   * @param location Location string
   */
  private async checkNewLocationOrDevice(
    user: User,
    ip: string,
    userAgent: string,
    location: string
  ): Promise<void> {
    try {
      // Check if user has logged in from this location before
      const previousLoginFromLocation = await prisma.loginAttempt.findFirst({
        where: {
          email: user.email,
          success: true,
          location,
          createdAt: {
            lt: new Date(), // Before current login
          },
        },
      });
      
      // If this is a new location, send notification
      if (!previousLoginFromLocation) {
        await this.sendSecurityNotification(
          user,
          'New Login Location Detected',
          `We detected a login from a new location: ${location}`,
          'NEW_LOCATION',
          ip
        );
      }
      
      // Check if user has used this device before
      const parser = new UAParser(userAgent);
      const browser = parser.getBrowser();
      const os = parser.getOS();
      const deviceInfo = `${browser.name} on ${os.name}`;
      
      const previousLoginFromDevice = await prisma.loginAttempt.findFirst({
        where: {
          email: user.email,
          success: true,
          browser: { contains: browser.name || '' },
          operatingSystem: { contains: os.name || '' },
          createdAt: {
            lt: new Date(), // Before current login
          },
        },
      });
      
      // If this is a new device, send notification
      if (!previousLoginFromDevice) {
        await this.sendSecurityNotification(
          user,
          'New Device Login Detected',
          `We detected a login from a new device: ${deviceInfo}`,
          'NEW_DEVICE',
          ip
        );
      }
    } catch (error) {
      logger.error('Error checking for new location or device:', error);
    }
  }
  
  /**
   * Send security notification email
   * @param user User object
   * @param title Notification title
   * @param message Notification message
   * @param eventType Event type
   * @param ip IP address
   */
  private async sendSecurityNotification(
    user: User,
    title: string,
    message: string,
    eventType: string,
    ip: string
  ): Promise<void> {
    try {
      const geo = geoip.lookup(ip);
      const location = geo ? `${geo.city}, ${geo.country}` : 'Unknown';
      
      // Create security notification record
      await prisma.securityNotification.create({
        data: {
          userId: user.id,
          title,
          message,
          eventType,
          ipAddress: ip,
          location,
        },
      });
      
      // Send email notification
      await sendEmail({
        to: user.email,
        subject: `Security Alert - ${title}`,
        template: 'security-notification',
        data: {
          name: user.name,
          alertTitle: title,
          alertMessage: message,
          eventType,
          eventTime: new Date().toLocaleString(),
          ipAddress: ip,
          location,
          device: 'Unknown', // This would be populated with actual device info
          wasYou: false,
          securitySettingsUrl: `${process.env.FRONTEND_URL}/dashboard/settings/security`,
          year: new Date().getFullYear(),
        },
      });
    } catch (error) {
      logger.error('Error sending security notification:', error);
    }
  }
  
  /**
   * Get client IP address from request
   * @param req Express request object
   * @returns IP address
   */
  private getClientIp(req: Request): string {
    const forwardedFor = req.headers['x-forwarded-for'];
    
    if (forwardedFor) {
      const ips = Array.isArray(forwardedFor)
        ? forwardedFor[0]
        : forwardedFor.split(',')[0];
      return ips.trim();
    }
    
    return req.ip || '127.0.0.1';
  }
  
  /**
   * Lock user account temporarily
   * @param userId User ID
   * @param minutes Minutes to lock the account for
   */
  async lockAccount(userId: string, minutes: number): Promise<void> {
    try {
      const lockUntil = new Date(Date.now() + minutes * 60 * 1000);
      
      await prisma.user.update({
        where: { id: userId },
        data: {
          lockedUntil: lockUntil,
        },
      });
      
      logger.info(`Account ${userId} locked until ${lockUntil}`);
    } catch (error) {
      logger.error('Error locking account:', error);
    }
  }
  
  /**
   * Check if account is locked
   * @param userId User ID
   * @returns Whether the account is locked
   */
  async isAccountLocked(userId: string): Promise<boolean> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { lockedUntil: true },
      });
      
      if (user?.lockedUntil && user.lockedUntil > new Date()) {
        return true;
      }
      
      return false;
    } catch (error) {
      logger.error('Error checking if account is locked:', error);
      return false;
    }
  }
}

export const securityService = new SecurityService();
export default securityService;
