/**
 * API client for making authenticated requests to the backend
 */
import { getAccessToken, isTokenExpired, refreshAccessToken, logout } from '../auth';

interface ApiClientOptions {
  baseUrl?: string;
  headers?: Record<string, string>;
}

interface RequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  skipAuth?: boolean; // Skip authentication for public endpoints
}

class ApiClient {
  private baseUrl: string;
  private defaultHeaders: Record<string, string>;

  constructor(options: ApiClientOptions = {}) {
    this.baseUrl = options.baseUrl || process.env.NEXT_PUBLIC_API_URL || '/api';
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...options.headers,
    };
  }

  /**
   * Add authentication header if token exists
   */
  private async getAuthHeaders(): Promise<Record<string, string>> {
    // Get access token
    let accessToken = getAccessToken();

    // Check if token is expired and refresh if needed
    if (accessToken && isTokenExpired(accessToken)) {
      try {
        accessToken = await refreshAccessToken();
      } catch (error) {
        // If refresh fails, redirect to login
        logout();
        if (typeof window !== 'undefined') {
          window.location.href = '/login?expired=true';
        }
        throw new Error('Session expired. Please login again.');
      }
    }

    // Return authorization header if token exists
    return accessToken ? { 'Authorization': `Bearer ${accessToken}` } : {};
  }

  /**
   * Make a request to the API
   * @param endpoint The API endpoint
   * @param options Request options
   * @returns Promise with the response data
   */
  async request<T = any>(endpoint: string, options: RequestOptions = {}): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    const method = options.method || 'GET';

    // Get authentication headers if not skipping auth
    const authHeaders = options.skipAuth ? {} : await this.getAuthHeaders();

    const headers = {
      ...this.defaultHeaders,
      ...authHeaders,
      ...options.headers,
    };

    const config: RequestInit = {
      method,
      headers,
      credentials: 'include', // Include cookies for authentication
    };

    if (options.body && method !== 'GET') {
      config.body = JSON.stringify(options.body);
    }

    try {
      const response = await fetch(url, config);

      // Handle 401 errors (token expired or invalid)
      if (response.status === 401 && !options.skipAuth) {
        try {
          const responseData = await response.json();

          // If token expired, try to refresh and retry the request
          if (responseData.code === 'TOKEN_EXPIRED') {
            const accessToken = await refreshAccessToken();

            // Retry the request with new token
            const retryConfig = {
              ...config,
              headers: {
                ...headers,
                'Authorization': `Bearer ${accessToken}`,
              },
            };

            const retryResponse = await fetch(url, retryConfig);
            return this.handleResponse<T>(retryResponse);
          }
        } catch (error) {
          // If refresh fails or it's not a token expiration issue, proceed with normal error handling
          logout();
          if (typeof window !== 'undefined') {
            window.location.href = '/login?expired=true';
          }
          throw new Error('Authentication failed. Please login again.');
        }
      }

      return this.handleResponse<T>(response);
    } catch (error: any) {
      console.error(`API request failed: ${url}`, error);
      throw error;
    }
  }

  /**
   * Handle API response
   */
  private async handleResponse<T>(response: Response): Promise<T> {
    // Handle non-JSON responses
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `API error: ${response.status}`);
      }

      return data;
    } else {
      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }

      return await response.text() as unknown as T;
    }
  }

  /**
   * Make a GET request
   * @param endpoint The API endpoint
   * @param options Request options
   * @returns Promise with the response data
   */
  async get<T = any>(endpoint: string, options: Omit<RequestOptions, 'method' | 'body'> = {}): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'GET' });
  }

  /**
   * Make a POST request
   * @param endpoint The API endpoint
   * @param body Request body
   * @param options Request options
   * @returns Promise with the response data
   */
  async post<T = any>(
    endpoint: string,
    body: any,
    options: Omit<RequestOptions, 'method' | 'body'> = {}
  ): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'POST', body });
  }

  /**
   * Make a PUT request
   * @param endpoint The API endpoint
   * @param body Request body
   * @param options Request options
   * @returns Promise with the response data
   */
  async put<T = any>(
    endpoint: string,
    body: any,
    options: Omit<RequestOptions, 'method' | 'body'> = {}
  ): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'PUT', body });
  }

  /**
   * Make a DELETE request
   * @param endpoint The API endpoint
   * @param options Request options
   * @returns Promise with the response data
   */
  async delete<T = any>(
    endpoint: string,
    options: Omit<RequestOptions, 'method'> = {}
  ): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'DELETE' });
  }

  /**
   * Make a PATCH request
   * @param endpoint The API endpoint
   * @param body Request body
   * @param options Request options
   * @returns Promise with the response data
   */
  async patch<T = any>(
    endpoint: string,
    body: any,
    options: Omit<RequestOptions, 'method' | 'body'> = {}
  ): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'PATCH', body });
  }
}

// Create a singleton instance of the API client
export const apiClient = new ApiClient();

// Export the class for custom instances
export default ApiClient;
