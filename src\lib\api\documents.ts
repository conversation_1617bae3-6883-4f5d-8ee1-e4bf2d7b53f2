import { apiClient } from './client';

/**
 * Search documents in LHDN MyInvois
 * @param params Search parameters
 */
export const searchDocuments = async (params: {
  startDate?: string;
  endDate?: string;
  status?: string;
  documentType?: string;
  page?: number;
  limit?: number;
}) => {
  try {
    // Build query string for the API client
    const queryParams = new URLSearchParams();
    if (params.startDate) queryParams.append('startDate', params.startDate);
    if (params.endDate) queryParams.append('endDate', params.endDate);
    if (params.status) queryParams.append('status', params.status);
    if (params.documentType) queryParams.append('documentType', params.documentType);
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());

    // For development, return mock data
    if (process.env.NODE_ENV === 'development') {
      // Simulate API response with mock data
      return {
        documents: [
          {
            id: '1',
            documentNumber: 'INV-2025-001',
            documentType: 'INVOICE',
            issueDate: '2025-01-15',
            status: 'ACTIVE',
            totalAmount: 1250.00,
            customerName: 'Acme Corporation',
          },
          {
            id: '2',
            documentNumber: 'INV-2025-002',
            documentType: 'INVOICE',
            issueDate: '2025-01-20',
            status: 'ACTIVE',
            totalAmount: 750.50,
            customerName: 'TechSolutions Inc.',
          },
        ],
        pagination: {
          total: 2,
          page: 1,
          limit: 10,
          totalPages: 1,
        },
      };
    }

    // In production, use the API client
    const response = await apiClient.get(`/documents/search?${queryParams.toString()}`);
    return response;
  } catch (error: any) {
    console.error('Error searching documents:', error);
    throw new Error(error.message || 'Failed to search documents');
  }
};

/**
 * Cancel a document in LHDN MyInvois
 * @param documentId Document ID
 * @param reason Cancellation reason
 */
export const cancelDocument = async (documentId: string, reason: string) => {
  try {
    // For development, return mock data
    if (process.env.NODE_ENV === 'development') {
      // Simulate API response with mock data
      await new Promise(resolve => setTimeout(resolve, 500)); // Simulate network delay
      return {
        success: true,
        message: 'Document cancelled successfully',
        documentId,
      };
    }

    // In production, use the API client
    const response = await apiClient.post(`/documents/${documentId}/cancel`, { reason });
    return response;
  } catch (error: any) {
    console.error('Error cancelling document:', error);
    throw new Error(error.message || 'Failed to cancel document');
  }
};

/**
 * Reject a document in LHDN MyInvois
 * @param documentId Document ID
 * @param reason Rejection reason
 */
export const rejectDocument = async (documentId: string, reason: string) => {
  try {
    // For development, return mock data
    if (process.env.NODE_ENV === 'development') {
      // Simulate API response with mock data
      await new Promise(resolve => setTimeout(resolve, 500)); // Simulate network delay
      return {
        success: true,
        message: 'Document rejected successfully',
        documentId,
      };
    }

    // In production, use the API client
    const response = await apiClient.post(`/documents/${documentId}/reject`, { reason });
    return response;
  } catch (error: any) {
    console.error('Error rejecting document:', error);
    throw new Error(error.message || 'Failed to reject document');
  }
};
