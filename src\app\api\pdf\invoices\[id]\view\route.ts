import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

/**
 * API route for viewing an invoice PDF in the browser
 * This route proxies the request to the backend PDF service
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the invoice ID from the URL params
    const { id } = params;

    // Get the user session
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    // Get the access token from the session
    const accessToken = session.accessToken;

    // Call the backend API to generate and view the PDF
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/pdf/invoices/${id}/view`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      },
    });

    // If the response is not OK, return an error
    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      return NextResponse.json(
        { error: errorData?.message || 'Failed to generate PDF' },
        { status: response.status }
      );
    }

    // Get the PDF data as a buffer
    const pdfBuffer = await response.arrayBuffer();

    // Get the content type from the response headers
    const contentType = response.headers.get('content-type') || 'application/pdf';

    // Return the PDF data with the appropriate headers for inline viewing
    return new NextResponse(pdfBuffer, {
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': 'inline',
      },
    });
  } catch (error) {
    console.error('Error viewing PDF:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
