'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';

export default function PaymentPage() {
  const [activeTab, setActiveTab] = useState('transactions');
  const [searchTerm, setSearchTerm] = useState('');

  // Mock payment data
  const transactions = [
    {
      id: 'TRX-2023-001',
      invoiceId: 'INV-2023-042',
      customer: 'Acme Corporation',
      amount: 1200,
      status: 'completed',
      date: '2023-06-15',
      method: 'credit_card',
      reference: '4242XXXX1234'
    },
    {
      id: 'TRX-2023-002',
      invoiceId: 'INV-2023-039',
      customer: 'Wayne Enterprises',
      amount: 3500,
      status: 'completed',
      date: '2023-06-12',
      method: 'bank_transfer',
      reference: 'MAYBANK123456'
    },
    {
      id: 'TRX-2023-003',
      invoiceId: 'INV-2023-035',
      customer: 'Stark Industries',
      amount: 2800,
      status: 'pending',
      date: '2023-06-10',
      method: 'bank_transfer',
      reference: 'CIMB789012'
    },
    {
      id: 'TRX-2023-004',
      invoiceId: 'INV-2023-031',
      customer: 'Daily Planet',
      amount: 950,
      status: 'failed',
      date: '2023-06-08',
      method: 'credit_card',
      reference: '5555XXXX5678',
      failureReason: 'Card declined'
    },
  ];

  const paymentLinks = [
    {
      id: 'LINK-2023-001',
      invoiceId: 'INV-2023-045',
      customer: 'Acme Corporation',
      amount: 2500,
      status: 'active',
      created: '2023-06-18',
      expires: '2023-07-18',
      url: 'https://pay.invoix.com/link/abc123'
    },
    {
      id: 'LINK-2023-002',
      invoiceId: 'INV-2023-044',
      customer: 'Wayne Enterprises',
      amount: 1800,
      status: 'active',
      created: '2023-06-17',
      expires: '2023-07-17',
      url: 'https://pay.invoix.com/link/def456'
    },
    {
      id: 'LINK-2023-003',
      invoiceId: 'INV-2023-040',
      customer: 'Stark Industries',
      amount: 3200,
      status: 'expired',
      created: '2023-05-20',
      expires: '2023-06-20',
      url: 'https://pay.invoix.com/link/ghi789'
    }
  ];

  const gateways = [
    {
      id: 'stripe',
      name: 'Stripe',
      status: 'active',
      lastSync: '2023-06-20T08:30:00Z',
      config: {
        apiKey: '•••••••••••••••••••••••••••',
        webhookSecret: '•••••••••••••••••••••••••••',
        currency: 'MYR'
      }
    },
    {
      id: 'razorpay',
      name: 'Razorpay',
      status: 'inactive',
      lastSync: null,
      config: {
        apiKey: '',
        apiSecret: '',
        currency: 'MYR'
      }
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'active':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'expired':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getMethodIcon = (method: string) => {
    switch (method) {
      case 'credit_card':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
          </svg>
        );
      case 'bank_transfer':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z" />
          </svg>
        );
      default:
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between bg-white p-6 rounded-lg shadow-sm mb-6">
        <div>
          <h1 className="text-2xl font-bold text-text-primary flex items-center">
            <svg className="w-6 h-6 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            Payments
          </h1>
          <p className="mt-1 text-sm text-text-secondary">
            Manage your payment transactions and settings
          </p>
        </div>
        <div className="mt-4 md:mt-0 flex space-x-3">
          <Button variant="outline" size="sm">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
            </svg>
            Export
          </Button>
          <Button size="sm" className="bg-indigo-600 hover:bg-indigo-700">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
            </svg>
            Create Payment Link
          </Button>
        </div>
      </div>

      {/* Payment Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="border-none shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-text-secondary">Total Received</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">RM 45,000</div>
          </CardContent>
        </Card>

        <Card className="border-none shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-text-secondary">Pending Amount</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">RM 12,000</div>
          </CardContent>
        </Card>

        <Card className="border-none shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-text-secondary">Failed Amount</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">RM 3,000</div>
          </CardContent>
        </Card>

        <Card className="border-none shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-text-secondary">Success Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">92%</div>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <div className="flex flex-col md:flex-row gap-4 my-6">
        <div className="flex-1 relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg className="h-5 w-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <Input
            type="text"
            placeholder={`Search ${activeTab}...`}
            className="pl-10 border-gray-200 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 rounded-md shadow-sm"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      <Tabs defaultValue="transactions" onValueChange={setActiveTab}>
        <TabsList className="mb-6 bg-white p-1 rounded-lg border">
          <TabsTrigger value="transactions" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
            </svg>
            Transactions
          </TabsTrigger>
          <TabsTrigger value="payment-links" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
            </svg>
            Payment Links
          </TabsTrigger>
          <TabsTrigger value="gateways" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            Payment Gateways
          </TabsTrigger>
        </TabsList>

        <TabsContent value="transactions" className="space-y-4">
          <Card className="border-none shadow-md">
            <CardHeader className="pb-2 border-b">
              <CardTitle className="text-lg font-bold text-text-primary">Recent Transactions</CardTitle>
              <CardDescription className="text-text-secondary">
                View and manage your payment transactions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {transactions.map((transaction) => (
                  <div key={transaction.id} className="p-4 border rounded-md">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-medium">{transaction.customer}</h3>
                        <p className="text-sm text-text-secondary">{transaction.invoiceId}</p>
                      </div>
                      <Badge variant="outline" className={getStatusColor(transaction.status)}>
                        {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                      </Badge>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4 text-sm">
                      <div>
                        <div className="font-medium text-text-secondary">Amount</div>
                        <div className="font-bold">RM {transaction.amount.toLocaleString()}</div>
                      </div>
                      <div>
                        <div className="font-medium text-text-secondary">Date</div>
                        <div>{transaction.date}</div>
                      </div>
                      <div>
                        <div className="font-medium text-text-secondary">Method</div>
                        <div className="flex items-center">
                          <span className="mr-1">{getMethodIcon(transaction.method)}</span>
                          <span>{transaction.method.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</span>
                        </div>
                        <div className="text-xs text-text-secondary">{transaction.reference}</div>
                      </div>
                    </div>
                    {transaction.failureReason && (
                      <Alert variant="destructive" className="mt-4">
                        <AlertDescription>{transaction.failureReason}</AlertDescription>
                      </Alert>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full">
                View All Transactions
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="payment-links" className="space-y-4">
          <Card className="border-none shadow-md">
            <CardHeader className="pb-2 border-b">
              <CardTitle className="text-lg font-bold text-text-primary">Payment Links</CardTitle>
              <CardDescription className="text-text-secondary">
                Manage your payment links
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {paymentLinks.map((link) => (
                  <div key={link.id} className="p-4 border rounded-md">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-medium">{link.customer}</h3>
                        <p className="text-sm text-text-secondary">{link.invoiceId}</p>
                      </div>
                      <Badge variant="outline" className={getStatusColor(link.status)}>
                        {link.status.charAt(0).toUpperCase() + link.status.slice(1)}
                      </Badge>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4 text-sm">
                      <div>
                        <div className="font-medium text-text-secondary">Amount</div>
                        <div className="font-bold">RM {link.amount.toLocaleString()}</div>
                      </div>
                      <div>
                        <div className="font-medium text-text-secondary">Created</div>
                        <div>{link.created}</div>
                      </div>
                      <div>
                        <div className="font-medium text-text-secondary">Expires</div>
                        <div>{link.expires}</div>
                      </div>
                    </div>
                    <div className="mt-4">
                      <div className="font-medium text-text-secondary mb-2">Payment URL</div>
                      <div className="flex">
                        <Input value={link.url} readOnly className="flex-1" />
                        <Button variant="outline" size="sm" className="ml-2">
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                          </svg>
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter>
              <Button className="w-full">
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Create New Payment Link
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="gateways" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {gateways.map((gateway) => (
              <Card key={gateway.id} className="border-none shadow-md">
                <CardHeader className="pb-2 border-b">
                  <CardTitle className="text-lg font-bold text-text-primary">{gateway.name}</CardTitle>
                  <CardDescription className="text-text-secondary">
                    Payment Gateway
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <Badge variant={gateway.status === 'active' ? 'default' : 'secondary'}>
                        {gateway.status === 'active' ? 'Active' : 'Inactive'}
                      </Badge>
                      {gateway.lastSync && (
                        <div className="text-sm text-text-secondary">
                          Last synced: {new Date(gateway.lastSync).toLocaleString()}
                        </div>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor={`${gateway.id}-apiKey`}>API Key</Label>
                      <Input
                        id={`${gateway.id}-apiKey`}
                        value={gateway.config.apiKey}
                        readOnly={gateway.status === 'active'}
                      />
                    </div>

                    {gateway.config.apiSecret && (
                      <div className="space-y-2">
                        <Label htmlFor={`${gateway.id}-apiSecret`}>API Secret</Label>
                        <Input
                          id={`${gateway.id}-apiSecret`}
                          type="password"
                          value={gateway.config.apiSecret}
                          readOnly={gateway.status === 'active'}
                        />
                      </div>
                    )}

                    {gateway.config.webhookSecret && (
                      <div className="space-y-2">
                        <Label htmlFor={`${gateway.id}-webhookSecret`}>Webhook Secret</Label>
                        <Input
                          id={`${gateway.id}-webhookSecret`}
                          type="password"
                          value={gateway.config.webhookSecret}
                          readOnly={gateway.status === 'active'}
                        />
                      </div>
                    )}

                    <div className="space-y-2">
                      <Label htmlFor={`${gateway.id}-currency`}>Currency</Label>
                      <Input
                        id={`${gateway.id}-currency`}
                        value={gateway.config.currency}
                        readOnly={gateway.status === 'active'}
                      />
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-end space-x-2">
                  {gateway.status === 'active' ? (
                    <>
                      <Button variant="outline" size="sm">Configure</Button>
                      <Button variant="destructive" size="sm">Deactivate</Button>
                    </>
                  ) : (
                    <Button size="sm">Activate</Button>
                  )}
                </CardFooter>
              </Card>
            ))}

            <Card className="border-dashed border-2 border-gray-300 bg-gray-50 shadow-sm hover:shadow-md transition-shadow">
              <CardHeader className="pb-2 border-b">
                <CardTitle className="text-lg font-bold text-text-primary">Add New Gateway</CardTitle>
                <CardDescription className="text-text-secondary">
                  Connect a new payment gateway
                </CardDescription>
              </CardHeader>
              <CardContent className="flex justify-center items-center py-6">
                <Button variant="outline" size="sm">
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Add Payment Gateway
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
