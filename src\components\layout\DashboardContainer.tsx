'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { Card, CardContent } from '@/components/ui/card';

interface DashboardContainerProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'elevated' | 'outline' | 'glass' | 'gradient';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  maxWidth?: 'none' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  withBackground?: boolean;
}

export function DashboardContainer({
  children,
  className,
  variant = 'default',
  padding = 'md',
  maxWidth = 'full',
  withBackground = false,
}: DashboardContainerProps) {
  const paddingClasses = {
    none: 'p-0',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
  };

  const maxWidthClasses = {
    none: '',
    sm: 'max-w-screen-sm',
    md: 'max-w-screen-md',
    lg: 'max-w-screen-lg',
    xl: 'max-w-screen-xl',
    '2xl': 'max-w-screen-2xl',
    full: 'max-w-full',
  };

  return (
    <div
      className={cn(
        'mx-auto',
        maxWidthClasses[maxWidth],
        withBackground && 'bg-neutral-50 min-h-screen',
        className
      )}
    >
      <Card 
        variant={variant} 
        className={cn(
          'border-0 shadow-none rounded-none h-full',
          variant === 'default' && 'bg-transparent',
          className
        )}
      >
        <CardContent className={cn(paddingClasses[padding], 'h-full')}>
          {children}
        </CardContent>
      </Card>
    </div>
  );
}

interface DashboardGridProps {
  children: React.ReactNode;
  className?: string;
  columns?: 1 | 2 | 3 | 4 | 6 | 12;
  gap?: 'none' | 'sm' | 'md' | 'lg';
}

export function DashboardGrid({
  children,
  className,
  columns = 3,
  gap = 'md',
}: DashboardGridProps) {
  const columnsClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4',
    6: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6',
    12: 'grid-cols-1 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-12',
  };

  const gapClasses = {
    none: 'gap-0',
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6',
  };

  return (
    <div
      className={cn(
        'grid',
        columnsClasses[columns],
        gapClasses[gap],
        className
      )}
    >
      {children}
    </div>
  );
}

interface DashboardSectionProps {
  children: React.ReactNode;
  className?: string;
  title?: string;
  description?: string;
  rightContent?: React.ReactNode;
  variant?: 'default' | 'elevated' | 'outline' | 'glass' | 'gradient';
  padding?: 'none' | 'sm' | 'md' | 'lg';
}

export function DashboardSection({
  children,
  className,
  title,
  description,
  rightContent,
  variant = 'default',
  padding = 'md',
}: DashboardSectionProps) {
  const paddingClasses = {
    none: 'p-0',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
  };

  return (
    <Card variant={variant} className={cn('w-full', className)}>
      {(title || description || rightContent) && (
        <div className={cn('flex justify-between items-center border-b p-6')}>
          <div>
            {title && <h2 className="text-xl font-semibold">{title}</h2>}
            {description && <p className="text-sm text-neutral-500 mt-1">{description}</p>}
          </div>
          {rightContent && <div>{rightContent}</div>}
        </div>
      )}
      <div className={cn(paddingClasses[padding])}>{children}</div>
    </Card>
  );
}

export { Card as DashboardCard } from '@/components/ui/card';
