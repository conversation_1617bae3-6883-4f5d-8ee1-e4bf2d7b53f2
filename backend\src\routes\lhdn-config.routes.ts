import { Router } from 'express';
import { getLHDNConfig, updateLHDNConfig, testLHDNConfig } from '../controllers/lhdn-config.controller';
import { authenticate } from '../middleware/auth.middleware';
import { ExpressHandler } from '../types/express';

const router = Router();

// All routes are protected and require admin access
router.get('/', authenticate as ExpressHandler, getLHDNConfig as ExpressHandler);
router.put('/', authenticate as ExpressHandler, updateLHDNConfig as ExpressHandler);
router.post('/test', authenticate as ExpressHandler, testLHDNConfig as ExpressHandler);

export default router;
