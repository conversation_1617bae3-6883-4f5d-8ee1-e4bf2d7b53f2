'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Spinner } from '@/components/ui/spinner';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import InvoiceValidationPanel from '@/components/lhdn/InvoiceValidationPanel';
import PDFPreview from '@/components/pdf/PDFPreview';
import { cancelDocument, rejectDocument } from '@/lib/api/documents';
import { downloadInvoicePDF, viewInvoicePDF } from '@/lib/api/pdf';
import Link from 'next/link';
import { Input } from '@/components/ui/input';

export default function InvoiceDetailPage() {
  const params = useParams();
  const router = useRouter();
  const invoiceId = params.id as string;

  const [invoice, setInvoice] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isCancelling, setIsCancelling] = useState(false);
  const [isRejecting, setIsRejecting] = useState(false);
  const [actionSuccess, setActionSuccess] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('details');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    const fetchInvoice = async () => {
      try {
        // In a real implementation, this would fetch the invoice from your API
        // const response = await fetch(`/api/invoices/${invoiceId}`);
        // const data = await response.json();
        // setInvoice(data.invoice);

        // For development, use mock data
        const mockInvoice = {
          id: invoiceId,
          invoiceNumber: 'INV-2025-001',
          dueDate: '2025-11-01',
          issueDate: '2025-10-01',
          status: 'DRAFT',
          customer: {
            id: '1',
            name: 'Acme Corporation',
            email: '<EMAIL>',
            taxId: '*********',
          },
          tax: 15.00,
          notes: 'Payment due within 30 days',
          items: [
            {
              id: '1',
              description: 'Product A',
              quantity: 2,
              unitPrice: 100.00,
              amount: 200.00,
            },
            {
              id: '2',
              description: 'Service B',
              quantity: 1,
              unitPrice: 50.00,
              amount: 50.00,
            },
          ],
          subtotal: 250.00,
          totalAmount: 265.00,
          lhdnValidated: false,
          lhdnValidationId: null,
        };

        setInvoice(mockInvoice);
      } catch (err) {
        console.error('Error fetching invoice:', err);
        setError('Failed to load invoice. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchInvoice();
  }, [invoiceId]);

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this invoice?')) {
      return;
    }

    setIsDeleting(true);

    try {
      // In a real implementation, this would delete the invoice via your API
      // await fetch(`/api/invoices/${invoiceId}`, {
      //   method: 'DELETE',
      // });

      // For development, just simulate a delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Redirect to invoices list
      router.push('/dashboard/invoices');
    } catch (err) {
      console.error('Error deleting invoice:', err);
      alert('Failed to delete invoice. Please try again.');
      setIsDeleting(false);
    }
  };

  const handleValidationComplete = (result: any) => {
    if (result.isValid) {
      setInvoice(prev => ({
        ...prev,
        lhdnValidated: true,
        lhdnValidationId: result.validationId,
      }));
    }
  };

  const handleCancelInvoice = async () => {
    if (!invoice.lhdnValidated || !invoice.lhdnValidationId) {
      setError('Invoice must be validated with LHDN before it can be cancelled');
      return;
    }

    const reason = prompt('Please enter a reason for cancellation:');
    if (!reason) return;

    setIsCancelling(true);
    setError(null);
    setActionSuccess(null);

    try {
      const result = await cancelDocument(invoice.id, reason);

      // Update invoice status
      setInvoice(prev => ({
        ...prev,
        status: 'CANCELLED',
      }));

      setActionSuccess('Invoice cancelled successfully');
    } catch (err: any) {
      console.error('Error cancelling invoice:', err);
      setError(err.message || 'Failed to cancel invoice');
    } finally {
      setIsCancelling(false);
    }
  };

  const handleRejectInvoice = async () => {
    if (!invoice.lhdnValidated || !invoice.lhdnValidationId) {
      setError('Invoice must be validated with LHDN before it can be rejected');
      return;
    }

    const reason = prompt('Please enter a reason for rejection:');
    if (!reason) return;

    setIsRejecting(true);
    setError(null);
    setActionSuccess(null);

    try {
      const result = await rejectDocument(invoice.id, reason);

      // Update invoice status
      setInvoice(prev => ({
        ...prev,
        status: 'REJECTED',
      }));

      setActionSuccess('Invoice rejected successfully');
    } catch (err: any) {
      console.error('Error rejecting invoice:', err);
      setError(err.message || 'Failed to reject invoice');
    } finally {
      setIsRejecting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spinner size="lg" />
        <span className="ml-2 text-text-secondary">Loading...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4">
        <p>{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-2 text-red-600 hover:text-red-800 font-medium"
        >
          Try Again
        </button>
      </div>
    );
  }

  if (!invoice) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 text-yellow-800 rounded-md p-4">
        <p>Invoice not found or you don't have permission to view it.</p>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PAID':
        return 'success';
      case 'OVERDUE':
        return 'destructive';
      case 'SENT':
        return 'default';
      case 'DRAFT':
        return 'secondary';
      case 'CANCELLED':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="bg-white p-6 rounded-lg shadow-sm mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-text-primary flex items-center">
              <svg className="w-6 h-6 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Invoice #{invoice.invoiceNumber}
            </h1>
            <div className="mt-2 flex items-center space-x-2">
              <Badge variant={getStatusColor(invoice.status)}>
                {invoice.status}
              </Badge>
              {invoice.lhdnValidated && (
                <Badge variant="success">LHDN Validated</Badge>
              )}
            </div>
          </div>
          <div className="mt-4 md:mt-0 flex space-x-3">
            <Link href={`/dashboard/invoices/${invoiceId}/edit`} passHref>
              <Button variant="outline" size="sm">
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                Edit
              </Button>
            </Link>
            <Button
              variant="destructive"
              size="sm"
              onClick={handleDelete}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <Spinner size="sm" className="mr-2" />
                  Deleting...
                </>
              ) : (
                <>
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                  Delete
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      <Tabs defaultValue="details" className="space-y-6">
        <TabsList className="mb-6 bg-white p-1 rounded-lg border">
          <TabsTrigger value="details" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">Details</TabsTrigger>
          <TabsTrigger value="preview" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">PDF Preview</TabsTrigger>
        </TabsList>

        <TabsContent value="details">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Left column - Invoice details */}
            <div className="md:col-span-2 space-y-6">
              <Card className="border-none shadow-md">
                <CardHeader className="pb-2 border-b">
                  <CardTitle className="text-lg font-bold text-text-primary">Invoice Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h3 className="text-sm font-medium text-text-secondary">Invoice Number</h3>
                      <p>{invoice.invoiceNumber}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-text-secondary">Status</h3>
                      <Badge variant={getStatusColor(invoice.status)}>
                        {invoice.status}
                      </Badge>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-text-secondary">Issue Date</h3>
                      <p>{invoice.issueDate}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-text-secondary">Due Date</h3>
                      <p>{invoice.dueDate}</p>
                    </div>
                  </div>

                  <div className="border-t pt-4">
                    <h3 className="text-sm font-medium text-text-secondary mb-2">Customer</h3>
                    <p className="font-medium">{invoice.customer.name}</p>
                    <p>{invoice.customer.email}</p>
                    {invoice.customer.taxId && (
                      <p className="text-sm text-text-secondary">Tax ID: {invoice.customer.taxId}</p>
                    )}
                  </div>

                  {invoice.notes && (
                    <div className="border-t pt-4">
                      <h3 className="text-sm font-medium text-text-secondary mb-2">Notes</h3>
                      <p className="text-sm text-text-secondary">{invoice.notes}</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Invoice Items */}
              <Card className="border-none shadow-md">
                <CardHeader className="pb-2 border-b">
                  <CardTitle className="text-lg font-bold text-text-primary">Invoice Items</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm text-left" className="min-w-full divide-y divide-gray-200">
                      <thead>
                        <tr className="bg-white hover:bg-gray-50 transition-colors">
                          <th className="px-3 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">Description</th>
                          <th className="px-3 py-3 text-right text-xs font-medium text-text-secondary uppercase tracking-wider">Quantity</th>
                          <th className="px-3 py-3 text-right text-xs font-medium text-text-secondary uppercase tracking-wider">Unit Price</th>
                          <th className="px-3 py-3 text-right text-xs font-medium text-text-secondary uppercase tracking-wider">Amount</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-200">
                        {invoice.items.map((item: any) => (
                          <tr key={item.id}>
                            <td className="px-3 py-4 text-sm text-text-primary">{item.description}</td>
                            <td className="px-3 py-4 text-sm text-text-primary text-right">{item.quantity}</td>
                            <td className="px-3 py-4 text-sm text-text-primary text-right">{item.unitPrice.toFixed(2)}</td>
                            <td className="px-3 py-4 text-sm text-text-primary text-right">{item.amount.toFixed(2)}</td>
                          </tr>
                        ))}
                      </tbody>
                      <tfoot>
                        <tr className="bg-white hover:bg-gray-50 transition-colors">
                          <td colSpan={3} className="px-3 py-3 text-sm font-medium text-text-primary text-right">Subtotal</td>
                          <td className="px-3 py-3 text-sm font-medium text-text-primary text-right">{invoice.subtotal.toFixed(2)}</td>
                        </tr>
                        <tr className="bg-white hover:bg-gray-50 transition-colors">
                          <td colSpan={3} className="px-3 py-3 text-sm font-medium text-text-primary text-right">Tax</td>
                          <td className="px-3 py-3 text-sm font-medium text-text-primary text-right">{invoice.tax.toFixed(2)}</td>
                        </tr>
                        <tr className="bg-white hover:bg-gray-50 transition-colors">
                          <td colSpan={3} className="px-3 py-3 text-base font-medium text-text-primary text-right">Total</td>
                          <td className="px-3 py-3 text-base font-medium text-text-primary text-right">{invoice.totalAmount.toFixed(2)}</td>
                        </tr>
                      </tfoot>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Right column - LHDN validation */}
            <div className="space-y-6">
              <InvoiceValidationPanel
                invoiceId={invoiceId}
                isNewInvoice={false}
                onValidationComplete={handleValidationComplete}
              />

              {/* Actions card */}
              <Card className="border-none shadow-md">
                <CardHeader className="pb-2 border-b">
                  <CardTitle className="text-lg font-bold text-text-primary">Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {error && (
                    <Alert variant="destructive">
                      <AlertDescription>{error}</AlertDescription>
                    </Alert>
                  )}

                  {actionSuccess && (
                    <Alert variant="success">
                      <AlertDescription>{actionSuccess}</AlertDescription>
                    </Alert>
                  )}

                  <Button className="w-full">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    Send Invoice
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => viewInvoicePDF(invoiceId)}
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                    </svg>
                    Print Invoice
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => downloadInvoicePDF(invoiceId)}
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                    </svg>
                    Download PDF
                  </Button>

                  {invoice?.lhdnValidated && (
                    <>
                      <div className="border-t pt-4">
                        <h3 className="text-sm font-medium text-text-primary mb-2">LHDN Actions</h3>

                        <Button
                          variant="outline"
                          className="w-full mb-2 border-red-300 text-red-700 hover:bg-red-50"
                          onClick={handleCancelInvoice}
                          disabled={isCancelling || invoice.status === 'CANCELLED'}
                        >
                          {isCancelling ? (
                            <>
                              <Spinner size="sm" className="mr-2" />
                              Cancelling...
                            </>
                          ) : (
                            <>
                              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                              </svg>
                              Cancel Invoice
                            </>
                          )}
                        </Button>

                        <Button
                          variant="outline"
                          className="w-full border-orange-300 text-orange-700 hover:bg-orange-50"
                          onClick={handleRejectInvoice}
                          disabled={isRejecting || invoice.status === 'REJECTED'}
                        >
                          {isRejecting ? (
                            <>
                              <Spinner size="sm" className="mr-2" />
                              Rejecting...
                            </>
                          ) : (
                            <>
                              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                              </svg>
                              Reject Invoice
                            </>
                          )}
                        </Button>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="preview">
          <PDFPreview invoiceId={invoiceId} title={`Invoice #${invoice.invoiceNumber}`} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
