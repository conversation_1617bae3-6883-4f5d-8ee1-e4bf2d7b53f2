'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/Badge';
import { Spinner } from '@/components/ui/Spinner';
import { Alert, AlertDescription } from '@/components/ui/Alert';
import Link from 'next/link';

interface Customer {
  id: string;
  name: string;
  email: string;
  taxId?: string;
  taxIdValidated?: boolean;
}

export default function ValidateTINsPage() {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [selectedCustomers, setSelectedCustomers] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isValidating, setIsValidating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationResults, setValidationResults] = useState<{
    success: number;
    failed: number;
    message?: string;
  } | null>(null);

  useEffect(() => {
    const fetchCustomers = async () => {
      try {
        // In a real implementation, this would fetch customers from your API
        // const response = await fetch('/api/customers?missingTaxId=true');
        // const data = await response.json();
        // setCustomers(data.customers);
        
        // For development, use mock data
        const mockCustomers: Customer[] = [
          { id: '1', name: 'Acme Corporation', email: '<EMAIL>', taxId: '*********', taxIdValidated: true },
          { id: '2', name: 'Wayne Enterprises', email: '<EMAIL>', taxId: '*********', taxIdValidated: false },
          { id: '3', name: 'Stark Industries', email: '<EMAIL>', taxId: '', taxIdValidated: false },
          { id: '4', name: 'Daily Planet', email: '<EMAIL>', taxId: '*********', taxIdValidated: false },
          { id: '5', name: 'LexCorp', email: '<EMAIL>', taxId: '', taxIdValidated: false },
          { id: '6', name: 'Queen Industries', email: '<EMAIL>', taxId: '*********', taxIdValidated: true },
          { id: '7', name: 'Oscorp', email: '<EMAIL>', taxId: '', taxIdValidated: false },
        ];
        
        setCustomers(mockCustomers);
      } catch (err) {
        console.error('Error fetching customers:', err);
        setError('Failed to load customers. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchCustomers();
  }, []);

  const toggleSelectCustomer = (customerId: string) => {
    setSelectedCustomers(prev => 
      prev.includes(customerId)
        ? prev.filter(id => id !== customerId)
        : [...prev, customerId]
    );
  };

  const toggleSelectAll = () => {
    if (selectedCustomers.length === customers.length) {
      setSelectedCustomers([]);
    } else {
      setSelectedCustomers(customers.map(c => c.id));
    }
  };

  const validateSelectedTINs = async () => {
    if (selectedCustomers.length === 0) {
      return;
    }
    
    setIsValidating(true);
    setValidationResults(null);
    
    try {
      // In a real implementation, this would call your API to validate the TINs
      // const response = await fetch('/api/customers/validate-tins', {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/json',
      //   },
      //   body: JSON.stringify({ customerIds: selectedCustomers }),
      // });
      // 
      // const data = await response.json();
      // setValidationResults(data);
      
      // For development, simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Update mock data
      const updatedCustomers = [...customers];
      let successCount = 0;
      let failedCount = 0;
      
      selectedCustomers.forEach(id => {
        const customerIndex = updatedCustomers.findIndex(c => c.id === id);
        if (customerIndex !== -1) {
          const customer = updatedCustomers[customerIndex];
          
          // If customer has a tax ID, validate it
          if (customer.taxId) {
            // Simulate validation (80% success rate)
            const isValid = Math.random() > 0.2;
            
            updatedCustomers[customerIndex] = {
              ...customer,
              taxIdValidated: isValid,
            };
            
            if (isValid) {
              successCount++;
            } else {
              failedCount++;
            }
          } else {
            failedCount++;
          }
        }
      });
      
      setCustomers(updatedCustomers);
      setValidationResults({
        success: successCount,
        failed: failedCount,
        message: `Validated ${successCount} TINs successfully. ${failedCount} TINs failed validation.`,
      });
      
      // Clear selection
      setSelectedCustomers([]);
    } catch (err) {
      console.error('Error validating TINs:', err);
      setError('Failed to validate TINs. Please try again.');
    } finally {
      setIsValidating(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spinner size="lg" />
        <span className="ml-2 text-text-secondary">Loading...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4">
        <p>{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-2 text-red-600 hover:text-red-800 font-medium"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow-sm mb-6">
        <h1 className="text-2xl font-bold text-text-primary flex items-center">
          <svg className="w-6 h-6 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          Validate Customer Tax IDs
        </h1>
        <p className="mt-1 text-sm text-text-secondary">
          Validate multiple customer tax IDs with LHDN MyInvois
        </p>
      </div>

      {validationResults && (
        <Alert variant={validationResults.failed === 0 ? "success" : "default"} className="mb-4">
          <AlertDescription>{validationResults.message}</AlertDescription>
        </Alert>
      )}

      <Card className="border-none shadow-md">
        <CardHeader className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <CardTitle className="text-lg font-bold text-text-primary">Customer Tax IDs</CardTitle>
          <div className="mt-2 sm:mt-0 flex space-x-2">
            <Button 
              size="sm" 
              variant="outline"
              onClick={toggleSelectAll}
            >
              {selectedCustomers.length === customers.length ? 'Deselect All' : 'Select All'}
            </Button>
            <Button 
              size="sm"
              onClick={validateSelectedTINs}
              disabled={isValidating || selectedCustomers.length === 0}
            >
              {isValidating ? (
                <>
                  <Spinner size="sm" className="mr-2" />
                  Validating...
                </>
              ) : (
                'Validate Selected'
              )}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm text-left" className="min-w-full divide-y divide-gray-200">
              <thead>
                <tr className="bg-white hover:bg-gray-50 transition-colors">
                  <th className="px-3 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                    <input
                      type="checkbox"
                      checked={selectedCustomers.length === customers.length}
                      onChange={toggleSelectAll}
                      className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                    />
                  </th>
                  <th className="px-3 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">Customer</th>
                  <th className="px-3 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">Email</th>
                  <th className="px-3 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">Tax ID</th>
                  <th className="px-3 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">Status</th>
                  <th className="px-3 py-3 text-right text-xs font-medium text-text-secondary uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {customers.map((customer) => (
                  <tr key={customer.id} className="bg-white  transition-colors">
                    <td className="px-3 py-4">
                      <input
                        type="checkbox"
                        checked={selectedCustomers.includes(customer.id)}
                        onChange={() => toggleSelectCustomer(customer.id)}
                        className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                      />
                    </td>
                    <td className="px-3 py-4 font-medium text-text-primary">{customer.name}</td>
                    <td className="px-3 py-4 text-text-secondary">{customer.email}</td>
                    <td className="px-3 py-4 text-text-primary">
                      {customer.taxId ? customer.taxId : (
                        <span className="text-gray-400 italic">Not set</span>
                      )}
                    </td>
                    <td className="px-3 py-4">
                      {customer.taxId ? (
                        customer.taxIdValidated ? (
                          <Badge variant="success">Validated</Badge>
                        ) : (
                          <Badge variant="secondary">Not Validated</Badge>
                        )
                      ) : (
                        <Badge variant="outline">Missing</Badge>
                      )}
                    </td>
                    <td className="px-3 py-4 text-right">
                      <Link href={`/dashboard/customers/${customer.id}/edit`} className="text-indigo-600 hover:text-indigo-900 font-medium">
                        Edit
                      </Link>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      <Card className="border-none shadow-md">
        <CardHeader className="pb-2 border-b">
          <CardTitle className="text-lg font-bold text-text-primary">About Tax ID Validation</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-text-secondary space-y-4">
            <p>
              Tax ID validation ensures that your customer's tax identification numbers are valid according to LHDN MyInvois requirements.
              This helps ensure compliance with Malaysian tax regulations and prevents issues with invoice validation.
            </p>
            <p>
              Benefits of validating Tax IDs:
            </p>
            <ul className="list-disc pl-5 space-y-2">
              <li>Ensures invoices will be accepted by LHDN MyInvois</li>
              <li>Reduces the risk of tax compliance issues</li>
              <li>Improves the accuracy of your customer database</li>
              <li>Streamlines the invoicing process</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
