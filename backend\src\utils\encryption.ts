import crypto from 'crypto';
import { logger } from './logger';

// Get encryption key from environment variables or use a default (for development only)
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'default-encryption-key-change-in-production';
const ENCRYPTION_IV = process.env.ENCRYPTION_IV || 'default-iv-16-char';
const ALGORITHM = 'aes-256-cbc';

/**
 * Encrypt a string
 * @param text Text to encrypt
 * @returns Encrypted text
 */
export const encrypt = (text: string): string => {
  try {
    // Create key buffer from the encryption key
    const key = crypto.scryptSync(ENCRYPTION_KEY, 'salt', 32);
    
    // Create initialization vector
    const iv = Buffer.from(ENCRYPTION_IV.slice(0, 16), 'utf-8');
    
    // Create cipher
    const cipher = crypto.createCipheriv(ALGORITHM, key, iv);
    
    // Encrypt text
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    return encrypted;
  } catch (error) {
    logger.error('Error encrypting data:', error);
    throw new Error('Encryption failed');
  }
};

/**
 * Decrypt a string
 * @param encryptedText Encrypted text
 * @returns Decrypted text
 */
export const decrypt = (encryptedText: string): string => {
  try {
    // Create key buffer from the encryption key
    const key = crypto.scryptSync(ENCRYPTION_KEY, 'salt', 32);
    
    // Create initialization vector
    const iv = Buffer.from(ENCRYPTION_IV.slice(0, 16), 'utf-8');
    
    // Create decipher
    const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);
    
    // Decrypt text
    let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  } catch (error) {
    logger.error('Error decrypting data:', error);
    throw new Error('Decryption failed');
  }
};

/**
 * Generate a random string
 * @param length Length of the string
 * @returns Random string
 */
export const generateRandomString = (length: number = 32): string => {
  return crypto.randomBytes(Math.ceil(length / 2))
    .toString('hex')
    .slice(0, length);
};

/**
 * Hash a password
 * @param password Password to hash
 * @returns Hashed password
 */
export const hashPassword = (password: string): string => {
  const salt = crypto.randomBytes(16).toString('hex');
  const hash = crypto.pbkdf2Sync(password, salt, 1000, 64, 'sha512').toString('hex');
  return `${salt}:${hash}`;
};

/**
 * Verify a password
 * @param password Password to verify
 * @param hashedPassword Hashed password
 * @returns Whether the password is valid
 */
export const verifyPassword = (password: string, hashedPassword: string): boolean => {
  const [salt, hash] = hashedPassword.split(':');
  const verifyHash = crypto.pbkdf2Sync(password, salt, 1000, 64, 'sha512').toString('hex');
  return hash === verifyHash;
};

/**
 * Generate a secure token
 * @param length Length of the token
 * @returns Secure token
 */
export const generateToken = (length: number = 32): string => {
  return crypto.randomBytes(length).toString('hex');
};
