'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AIOverviewDashboard from '@/components/ai/AIOverviewDashboard';
import AIFinancialInsights from '@/components/ai/AIFinancialInsights';
import CustomerIntelligence from '@/components/ai/CustomerIntelligence';
import DocumentIntelligence from '@/components/ai/DocumentIntelligence';
import AIRecommendations from '@/components/ai/AIRecommendations';

export default function AIPage() {
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">AI Insights Dashboard</h1>
          <p className="mt-1 text-sm text-gray-500">
            AI-powered analytics and insights across your business
          </p>
        </div>
      </div>

      <Tabs defaultValue="overview" onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="mb-6 bg-white p-1 rounded-lg border">
          <TabsTrigger value="overview" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">
            <svg
              className="w-4 h-4 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
              />
            </svg>
            Overview
          </TabsTrigger>
          <TabsTrigger value="financial" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">
            <svg
              className="w-4 h-4 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            Financial
          </TabsTrigger>
          <TabsTrigger value="customers" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">
            <svg
              className="w-4 h-4 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
              />
            </svg>
            Customer
          </TabsTrigger>
          <TabsTrigger value="document" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">
            <svg
              className="w-4 h-4 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
            Document
          </TabsTrigger>
          <TabsTrigger value="recommendations" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">
            <svg
              className="w-4 h-4 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
              />
            </svg>
            Recommendations
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-0">
          <Card className="shadow-md">
            <CardHeader className="bg-white border-b pb-3">
              <CardTitle className="text-lg font-semibold">Business Intelligence Overview</CardTitle>
              <CardDescription>
                AI-powered insights across your entire business
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-6">
              <AIOverviewDashboard />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="financial" className="mt-0">
          <AIFinancialInsights />
        </TabsContent>

        <TabsContent value="customers" className="mt-0">
          <CustomerIntelligence />
        </TabsContent>

        <TabsContent value="document" className="mt-0">
          <DocumentIntelligence />
        </TabsContent>

        <TabsContent value="recommendations" className="mt-0">
          <AIRecommendations />
        </TabsContent>
      </Tabs>
    </div>
  );
}
