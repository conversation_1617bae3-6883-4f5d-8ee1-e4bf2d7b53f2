'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from '@/components/ui/card';
import { apiClient } from '@/lib/api/client';
import { useToast } from '@/components/ui/use-toast';
import BusinessRegistrationField from '@/components/forms/BusinessRegistrationField';

export default function CompanySettingsPage() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [formData, setFormData] = useState({
    name: '',
    businessName: '',
    businessRegNo: '',
    isBusinessRegValid: true,
    email: '',
    phone: '',
    address: '',
    city: '',
    state: '',
    postalCode: '',
    country: 'Malaysia',
    taxId: '',
  });

  // Fetch company data
  useEffect(() => {
    const fetchCompanyData = async () => {
      try {
        const data = await apiClient.get(`/tenants/${user?.tenant?.id}`);
        setFormData({
          ...formData,
          ...data,
          isBusinessRegValid: true,
        });
      } catch (error) {
        console.error('Error fetching company data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load company data',
          variant: 'destructive',
        });
      } finally {
        setIsLoadingData(false);
      }
    };

    if (user?.tenant?.id) {
      fetchCompanyData();
    } else {
      setIsLoadingData(false);
    }
  }, [user?.tenant?.id]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleBusinessRegChange = (value: string, isValid: boolean, businessName?: string) => {
    setFormData((prev) => ({
      ...prev,
      businessRegNo: value,
      isBusinessRegValid: isValid,
      // If business name is provided and the current name is empty, auto-fill it
      businessName: businessName && !prev.businessName ? businessName : prev.businessName,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.isBusinessRegValid) {
      toast({
        title: 'Error',
        description: 'Please enter a valid business registration number',
        variant: 'destructive',
      });
      return;
    }
    
    setIsLoading(true);

    try {
      await apiClient.put(`/tenants/${user?.tenant?.id}`, {
        name: formData.name,
        businessName: formData.businessName,
        businessRegNo: formData.businessRegNo,
        email: formData.email,
        phone: formData.phone,
        address: formData.address,
        city: formData.city,
        state: formData.state,
        postalCode: formData.postalCode,
        country: formData.country,
        taxId: formData.taxId,
      });

      toast({
        title: 'Company Updated',
        description: 'Company information has been updated successfully.',
        variant: 'default',
      });
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update company information',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoadingData) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-2xl font-bold">Company Settings</h1>
        <p className="text-text-secondary">Manage your company information</p>
      </div>

      <Card className="border-none shadow-md">
        <CardHeader className="pb-2 border-b">
          <CardTitle className="text-lg font-bold text-text-primary">Company Information</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <Input
                label="Company Name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
              />
              <Input
                label="Legal Business Name"
                name="businessName"
                value={formData.businessName}
                onChange={handleChange}
                required
              />
            </div>
            
            <BusinessRegistrationField
              value={formData.businessRegNo}
              onChange={handleBusinessRegChange}
              autoValidate={true}
              required
            />
            
            <div className="grid gap-4 md:grid-cols-2">
              <Input
                label="Company Email"
                type="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
              />
              <Input
                label="Phone Number"
                type="tel"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                required
              />
            </div>
            
            <Input
              label="Address"
              name="address"
              value={formData.address}
              onChange={handleChange}
            />
            
            <div className="grid gap-4 md:grid-cols-3">
              <Input
                label="City"
                name="city"
                value={formData.city}
                onChange={handleChange}
              />
              <Input
                label="State"
                name="state"
                value={formData.state}
                onChange={handleChange}
              />
              <Input
                label="Postal Code"
                name="postalCode"
                value={formData.postalCode}
                onChange={handleChange}
              />
            </div>
            
            <div className="grid gap-4 md:grid-cols-2">
              <Input
                label="Country"
                name="country"
                value={formData.country}
                onChange={handleChange}
                disabled
              />
              <Input
                label="Tax ID"
                name="taxId"
                value={formData.taxId}
                onChange={handleChange}
              />
            </div>
          </form>
        </CardContent>
        <CardFooter>
          <Button 
            type="submit" 
            onClick={handleSubmit}
            disabled={isLoading}
          >
            {isLoading ? 'Saving...' : 'Save Changes'}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
