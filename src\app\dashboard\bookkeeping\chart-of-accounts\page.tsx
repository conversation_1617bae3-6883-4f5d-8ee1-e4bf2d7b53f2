'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { PlusCircle, Download, Upload, Filter, Search } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { AccountType, Account } from '@/lib/api/bookkeeping.service';
import bookkeepingService from '@/lib/api/bookkeeping.service';

// Mock data for development
const mockAccounts = [
  {
    id: '1',
    accountCode: '1-1000',
    accountName: 'Cash',
    accountType: 'ASSET',
    accountSubType: 'Current Asset',
    description: 'Cash on hand',
    isActive: true,
    balance: 10000.00,
  },
  {
    id: '2',
    accountCode: '1-1100',
    accountName: 'Bank',
    accountType: 'ASSET',
    accountSubType: 'Current Asset',
    description: 'Bank accounts',
    isActive: true,
    balance: 50000.00,
  },
  {
    id: '3',
    accountCode: '1-1200',
    accountName: 'Accounts Receivable',
    accountType: 'ASSET',
    accountSubType: 'Current Asset',
    description: 'Amounts owed by customers',
    isActive: true,
    balance: 35000.00,
  },
  {
    id: '4',
    accountCode: '1-1300',
    accountName: 'Inventory',
    accountType: 'ASSET',
    accountSubType: 'Current Asset',
    description: 'Goods for sale',
    isActive: true,
    balance: 25000.00,
  },
  {
    id: '5',
    accountCode: '1-2000',
    accountName: 'Fixed Assets',
    accountType: 'ASSET',
    accountSubType: 'Fixed Asset',
    description: 'Long-term assets',
    isActive: true,
    balance: 75000.00,
  },
  {
    id: '6',
    accountCode: '2-1000',
    accountName: 'Accounts Payable',
    accountType: 'LIABILITY',
    accountSubType: 'Current Liability',
    description: 'Amounts owed to suppliers',
    isActive: true,
    balance: 25000.00,
  },
  {
    id: '7',
    accountCode: '2-2000',
    accountName: 'Loans Payable',
    accountType: 'LIABILITY',
    accountSubType: 'Long-term Liability',
    description: 'Long-term loans',
    isActive: true,
    balance: 20000.00,
  },
  {
    id: '8',
    accountCode: '3-1000',
    accountName: 'Share Capital',
    accountType: 'EQUITY',
    accountSubType: 'Capital',
    description: 'Owner\'s investment',
    isActive: true,
    balance: 50000.00,
  },
  {
    id: '9',
    accountCode: '3-2000',
    accountName: 'Retained Earnings',
    accountType: 'EQUITY',
    accountSubType: 'Earnings',
    description: 'Accumulated profits',
    isActive: true,
    balance: 30000.00,
  },
  {
    id: '10',
    accountCode: '4-1000',
    accountName: 'Sales Revenue',
    accountType: 'REVENUE',
    accountSubType: 'Operating Revenue',
    description: 'Income from sales',
    isActive: true,
    balance: 250000.00,
  },
  {
    id: '11',
    accountCode: '5-1000',
    accountName: 'Cost of Goods Sold',
    accountType: 'EXPENSE',
    accountSubType: 'Direct Cost',
    description: 'Cost of items sold',
    isActive: true,
    balance: 150000.00,
  },
  {
    id: '12',
    accountCode: '5-2000',
    accountName: 'Salaries Expense',
    accountType: 'EXPENSE',
    accountSubType: 'Operating Expense',
    description: 'Employee salaries',
    isActive: true,
    balance: 50000.00,
  },
  {
    id: '13',
    accountCode: '5-3000',
    accountName: 'Rent Expense',
    accountType: 'EXPENSE',
    accountSubType: 'Operating Expense',
    description: 'Office rent',
    isActive: true,
    balance: 12000.00,
  },
  {
    id: '14',
    accountCode: '5-4000',
    accountName: 'Utilities Expense',
    accountType: 'EXPENSE',
    accountSubType: 'Operating Expense',
    description: 'Electricity, water, etc.',
    isActive: true,
    balance: 8000.00,
  },
];

export default function ChartOfAccountsPage() {
  const { toast } = useToast();
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string>('ALL');

  // Fetch accounts on component mount
  useEffect(() => {
    const fetchAccounts = async () => {
      try {
        // For development, use mock data
        // In production, uncomment the API call
        // const data = await bookkeepingService.getAccounts();
        // setAccounts(data);
        setAccounts(mockAccounts as unknown as Account[]);
      } catch (error: any) {
        toast({
          title: 'Error',
          description: error.message || 'Failed to fetch accounts',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchAccounts();
  }, [toast]);

  // Filter accounts based on search term and filter type
  const filteredAccounts = accounts.filter(account => {
    const matchesSearch =
      account.accountCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
      account.accountName.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesFilter = filterType === 'ALL' || account.accountType === filterType;

    return matchesSearch && matchesFilter;
  });

  return (
    <div className="space-y-4">
      {/* Page header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between bg-white p-3 md:p-4 rounded-lg shadow-sm mb-3">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <svg className="w-6 h-6 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
            </svg>
            Chart of Accounts
          </h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage your company's chart of accounts
          </p>
        </div>
        <div className="mt-4 md:mt-0 flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-3">
          <div className="relative">
            <Input
              placeholder="Search accounts..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-9 w-full md:w-64"
            />
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          </div>

          <Select value={filterType} onValueChange={setFilterType}>
            <SelectTrigger className="w-full md:w-40">
              <SelectValue placeholder="Filter by type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ALL">All Types</SelectItem>
              {Object.values(AccountType).map((type) => (
                <SelectItem key={type} value={type}>{type}</SelectItem>
              ))}
            </SelectContent>
          </Select>

          <div className="flex space-x-2">
            <Button variant="outline" size="sm">
              <Upload className="mr-2 h-4 w-4" />
              Import
            </Button>
            <Button variant="outline" size="sm">
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
            <Button size="sm" className="bg-indigo-600 hover:bg-indigo-700">
              <PlusCircle className="mr-2 h-4 w-4" /> Add Account
            </Button>
          </div>
        </div>
      </div>

      {/* Chart of Accounts Table */}
      <Card className="border-none shadow-md">
        <CardHeader className="pb-2 border-b">
          <CardTitle className="text-lg font-bold">Chart of Accounts</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full text-sm text-left">
              <thead className="text-xs font-medium text-gray-500 bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-3 py-2">Account Code</th>
                  <th className="px-3 py-2">Account Name</th>
                  <th className="px-3 py-2">Type</th>
                  <th className="px-3 py-2">Subtype</th>
                  <th className="px-3 py-2">Balance (RM)</th>
                  <th className="px-3 py-2">Status</th>
                  <th className="px-3 py-2 text-right">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {isLoading ? (
                  // Loading state
                  Array.from({ length: 5 }).map((_, index) => (
                    <tr key={index} className="bg-white">
                      <td colSpan={7} className="px-3 py-2">
                        <div className="h-6 bg-gray-200 rounded animate-pulse"></div>
                      </td>
                    </tr>
                  ))
                ) : filteredAccounts.length === 0 ? (
                  // Empty state
                  <tr className="bg-white">
                    <td colSpan={7} className="px-3 py-4 text-center text-gray-500">
                      No accounts found. {searchTerm || filterType !== 'ALL' ? 'Try adjusting your filters.' : ''}
                    </td>
                  </tr>
                ) : (
                  // Accounts list
                  filteredAccounts.map((account) => (
                    <tr key={account.id} className="bg-white hover:bg-gray-50 transition-colors">
                      <td className="px-3 py-2 font-medium text-indigo-600">
                        {account.accountCode}
                      </td>
                      <td className="px-3 py-2 font-medium">
                        {account.accountName}
                      </td>
                      <td className="px-3 py-2">
                        {account.accountType}
                      </td>
                      <td className="px-3 py-2">
                        {account.accountSubType}
                      </td>
                      <td className="px-3 py-2 font-medium">
                        {account.balance.toFixed(2)}
                      </td>
                      <td className="px-3 py-2">
                        <span className={`inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium ${
                          account.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {account.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                      <td className="px-3 py-2 text-right">
                        <div className="flex justify-end space-x-1">
                          <Button variant="ghost" size="sm" className="h-7 px-2 text-indigo-600 hover:text-indigo-800 hover:bg-indigo-50">
                            View
                          </Button>
                          <Button variant="ghost" size="sm" className="h-7 px-2 text-gray-600 hover:text-gray-800 hover:bg-gray-50">
                            Edit
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {!isLoading && filteredAccounts.length > 0 && (
            <div className="flex items-center justify-between p-3 border-t border-gray-200">
              <div className="text-sm text-gray-500">
                Showing <span className="font-medium">1</span> to <span className="font-medium">{filteredAccounts.length}</span> of <span className="font-medium">{accounts.length}</span> accounts
                {(searchTerm || filterType !== 'ALL') && (
                  <span> (filtered from {accounts.length} total accounts)</span>
                )}
              </div>
              <div className="flex space-x-2">
                <Button variant="outline" size="sm" disabled>
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                  Previous
                </Button>
                <Button variant="outline" size="sm" disabled={true}>
                  Next
                  <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
