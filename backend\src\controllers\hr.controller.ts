import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { handleError } from '../utils/errorHandler';

// Extend Request type to include user property
// Note: This is already declared in auth.middleware.ts
// We're not declaring it here to avoid conflicts

// Create a type-safe mock of the Prisma client with our models
// This is a temporary solution until the schema is properly updated
type MockPrismaClient = PrismaClient & {
  employee: any;
  salaryRecord: any;
  attendanceRecord: any;
  leaveRequest: any;
};

const prisma = new PrismaClient({
  errorFormat: 'minimal',
}) as MockPrismaClient;

// Employee Controllers
export const createEmployee = async (req: Request, res: Response) => {
  try {
    const {
      employeeId,
      name,
      email,
      phone,
      address,
      position,
      department,
      joinDate
    } = req.body;

    const tenantId = req.user.tenantId;

    const employee = await prisma.employee.create({
      data: {
        employeeId,
        name,
        email,
        phone,
        address,
        position,
        department,
        joinDate: new Date(joinDate),
        tenant: {
          connect: { id: tenantId }
        }
      }
    });

    res.status(201).json(employee);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const getEmployees = async (req: Request, res: Response) => {
  try {
    const tenantId = req.user.tenantId;
    const employees = await prisma.employee.findMany({
      where: {
        tenantId
      },
      orderBy: {
        name: 'asc'
      }
    });

    res.status(200).json(employees);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const getEmployee = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenantId;

    const employee = await prisma.employee.findFirst({
      where: {
        id,
        tenantId
      },
      include: {
        salaryRecords: {
          orderBy: {
            year: 'desc',
            month: 'desc'
          },
          take: 12
        },
        attendanceRecords: {
          orderBy: {
            date: 'desc'
          },
          take: 30
        },
        leaveRequests: {
          orderBy: {
            createdAt: 'desc'
          },
          take: 10
        }
      }
    });

    if (!employee) {
      return res.status(404).json({ message: 'Employee not found' });
    }

    res.status(200).json(employee);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const updateEmployee = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const {
      employeeId,
      name,
      email,
      phone,
      address,
      position,
      department,
      joinDate,
      terminationDate,
      isActive
    } = req.body;

    const tenantId = req.user.tenantId;

    const employee = await prisma.employee.findFirst({
      where: {
        id,
        tenantId
      }
    });

    if (!employee) {
      return res.status(404).json({ message: 'Employee not found' });
    }

    const updatedEmployee = await prisma.employee.update({
      where: { id },
      data: {
        employeeId,
        name,
        email,
        phone,
        address,
        position,
        department,
        joinDate: joinDate ? new Date(joinDate) : undefined,
        terminationDate: terminationDate ? new Date(terminationDate) : null,
        isActive: isActive !== undefined ? isActive : true
      }
    });

    res.status(200).json(updatedEmployee);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const deleteEmployee = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenantId;

    const employee = await prisma.employee.findFirst({
      where: {
        id,
        tenantId
      }
    });

    if (!employee) {
      return res.status(404).json({ message: 'Employee not found' });
    }

    await prisma.employee.delete({
      where: { id }
    });

    res.status(200).json({ message: 'Employee deleted successfully' });
  } catch (error) {
    handleError(error, req, res);
  }
};

// Salary Record Controllers
export const createSalaryRecord = async (req: Request, res: Response) => {
  try {
    const {
      employeeId,
      month,
      year,
      basicSalary,
      allowances,
      deductions,
      tax,
      epf,
      socso,
      paymentDate,
      paymentStatus
    } = req.body;

    const tenantId = req.user.tenantId;

    // Calculate net salary
    const netSalary = parseFloat(basicSalary) +
                      parseFloat(allowances || 0) -
                      parseFloat(deductions || 0) -
                      parseFloat(tax || 0) -
                      parseFloat(epf || 0) -
                      parseFloat(socso || 0);

    const salaryRecord = await prisma.salaryRecord.create({
      data: {
        month: parseInt(month),
        year: parseInt(year),
        basicSalary: parseFloat(basicSalary),
        allowances: parseFloat(allowances || 0),
        deductions: parseFloat(deductions || 0),
        tax: parseFloat(tax || 0),
        epf: parseFloat(epf || 0),
        socso: parseFloat(socso || 0),
        netSalary,
        paymentDate: paymentDate ? new Date(paymentDate) : null,
        paymentStatus: paymentStatus || 'PENDING',
        employee: {
          connect: { id: employeeId }
        },
        tenant: {
          connect: { id: tenantId }
        }
      }
    });

    res.status(201).json(salaryRecord);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const getSalaryRecords = async (req: Request, res: Response) => {
  try {
    const tenantId = req.user.tenantId;
    const { employeeId, month, year } = req.query;

    const whereClause: any = { tenantId };

    if (employeeId) {
      whereClause.employeeId = employeeId as string;
    }

    if (month) {
      whereClause.month = parseInt(month as string);
    }

    if (year) {
      whereClause.year = parseInt(year as string);
    }

    const salaryRecords = await prisma.salaryRecord.findMany({
      where: whereClause,
      include: {
        employee: true
      },
      orderBy: [
        { year: 'desc' },
        { month: 'desc' }
      ]
    });

    res.status(200).json(salaryRecords);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const updateSalaryRecord = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const {
      basicSalary,
      allowances,
      deductions,
      tax,
      epf,
      socso,
      paymentDate,
      paymentStatus
    } = req.body;

    const tenantId = req.user.tenantId;

    const salaryRecord = await prisma.salaryRecord.findFirst({
      where: {
        id,
        tenantId
      }
    });

    if (!salaryRecord) {
      return res.status(404).json({ message: 'Salary record not found' });
    }

    // Calculate net salary
    const netSalary = parseFloat(basicSalary) +
                      parseFloat(allowances || 0) -
                      parseFloat(deductions || 0) -
                      parseFloat(tax || 0) -
                      parseFloat(epf || 0) -
                      parseFloat(socso || 0);

    const updatedSalaryRecord = await prisma.salaryRecord.update({
      where: { id },
      data: {
        basicSalary: parseFloat(basicSalary),
        allowances: parseFloat(allowances || 0),
        deductions: parseFloat(deductions || 0),
        tax: parseFloat(tax || 0),
        epf: parseFloat(epf || 0),
        socso: parseFloat(socso || 0),
        netSalary,
        paymentDate: paymentDate ? new Date(paymentDate) : null,
        paymentStatus: paymentStatus || 'PENDING'
      }
    });

    res.status(200).json(updatedSalaryRecord);
  } catch (error) {
    handleError(error, req, res);
  }
};

// Attendance Record Controllers
export const createAttendanceRecord = async (req: Request, res: Response) => {
  try {
    const {
      employeeId,
      date,
      checkIn,
      checkOut,
      status,
      notes
    } = req.body;

    const tenantId = req.user.tenantId;

    // Check if record already exists for this employee and date
    const existingRecord = await prisma.attendanceRecord.findFirst({
      where: {
        employeeId,
        date: new Date(date),
        tenantId
      }
    });

    if (existingRecord) {
      return res.status(400).json({ message: 'Attendance record already exists for this date' });
    }

    const attendanceRecord = await prisma.attendanceRecord.create({
      data: {
        date: new Date(date),
        checkIn: checkIn ? new Date(checkIn) : null,
        checkOut: checkOut ? new Date(checkOut) : null,
        status: status || 'PRESENT',
        notes,
        employee: {
          connect: { id: employeeId }
        },
        tenant: {
          connect: { id: tenantId }
        }
      }
    });

    res.status(201).json(attendanceRecord);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const getAttendanceRecords = async (req: Request, res: Response) => {
  try {
    const tenantId = req.user.tenantId;
    const { employeeId, startDate, endDate, status } = req.query;

    const whereClause: any = { tenantId };

    if (employeeId) {
      whereClause.employeeId = employeeId as string;
    }

    if (startDate && endDate) {
      whereClause.date = {
        gte: new Date(startDate as string),
        lte: new Date(endDate as string)
      };
    } else if (startDate) {
      whereClause.date = {
        gte: new Date(startDate as string)
      };
    } else if (endDate) {
      whereClause.date = {
        lte: new Date(endDate as string)
      };
    }

    if (status) {
      whereClause.status = status as string;
    }

    const attendanceRecords = await prisma.attendanceRecord.findMany({
      where: whereClause,
      include: {
        employee: true
      },
      orderBy: {
        date: 'desc'
      }
    });

    res.status(200).json(attendanceRecords);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const updateAttendanceRecord = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const {
      checkIn,
      checkOut,
      status,
      notes
    } = req.body;

    const tenantId = req.user.tenantId;

    const attendanceRecord = await prisma.attendanceRecord.findFirst({
      where: {
        id,
        tenantId
      }
    });

    if (!attendanceRecord) {
      return res.status(404).json({ message: 'Attendance record not found' });
    }

    const updatedAttendanceRecord = await prisma.attendanceRecord.update({
      where: { id },
      data: {
        checkIn: checkIn ? new Date(checkIn) : null,
        checkOut: checkOut ? new Date(checkOut) : null,
        status: status || attendanceRecord.status,
        notes
      }
    });

    res.status(200).json(updatedAttendanceRecord);
  } catch (error) {
    handleError(error, req, res);
  }
};

// Leave Request Controllers
export const createLeaveRequest = async (req: Request, res: Response) => {
  try {
    const {
      employeeId,
      leaveType,
      startDate,
      endDate,
      reason
    } = req.body;

    const tenantId = req.user.tenantId;

    // Calculate total days (including weekends for simplicity)
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const totalDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 to include both start and end days

    const leaveRequest = await prisma.leaveRequest.create({
      data: {
        leaveType,
        startDate: start,
        endDate: end,
        totalDays,
        reason,
        employee: {
          connect: { id: employeeId }
        },
        tenant: {
          connect: { id: tenantId }
        }
      }
    });

    res.status(201).json(leaveRequest);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const getLeaveRequests = async (req: Request, res: Response) => {
  try {
    const tenantId = req.user.tenantId;
    const { employeeId, status, leaveType } = req.query;

    const whereClause: any = { tenantId };

    if (employeeId) {
      whereClause.employeeId = employeeId as string;
    }

    if (status) {
      whereClause.status = status as string;
    }

    if (leaveType) {
      whereClause.leaveType = leaveType as string;
    }

    const leaveRequests = await prisma.leaveRequest.findMany({
      where: whereClause,
      include: {
        employee: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    res.status(200).json(leaveRequests);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const updateLeaveRequest = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const {
      status,
      approvedBy
    } = req.body;

    const tenantId = req.user.tenantId;
    // Use the approvedBy from request body or fallback to a string version of user ID
    const userId = String(req.user.id);

    const leaveRequest = await prisma.leaveRequest.findFirst({
      where: {
        id,
        tenantId
      }
    });

    if (!leaveRequest) {
      return res.status(404).json({ message: 'Leave request not found' });
    }

    const updatedLeaveRequest = await prisma.leaveRequest.update({
      where: { id },
      data: {
        status,
        approvedBy: approvedBy || userId,
        approvalDate: new Date()
      }
    });

    // If approved, create attendance records for the leave period
    if (status === 'APPROVED') {
      const start = new Date(leaveRequest.startDate);
      const end = new Date(leaveRequest.endDate);

      // Create attendance records for each day of the leave
      for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
        // Check if attendance record already exists
        const existingRecord = await prisma.attendanceRecord.findFirst({
          where: {
            employeeId: leaveRequest.employeeId,
            date: new Date(date),
            tenantId
          }
        });

        if (!existingRecord) {
          await prisma.attendanceRecord.create({
            data: {
              date: new Date(date),
              status: 'ON_LEAVE',
              notes: `${leaveRequest.leaveType} leave`,
              employee: {
                connect: { id: leaveRequest.employeeId }
              },
              tenant: {
                connect: { id: tenantId }
              }
            }
          });
        }
      }
    }

    res.status(200).json(updatedLeaveRequest);
  } catch (error) {
    handleError(error, req, res);
  }
};
