import { Request, Response } from 'express';
import { prisma } from '../index';
import {
  generateFinancialInsights,
  detectFraud,
  processDocument,
  generateCustomerInsights,
  generateRecommendations,
  validateInvoiceForLHDN
} from '../services/ai.service';
import multer from 'multer';
import path from 'path';
import fs from 'fs';

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

export const upload = multer({
  storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf'];
    if (!allowedTypes.includes(file.mimetype)) {
      return cb(new Error('Only JPEG, PNG, and PDF files are allowed'));
    }
    cb(null, true);
  }
});

// Get financial insights for a tenant
export const getFinancialInsights = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId } = req.user;
    const { startDate, endDate, predictionPeriod } = req.query;

    // Validate query parameters
    const parsedStartDate = startDate ? new Date(startDate as string) : new Date(new Date().setMonth(new Date().getMonth() - 3));
    const parsedEndDate = endDate ? new Date(endDate as string) : new Date();
    const parsedPredictionPeriod = predictionPeriod ? parseInt(predictionPeriod as string) : 3;

    if (isNaN(parsedStartDate.getTime()) || isNaN(parsedEndDate.getTime())) {
      return res.status(400).json({ message: 'Invalid date format' });
    }

    // Get invoices for the tenant within the date range
    const invoices = await prisma.invoice.findMany({
      where: {
        tenantId,
        issueDate: {
          gte: parsedStartDate,
          lte: parsedEndDate
        }
      },
      select: {
        id: true,
        invoiceNumber: true,
        status: true,
        dueDate: true,
        issueDate: true,
        totalAmount: true,
        customerId: true,
      },
    });

    // Get payments for the tenant's invoices
    const payments = await prisma.payment.findMany({
      where: {
        invoice: {
          tenantId,
          issueDate: {
            gte: parsedStartDate,
            lte: parsedEndDate
          }
        },
      },
      select: {
        id: true,
        amount: true,
        paymentDate: true,
        invoiceId: true,
      },
    });

    // Get customers for the tenant
    const customers = await prisma.customer.findMany({
      where: { tenantId },
      select: {
        id: true,
        name: true,
        email: true,
      },
    });

    // Generate financial insights
    const insights = await generateFinancialInsights(
      invoices,
      payments,
      customers,
      parsedPredictionPeriod
    );

    return res.status(200).json(insights);
  } catch (error: any) {
    console.error('Error generating financial insights:', error);
    return res.status(500).json({ message: 'Failed to generate financial insights', error: error.message });
  }
};

// Detect potential fraud in an invoice
export const detectInvoiceFraud = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId } = req.user;
    const { invoiceId } = req.params;

    // Get the invoice to check
    const invoice = await prisma.invoice.findFirst({
      where: {
        id: invoiceId,
        tenantId,
      },
      select: {
        id: true,
        invoiceNumber: true,
        status: true,
        dueDate: true,
        issueDate: true,
        totalAmount: true,
        customerId: true,
      },
    });

    if (!invoice) {
      return res.status(404).json({ message: 'Invoice not found or does not belong to your tenant' });
    }

    // Get previous invoices for comparison
    const previousInvoices = await prisma.invoice.findMany({
      where: {
        tenantId,
        id: { not: invoiceId },
      },
      select: {
        id: true,
        invoiceNumber: true,
        status: true,
        dueDate: true,
        issueDate: true,
        totalAmount: true,
        customerId: true,
      },
    });

    // Detect potential fraud
    const fraudDetectionResult = await detectFraud(invoice, previousInvoices);

    return res.status(200).json({
      invoiceId,
      invoiceNumber: invoice.invoiceNumber,
      ...fraudDetectionResult,
    });
  } catch (error: any) {
    console.error('Error detecting fraud:', error);
    return res.status(500).json({ message: 'Failed to detect fraud', error: error.message });
  }
};

// Get payment predictions for a customer
export const getPaymentPredictions = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId } = req.user;
    const { customerId } = req.params;

    // Check if customer exists and belongs to the tenant
    const customer = await prisma.customer.findFirst({
      where: {
        id: customerId,
        tenantId,
      },
    });

    if (!customer) {
      return res.status(404).json({ message: 'Customer not found or does not belong to your tenant' });
    }

    // Get customer's invoices
    const invoices = await prisma.invoice.findMany({
      where: {
        customerId,
        tenantId,
      },
      include: {
        payments: true,
      },
    });

    // Calculate payment behavior
    const paidInvoices = invoices.filter(
      (invoice: any) => invoice.payments.length > 0
    );

    if (paidInvoices.length === 0) {
      return res.status(200).json({
        customerId,
        customerName: customer.name,
        prediction: {
          averagePaymentTime: null,
          paymentProbability: null,
          message: 'Not enough payment history to make predictions',
        },
      });
    }

    // Calculate average payment time
    const paymentTimes = paidInvoices.map((invoice: any) => {
      const invoiceDate = new Date(invoice.issueDate);
      const paymentDate = new Date(invoice.payments[0].paymentDate);
      const diffTime = Math.abs(paymentDate.getTime() - invoiceDate.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return diffDays;
    });

    const averagePaymentTime =
      paymentTimes.reduce((sum: number, days: number) => sum + days, 0) / paymentTimes.length;

    // Calculate on-time payment percentage
    const onTimePayments = paidInvoices.filter((invoice: any) => {
      const dueDate = new Date(invoice.dueDate);
      const paymentDate = new Date(invoice.payments[0].paymentDate);
      return paymentDate <= dueDate;
    });

    const paymentProbability = (onTimePayments.length / paidInvoices.length) * 100;

    // Generate prediction message
    let message = '';
    if (paymentProbability >= 80) {
      message = 'This customer has a high probability of paying on time.';
    } else if (paymentProbability >= 50) {
      message = 'This customer has a moderate probability of paying on time.';
    } else {
      message = 'This customer has a low probability of paying on time. Consider sending early reminders.';
    }

    return res.status(200).json({
      customerId,
      customerName: customer.name,
      prediction: {
        averagePaymentTime: Math.round(averagePaymentTime),
        paymentProbability: Math.round(paymentProbability),
        message,
      },
    });
  } catch (error: any) {
    console.error('Error generating payment predictions:', error);
    return res.status(500).json({ message: 'Failed to generate payment predictions', error: error.message });
  }
};

// Process document with OCR
export const processDocumentWithOCR = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    if (!req.file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }

    const { tenantId } = req.user;
    const filePath = req.file.path;
    const fileType = req.file.mimetype;

    // Process the document
    const result = await processDocument(filePath, fileType, tenantId);

    // Return the extracted data
    return res.status(200).json(result);
  } catch (error: any) {
    console.error('Error processing document:', error);
    return res.status(500).json({ message: 'Failed to process document', error: error.message });
  }
};

// Get customer insights
export const getCustomerInsights = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId } = req.user;
    const { startDate, endDate } = req.query;

    // Validate query parameters
    const parsedStartDate = startDate ? new Date(startDate as string) : new Date(new Date().setMonth(new Date().getMonth() - 3));
    const parsedEndDate = endDate ? new Date(endDate as string) : new Date();

    if (isNaN(parsedStartDate.getTime()) || isNaN(parsedEndDate.getTime())) {
      return res.status(400).json({ message: 'Invalid date format' });
    }

    // Get customers for the tenant
    const customers = await prisma.customer.findMany({
      where: { tenantId },
      select: {
        id: true,
        name: true,
        email: true,
        phone: true,
      },
    });

    // Get invoices for the tenant within the date range
    const invoices = await prisma.invoice.findMany({
      where: {
        tenantId,
        issueDate: {
          gte: parsedStartDate,
          lte: parsedEndDate
        }
      },
      include: {
        payments: true,
        customer: true,
      },
    });

    // Generate customer insights
    const insights = await generateCustomerInsights(customers, invoices);

    return res.status(200).json(insights);
  } catch (error: any) {
    console.error('Error generating customer insights:', error);
    return res.status(500).json({ message: 'Failed to generate customer insights', error: error.message });
  }
};

// Get AI recommendations
export const getAIRecommendations = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId } = req.user;
    const { type } = req.params;

    // Validate recommendation type
    if (!['business', 'compliance', 'customer'].includes(type)) {
      return res.status(400).json({ message: 'Invalid recommendation type. Must be one of: business, compliance, customer' });
    }

    // Get data for generating recommendations
    const invoices = await prisma.invoice.findMany({
      where: { tenantId },
      include: {
        payments: true,
        customer: true,
      },
    });

    const customers = await prisma.customer.findMany({
      where: { tenantId },
    });

    // Generate recommendations
    const recommendations = await generateRecommendations(
      type as 'business' | 'compliance' | 'customer',
      invoices,
      customers,
      tenantId
    );

    return res.status(200).json(recommendations);
  } catch (error: any) {
    console.error('Error generating recommendations:', error);
    return res.status(500).json({ message: 'Failed to generate recommendations', error: error.message });
  }
};

// Validate invoice for LHDN compliance
export const validateInvoice = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId } = req.user;
    const { invoiceId } = req.params;

    // Get the invoice to validate
    const invoice = await prisma.invoice.findFirst({
      where: {
        id: invoiceId,
        tenantId,
      },
      include: {
        customer: true,
        items: true,
      },
    });

    if (!invoice) {
      return res.status(404).json({ message: 'Invoice not found or does not belong to your tenant' });
    }

    // Validate the invoice for LHDN compliance
    const validationResult = await validateInvoiceForLHDN(invoice);

    // Update the invoice validation status in the database
    await prisma.invoice.update({
      where: { id: invoiceId },
      data: {
        validationStatus: validationResult.isValid ? 'VALID' : 'INVALID',
        validationDate: new Date(),
      },
    });

    return res.status(200).json(validationResult);
  } catch (error: any) {
    console.error('Error validating invoice:', error);
    return res.status(500).json({ message: 'Failed to validate invoice', error: error.message });
  }
};
