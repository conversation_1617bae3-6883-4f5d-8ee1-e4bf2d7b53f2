import jwt from 'jsonwebtoken';
import bcrypt from 'bcrypt';
import crypto from 'crypto';
import config from '../config/config';

// Interface for JWT payload
export interface JwtPayload {
  userId: string;
  tenantId: string;
  email: string;
  role: string;
}

// Access token configuration
const ACCESS_TOKEN_SECRET = process.env.ACCESS_TOKEN_SECRET || 'your-super-secret-jwt-key-change-in-production';
const ACCESS_TOKEN_EXPIRY = '1h'; // Short-lived access token

// Refresh token configuration
const REFRESH_TOKEN_SECRET = process.env.REFRESH_TOKEN_SECRET || 'your-super-secret-refresh-key-change-in-production';
const REFRESH_TOKEN_EXPIRY = '7d'; // Longer-lived refresh token

// Generate access token (short-lived)
export const generateAccessToken = (payload: JwtPayload): string => {
  return jwt.sign(payload, ACCESS_TOKEN_SECRET, {
    expiresIn: ACCESS_TOKEN_EXPIRY,
  });
};

// Generate refresh token (long-lived)
export const generateRefreshToken = (payload: JwtPayload): string => {
  return jwt.sign(payload, REFRESH_TOKEN_SECRET, {
    expiresIn: REFRESH_TOKEN_EXPIRY,
  });
};

// Generate both tokens
export const generateTokens = (payload: JwtPayload): { accessToken: string; refreshToken: string } => {
  return {
    accessToken: generateAccessToken(payload),
    refreshToken: generateRefreshToken(payload),
  };
};

// Legacy function for backward compatibility
export const generateToken = (payload: JwtPayload): string => {
  return generateAccessToken(payload);
};

// Verify access token
export const verifyAccessToken = (token: string): JwtPayload => {
  try {
    return jwt.verify(token, ACCESS_TOKEN_SECRET) as JwtPayload;
  } catch (error) {
    throw new Error('Invalid access token');
  }
};

// Verify refresh token
export const verifyRefreshToken = (token: string): JwtPayload => {
  try {
    return jwt.verify(token, REFRESH_TOKEN_SECRET) as JwtPayload;
  } catch (error) {
    throw new Error('Invalid refresh token');
  }
};

// Legacy function for backward compatibility
export const verifyToken = (token: string): JwtPayload => {
  return verifyAccessToken(token);
};

// Generate a secure random token
export const generateSecureToken = (length: number = 64): string => {
  return crypto.randomBytes(length).toString('hex');
};

// Hash password
export const hashPassword = async (password: string): Promise<string> => {
  const saltRounds = 10;
  return bcrypt.hash(password, saltRounds);
};

// Compare password with hash
export const comparePassword = async (
  password: string,
  hash: string
): Promise<boolean> => {
  return bcrypt.compare(password, hash);
};
