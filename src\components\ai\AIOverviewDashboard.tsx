'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';

export default function AIOverviewDashboard() {
  const [isLoading, setIsLoading] = useState(true);
  const [insights, setInsights] = useState<any>(null);
  
  useEffect(() => {
    // In a real implementation, this would fetch data from your API
    const fetchInsights = async () => {
      setIsLoading(true);
      
      try {
        // Simulate API call with timeout
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock data for demonstration
        const mockInsights = {
          businessHealth: {
            score: 78,
            trend: '+3%',
            status: 'healthy',
            recommendations: 2
          },
          financialMetrics: {
            cashFlow: {
              current: 25000,
              previous: 22000,
              trend: '+13.6%'
            },
            accountsReceivable: {
              current: 45000,
              previous: 48000,
              trend: '-6.3%'
            },
            accountsPayable: {
              current: 32000,
              previous: 30000,
              trend: '+6.7%'
            },
            profitMargin: {
              current: 22,
              previous: 20,
              trend: '+10%'
            }
          },
          inventoryInsights: {
            lowStock: 5,
            overstock: 3,
            optimalStock: 42,
            totalItems: 50
          },
          customerMetrics: {
            totalCustomers: 120,
            activeCustomers: 85,
            atRiskCustomers: 12,
            newCustomers: 8
          },
          fraudAlerts: {
            highRisk: 2,
            mediumRisk: 5,
            lowRisk: 8,
            total: 15
          }
        };
        
        setInsights(mockInsights);
      } catch (err) {
        console.error('Error fetching AI insights:', err);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchInsights();
  }, []);

  if (isLoading || !insights) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        <span className="ml-2 text-gray-600">Loading AI insights...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Business Health Score */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Business Health Score</CardTitle>
          <CardDescription>AI-generated overall health assessment</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div className="mb-4 md:mb-0">
              <div className="text-4xl font-bold">{insights.businessHealth.score}/100</div>
              <div className="flex items-center mt-1">
                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                  {insights.businessHealth.trend}
                </Badge>
                <span className="ml-2 text-sm text-gray-500">from last month</span>
              </div>
            </div>
            <div className="w-full md:w-2/3">
              <div className="mb-2 flex justify-between items-center">
                <span className="text-sm font-medium">Health Score</span>
                <span className="text-sm font-medium">{insights.businessHealth.score}%</span>
              </div>
              <Progress value={insights.businessHealth.score} className="h-2" />
              <div className="mt-4 text-sm">
                <span className="font-medium">AI Assessment: </span>
                <span className="text-green-600 font-medium">
                  {insights.businessHealth.status === 'healthy' ? 'Healthy' : 
                   insights.businessHealth.status === 'warning' ? 'Needs Attention' : 'Critical'}
                </span>
                <span className="ml-2">
                  {insights.businessHealth.recommendations > 0 && 
                    `(${insights.businessHealth.recommendations} recommendations available)`}
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Cash Flow</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              RM {insights.financialMetrics.cashFlow.current.toLocaleString()}
            </div>
            <div className="mt-1">
              <Badge 
                variant="outline" 
                className={insights.financialMetrics.cashFlow.trend.startsWith('+') ? 
                  "bg-green-50 text-green-700 border-green-200" : 
                  "bg-red-50 text-red-700 border-red-200"}
              >
                {insights.financialMetrics.cashFlow.trend}
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Accounts Receivable</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              RM {insights.financialMetrics.accountsReceivable.current.toLocaleString()}
            </div>
            <div className="mt-1">
              <Badge 
                variant="outline" 
                className={insights.financialMetrics.accountsReceivable.trend.startsWith('-') ? 
                  "bg-green-50 text-green-700 border-green-200" : 
                  "bg-yellow-50 text-yellow-700 border-yellow-200"}
              >
                {insights.financialMetrics.accountsReceivable.trend}
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Accounts Payable</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              RM {insights.financialMetrics.accountsPayable.current.toLocaleString()}
            </div>
            <div className="mt-1">
              <Badge 
                variant="outline" 
                className={insights.financialMetrics.accountsPayable.trend.startsWith('-') ? 
                  "bg-green-50 text-green-700 border-green-200" : 
                  "bg-yellow-50 text-yellow-700 border-yellow-200"}
              >
                {insights.financialMetrics.accountsPayable.trend}
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Profit Margin</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {insights.financialMetrics.profitMargin.current}%
            </div>
            <div className="mt-1">
              <Badge 
                variant="outline" 
                className={insights.financialMetrics.profitMargin.trend.startsWith('+') ? 
                  "bg-green-50 text-green-700 border-green-200" : 
                  "bg-red-50 text-red-700 border-red-200"}
              >
                {insights.financialMetrics.profitMargin.trend}
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Alert Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Inventory Insights</CardTitle>
            <CardDescription>AI-detected inventory status</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm">Low Stock Items</span>
                <Badge variant="destructive">{insights.inventoryInsights.lowStock}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Overstocked Items</span>
                <Badge variant="secondary">{insights.inventoryInsights.overstock}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Optimal Stock</span>
                <Badge variant="outline" className="bg-green-50 text-green-700">
                  {insights.inventoryInsights.optimalStock}
                </Badge>
              </div>
              <div className="pt-2 text-xs text-gray-500">
                {Math.round((insights.inventoryInsights.optimalStock / insights.inventoryInsights.totalItems) * 100)}% of inventory at optimal levels
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Customer Insights</CardTitle>
            <CardDescription>AI-analyzed customer metrics</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm">Active Customers</span>
                <span className="font-medium">{insights.customerMetrics.activeCustomers}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">At-Risk Customers</span>
                <Badge variant="destructive">{insights.customerMetrics.atRiskCustomers}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">New Customers</span>
                <Badge variant="outline" className="bg-green-50 text-green-700">
                  {insights.customerMetrics.newCustomers}
                </Badge>
              </div>
              <div className="pt-2 text-xs text-gray-500">
                {Math.round((insights.customerMetrics.activeCustomers / insights.customerMetrics.totalCustomers) * 100)}% customer activity rate
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Fraud Alerts</CardTitle>
            <CardDescription>AI-detected potential fraud</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm">High Risk</span>
                <Badge variant="destructive">{insights.fraudAlerts.highRisk}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Medium Risk</span>
                <Badge variant="secondary">{insights.fraudAlerts.mediumRisk}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Low Risk</span>
                <Badge variant="outline">{insights.fraudAlerts.lowRisk}</Badge>
              </div>
              <div className="pt-2 text-xs text-gray-500">
                {insights.fraudAlerts.total} total alerts requiring review
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
