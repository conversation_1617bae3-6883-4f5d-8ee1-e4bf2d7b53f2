import { Router } from 'express';
import {
  validateInvoiceWithLHDN,
  checkValidationStatus,
  validateBusinessRegNoController
} from '../controllers/lhdn.controller';
import { authenticate } from '../middleware/auth.middleware';
import { ExpressHandler } from '../types/express';

const router = Router();

// All routes are protected
router.post('/validate/:invoiceId', authenticate as ExpressHandler, validateInvoiceWithLHDN as ExpressHandler);
router.get('/status/:invoiceId', authenticate as ExpressHandler, checkValidationStatus as ExpressHandler);
router.get('/validate-business/:regNo', authenticate as ExpressHandler, validateBusinessRegNoController as ExpressHandler);

export default router;
