'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';

export default function ProfilePage() {
  const [activeTab, setActiveTab] = useState('general');
  const [searchTerm, setSearchTerm] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  
  // Mock user data
  const [userData, setUserData] = useState({
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+60123456789',
    company: 'Acme Corporation',
    position: 'Finance Manager',
    avatar: '',
    twoFactorEnabled: false,
    emailNotifications: true,
    language: 'en',
    timezone: 'Asia/Kuala_Lumpur'
  });

  const handleSaveProfile = () => {
    // In a real implementation, this would call your API
    setIsEditing(false);
    setSuccessMessage('Profile updated successfully');
    
    // Clear success message after 3 seconds
    setTimeout(() => {
      setSuccessMessage(null);
    }, 3000);
  };

  const handleToggleTwoFactor = () => {
    setUserData({
      ...userData,
      twoFactorEnabled: !userData.twoFactorEnabled
    });
  };

  const handleToggleEmailNotifications = () => {
    setUserData({
      ...userData,
      emailNotifications: !userData.emailNotifications
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between bg-white p-6 rounded-lg shadow-sm mb-6">
        <div>
          <h1 className="text-2xl font-bold text-text-primary">Profile</h1>
          <p className="mt-1 text-sm text-text-secondary">
            Manage your account settings and preferences
          </p>
        </div>
      </div>

      {successMessage && (
        <Alert variant="success" className="bg-green-50 text-green-800 border-green-200">
          <AlertDescription>{successMessage}</AlertDescription>
        </Alert>
      )}

      <div className="flex flex-col md:flex-row gap-6">
        {/* Profile sidebar */}
        <div className="md:w-1/3">
          <Card className="border-none shadow-md">
            <CardHeader className="pb-2 border-b">
              <div className="flex flex-col items-center">
                <Avatar className="h-24 w-24">
                  <AvatarImage src={userData.avatar || ''} alt={userData.name} />
                  <AvatarFallback className="text-2xl bg-indigo-100 text-indigo-600">
                    {userData.name.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
                <div className="mt-4 text-center">
                  <h2 className="text-xl font-bold">{userData.name}</h2>
                  <p className="text-sm text-text-secondary">{userData.position}</p>
                  <p className="text-sm text-text-secondary">{userData.company}</p>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center">
                  <svg className="w-5 h-5 mr-2 text-text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  <span className="text-sm">{userData.email}</span>
                </div>
                <div className="flex items-center">
                  <svg className="w-5 h-5 mr-2 text-text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                  </svg>
                  <span className="text-sm">{userData.phone}</span>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full" onClick={() => setIsEditing(true)}>
                Edit Profile
              </Button>
            </CardFooter>
          </Card>
        </div>

        {/* Profile content */}
        <div className="flex-1">
          <Card className="border-none shadow-md">
            <CardHeader className="pb-2 border-b">
              <Tabs defaultValue="general" onValueChange={setActiveTab}>
                <TabsList className="mb-6 bg-white p-1 rounded-lg border">
                  <TabsTrigger value="general" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">General</TabsTrigger>
                  <TabsTrigger value="security" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">Security</TabsTrigger>
                  <TabsTrigger value="preferences" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">Preferences</TabsTrigger>
                </TabsList>
              </Tabs>
            </CardHeader>
            <CardContent>
              <TabsContent value="general" className="space-y-6">
                {isEditing ? (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="name">Full Name</Label>
                        <Input 
                          id="name" 
                          value={userData.name} 
                          onChange={(e) => setUserData({...userData, name: e.target.value})}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="email">Email</Label>
                        <Input 
                          id="email" 
                          type="email" 
                          value={userData.email} 
                          onChange={(e) => setUserData({...userData, email: e.target.value})}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="phone">Phone</Label>
                        <Input 
                          id="phone" 
                          value={userData.phone} 
                          onChange={(e) => setUserData({...userData, phone: e.target.value})}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="company">Company</Label>
                        <Input 
                          id="company" 
                          value={userData.company} 
                          onChange={(e) => setUserData({...userData, company: e.target.value})}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="position">Position</Label>
                        <Input 
                          id="position" 
                          value={userData.position} 
                          onChange={(e) => setUserData({...userData, position: e.target.value})}
                        />
                      </div>
                    </div>
                    <div className="flex justify-end space-x-2">
                      <Button variant="outline" onClick={() => setIsEditing(false)}>
                        Cancel
                      </Button>
                      <Button onClick={handleSaveProfile}>
                        Save Changes
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-medium">Personal Information</h3>
                      <Separator className="my-4" />
                      <dl className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-6">
                        <div>
                          <dt className="text-sm font-medium text-text-secondary">Full Name</dt>
                          <dd className="mt-1 text-sm text-text-primary">{userData.name}</dd>
                        </div>
                        <div>
                          <dt className="text-sm font-medium text-text-secondary">Email</dt>
                          <dd className="mt-1 text-sm text-text-primary">{userData.email}</dd>
                        </div>
                        <div>
                          <dt className="text-sm font-medium text-text-secondary">Phone</dt>
                          <dd className="mt-1 text-sm text-text-primary">{userData.phone}</dd>
                        </div>
                      </dl>
                    </div>
                    <div>
                      <h3 className="text-lg font-medium">Company Information</h3>
                      <Separator className="my-4" />
                      <dl className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-6">
                        <div>
                          <dt className="text-sm font-medium text-text-secondary">Company</dt>
                          <dd className="mt-1 text-sm text-text-primary">{userData.company}</dd>
                        </div>
                        <div>
                          <dt className="text-sm font-medium text-text-secondary">Position</dt>
                          <dd className="mt-1 text-sm text-text-primary">{userData.position}</dd>
                        </div>
                      </dl>
                    </div>
                  </div>
                )}
              </TabsContent>
              
              <TabsContent value="security" className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium">Password</h3>
                  <Separator className="my-4" />
                  <div className="space-y-4">
                    <p className="text-sm text-text-secondary">
                      Change your password to keep your account secure.
                    </p>
                    <Button variant="outline" size="sm">
                      Change Password
                    </Button>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-medium">Two-Factor Authentication</h3>
                  <Separator className="my-4" />
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="text-sm font-medium">
                        {userData.twoFactorEnabled ? 'Enabled' : 'Disabled'}
                      </p>
                      <p className="text-sm text-text-secondary">
                        Add an extra layer of security to your account
                      </p>
                    </div>
                    <Switch
                      checked={userData.twoFactorEnabled}
                      onCheckedChange={handleToggleTwoFactor}
                    />
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-medium">Sessions</h3>
                  <Separator className="my-4" />
                  <div className="space-y-4">
                    <p className="text-sm text-text-secondary">
                      Manage your active sessions on different devices.
                    </p>
                    <Button variant="outline" size="sm">
                      Manage Sessions
                    </Button>
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="preferences" className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium">Notifications</h3>
                  <Separator className="my-4" />
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Email Notifications</p>
                      <p className="text-sm text-text-secondary">
                        Receive email notifications for important updates
                      </p>
                    </div>
                    <Switch
                      checked={userData.emailNotifications}
                      onCheckedChange={handleToggleEmailNotifications}
                    />
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-medium">Language and Region</h3>
                  <Separator className="my-4" />
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="language">Language</Label>
                      <select
                        id="language"
                        className="w-full rounded-md border border-gray-300 py-2 px-3 text-sm"
                        value={userData.language}
                        onChange={(e) => setUserData({...userData, language: e.target.value})}
                      >
                        <option value="en">English</option>
                        <option value="ms">Bahasa Malaysia</option>
                        <option value="zh">Chinese</option>
                      </select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="timezone">Timezone</Label>
                      <select
                        id="timezone"
                        className="w-full rounded-md border border-gray-300 py-2 px-3 text-sm"
                        value={userData.timezone}
                        onChange={(e) => setUserData({...userData, timezone: e.target.value})}
                      >
                        <option value="Asia/Kuala_Lumpur">Malaysia (GMT+8)</option>
                        <option value="Asia/Singapore">Singapore (GMT+8)</option>
                        <option value="Asia/Bangkok">Thailand (GMT+7)</option>
                      </select>
                    </div>
                  </div>
                </div>
                
                <div className="flex justify-end">
                  <Button onClick={() => {
                    setSuccessMessage('Preferences saved successfully');
                    setTimeout(() => {
                      setSuccessMessage(null);
                    }, 3000);
                  }}>
                    Save Preferences
                  </Button>
                </div>
              </TabsContent>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
