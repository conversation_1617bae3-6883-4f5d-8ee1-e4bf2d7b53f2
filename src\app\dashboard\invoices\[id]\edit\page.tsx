'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import InvoiceForm from '@/components/forms/InvoiceForm';
import { Spinner } from '@/components/ui/Spinner';

export default function EditInvoicePage() {
  const params = useParams();
  const invoiceId = params.id as string;
  
  const [invoice, setInvoice] = useState<any>(null);
  const [customers, setCustomers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // In a real implementation, this would fetch data from your API
        // const [invoiceResponse, customersResponse] = await Promise.all([
        //   fetch(`/api/invoices/${invoiceId}`),
        //   fetch('/api/customers')
        // ]);
        // 
        // const invoiceData = await invoiceResponse.json();
        // const customersData = await customersResponse.json();
        // 
        // setInvoice(invoiceData.invoice);
        // setCustomers(customersData.customers);
        
        // For development, use mock data
        const mockCustomers = [
          { id: '1', name: 'Acme Corporation', email: '<EMAIL>', taxId: '*********' },
          { id: '2', name: 'Wayne Enterprises', email: '<EMAIL>', taxId: '*********' },
          { id: '3', name: 'Stark Industries', email: '<EMAIL>', taxId: '*********' },
          { id: '4', name: 'Daily Planet', email: '<EMAIL>', taxId: '*********' },
          { id: '5', name: 'LexCorp', email: '<EMAIL>', taxId: '*********' },
        ];
        
        const mockInvoice = {
          id: invoiceId,
          invoiceNumber: 'INV-2025-001',
          dueDate: '2025-11-01',
          status: 'DRAFT',
          customerId: '1',
          tax: 15.00,
          notes: 'Payment due within 30 days',
          items: [
            {
              id: '1',
              description: 'Product A',
              quantity: 2,
              unitPrice: 100.00,
              amount: 200.00,
            },
            {
              id: '2',
              description: 'Service B',
              quantity: 1,
              unitPrice: 50.00,
              amount: 50.00,
            },
          ],
          lhdnValidated: false,
          lhdnValidationId: null,
        };
        
        setInvoice(mockInvoice);
        setCustomers(mockCustomers);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load invoice data. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [invoiceId]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spinner size="lg" />
        <span className="ml-2 text-text-secondary">Loading...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4">
        <p>{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-2 text-red-600 hover:text-red-800 font-medium"
        >
          Try Again
        </button>
      </div>
    );
  }

  if (!invoice) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 text-yellow-800 rounded-md p-4">
        <p>Invoice not found or you don't have permission to view it.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow-sm mb-6">
        <h1 className="text-2xl font-bold text-text-primary flex items-center">
          <svg className="w-6 h-6 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
          Edit Invoice
        </h1>
        <p className="mt-1 text-sm text-text-secondary">
          Edit invoice #{invoice.invoiceNumber}
        </p>
      </div>

      <InvoiceForm 
        invoice={invoice} 
        customers={customers} 
        isEditing={true} 
      />
    </div>
  );
}
