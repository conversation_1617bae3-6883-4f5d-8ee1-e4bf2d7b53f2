import { apiClient } from './client';
import { User } from '@/lib/auth';

// Types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  message: string;
  user: User;
  accessToken: string;
  refreshToken: string;
}

export interface RegisterRequest {
  name: string;
  email: string;
  password: string;
  tenantId?: string;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  token: string;
  password: string;
}

export interface VerifyEmailRequest {
  token: string;
}

export interface ResendVerificationRequest {
  email: string;
}

export interface TwoFactorSetupResponse {
  qrCodeUrl: string;
  setupKey: string;
}

export interface TwoFactorVerifyRequest {
  code: string;
}

export interface TwoFactorLoginRequest {
  token: string;
  code: string;
}

export interface TwoFactorRecoveryRequest {
  token: string;
  recoveryCode: string;
}

export interface InviteUserRequest {
  email: string;
  name: string;
  role: string;
}

export interface AcceptInviteRequest {
  token: string;
  name: string;
  password: string;
}

export interface UpdateUserRequest {
  name?: string;
  role?: string;
}

export interface DeactivateAccountRequest {
  password: string;
}

export interface DeleteAccountRequest {
  password: string;
}

/**
 * Authentication API Service
 * Provides methods for interacting with authentication-related API endpoints
 */
class AuthService {
  /**
   * Login with email and password
   */
  async login(data: LoginRequest): Promise<LoginResponse> {
    return apiClient.post('/auth/login', data, { skipAuth: true });
  }

  /**
   * Register a new user
   */
  async register(data: RegisterRequest): Promise<LoginResponse> {
    return apiClient.post('/auth/register', data, { skipAuth: true });
  }

  /**
   * Refresh access token
   */
  async refreshToken(refreshToken: string): Promise<{ accessToken: string }> {
    return apiClient.post('/auth/refresh-token', { refreshToken }, { skipAuth: true });
  }

  /**
   * Logout user
   */
  async logout(): Promise<{ message: string }> {
    return apiClient.post('/auth/logout', {});
  }

  /**
   * Get current user profile
   */
  async getProfile(): Promise<User> {
    return apiClient.get('/auth/profile');
  }

  /**
   * Update user profile
   */
  async updateProfile(data: { name: string }): Promise<User> {
    return apiClient.put('/auth/profile', data);
  }

  /**
   * Update user password
   */
  async updatePassword(data: { currentPassword: string; newPassword: string }): Promise<{ message: string }> {
    return apiClient.put('/auth/password', data);
  }

  /**
   * Request password reset
   */
  async forgotPassword(data: ForgotPasswordRequest): Promise<{ message: string }> {
    return apiClient.post('/auth/forgot-password', data, { skipAuth: true });
  }

  /**
   * Verify reset token
   */
  async verifyResetToken(token: string): Promise<{ valid: boolean }> {
    return apiClient.post('/auth/verify-reset-token', { token }, { skipAuth: true });
  }

  /**
   * Reset password
   */
  async resetPassword(data: ResetPasswordRequest): Promise<{ message: string }> {
    return apiClient.post('/auth/reset-password', data, { skipAuth: true });
  }

  /**
   * Verify email
   */
  async verifyEmail(data: VerifyEmailRequest): Promise<{ message: string }> {
    return apiClient.post('/auth/verify-email', data, { skipAuth: true });
  }

  /**
   * Resend verification email
   */
  async resendVerification(data: ResendVerificationRequest): Promise<{ message: string }> {
    return apiClient.post('/auth/resend-verification', data);
  }

  /**
   * Setup two-factor authentication
   */
  async setupTwoFactor(): Promise<TwoFactorSetupResponse> {
    return apiClient.post('/auth/2fa/setup', {});
  }

  /**
   * Verify two-factor setup
   */
  async verifyTwoFactorSetup(data: TwoFactorVerifyRequest): Promise<{ message: string; recoveryCodes: string[] }> {
    return apiClient.post('/auth/2fa/verify', data);
  }

  /**
   * Disable two-factor authentication
   */
  async disableTwoFactor(): Promise<{ message: string }> {
    return apiClient.post('/auth/2fa/disable', {});
  }

  /**
   * Verify two-factor during login
   */
  async verifyTwoFactorLogin(data: TwoFactorLoginRequest): Promise<LoginResponse> {
    return apiClient.post('/auth/2fa/login', data, { skipAuth: true });
  }

  /**
   * Use recovery code during login
   */
  async useRecoveryCode(data: TwoFactorRecoveryRequest): Promise<LoginResponse> {
    return apiClient.post('/auth/2fa/recovery', data, { skipAuth: true });
  }

  /**
   * Invite a user
   */
  async inviteUser(data: InviteUserRequest): Promise<{ message: string }> {
    return apiClient.post('/users/invite', data);
  }

  /**
   * Verify invite token
   */
  async verifyInvite(token: string): Promise<{ email: string; name: string; organization: string; role: string }> {
    return apiClient.get(`/invites/verify/${token}`, { skipAuth: true });
  }

  /**
   * Accept invitation
   */
  async acceptInvite(data: AcceptInviteRequest): Promise<{ message: string }> {
    return apiClient.post('/invites/accept', data, { skipAuth: true });
  }

  /**
   * Resend invitation
   */
  async resendInvitation(userId: string): Promise<{ message: string }> {
    return apiClient.post(`/users/${userId}/resend-invitation`, {});
  }

  /**
   * Get users
   */
  async getUsers(): Promise<User[]> {
    return apiClient.get('/users');
  }

  /**
   * Update user
   */
  async updateUser(userId: string, data: UpdateUserRequest): Promise<User> {
    return apiClient.put(`/users/${userId}`, data);
  }

  /**
   * Deactivate account
   */
  async deactivateAccount(data: DeactivateAccountRequest): Promise<{ message: string }> {
    return apiClient.post('/auth/deactivate', data);
  }

  /**
   * Delete account
   */
  async deleteAccount(data: DeleteAccountRequest): Promise<{ message: string }> {
    return apiClient.delete('/auth/account', { headers: {}, body: data });
  }
}

export const authService = new AuthService();
export default authService;
