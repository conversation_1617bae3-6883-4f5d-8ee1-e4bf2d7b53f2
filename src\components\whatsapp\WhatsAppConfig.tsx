'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';

export default function WhatsAppConfig() {
  const [isConnected, setIsConnected] = useState(false);
  const [provider, setProvider] = useState<'twilio' | '360dialog'>('twilio');
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  
  // Mock configuration data
  const [config, setConfig] = useState({
    twilio: {
      accountSid: '',
      authToken: '',
      phoneNumber: '',
    },
    '360dialog': {
      apiKey: '',
      phoneNumber: '',
    },
    templates: [
      {
        id: 'invoice_created',
        name: 'Invoice Created',
        status: 'approved',
        content: 'Hello {{1}}, your invoice #{{2}} for {{3}} has been created. You can view it at {{4}}.',
        variables: ['customer_name', 'invoice_number', 'amount', 'invoice_url'],
      },
      {
        id: 'payment_reminder',
        name: 'Payment Reminder',
        status: 'approved',
        content: 'Hello {{1}}, this is a reminder that invoice #{{2}} for {{3}} is due on {{4}}. Please make payment at your earliest convenience.',
        variables: ['customer_name', 'invoice_number', 'amount', 'due_date'],
      },
      {
        id: 'payment_received',
        name: 'Payment Received',
        status: 'pending',
        content: 'Hello {{1}}, we have received your payment of {{2}} for invoice #{{3}}. Thank you for your business!',
        variables: ['customer_name', 'amount', 'invoice_number'],
      },
    ],
    settings: {
      enableAutoSend: true,
      enablePaymentReminders: true,
      reminderDays: 3,
    }
  });

  const handleSaveConfig = () => {
    // In a real implementation, this would call your API
    setSuccessMessage('WhatsApp configuration saved successfully');
    
    // Clear success message after 3 seconds
    setTimeout(() => {
      setSuccessMessage(null);
    }, 3000);
  };

  const handleConnect = () => {
    // In a real implementation, this would call your API to connect to WhatsApp
    setIsConnected(true);
    setSuccessMessage('Connected to WhatsApp successfully');
    
    // Clear success message after 3 seconds
    setTimeout(() => {
      setSuccessMessage(null);
    }, 3000);
  };

  const handleDisconnect = () => {
    // In a real implementation, this would call your API to disconnect from WhatsApp
    setIsConnected(false);
    setSuccessMessage('Disconnected from WhatsApp successfully');
    
    // Clear success message after 3 seconds
    setTimeout(() => {
      setSuccessMessage(null);
    }, 3000);
  };

  const handleToggleAutoSend = () => {
    setConfig({
      ...config,
      settings: {
        ...config.settings,
        enableAutoSend: !config.settings.enableAutoSend
      }
    });
  };

  const handleTogglePaymentReminders = () => {
    setConfig({
      ...config,
      settings: {
        ...config.settings,
        enablePaymentReminders: !config.settings.enablePaymentReminders
      }
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">WhatsApp Configuration</h1>
          <p className="mt-1 text-sm text-gray-500">
            Configure your WhatsApp integration settings
          </p>
        </div>
        <div className="mt-4 md:mt-0">
          {isConnected ? (
            <Button variant="destructive" onClick={handleDisconnect}>
              Disconnect WhatsApp
            </Button>
          ) : (
            <Button onClick={handleConnect}>
              Connect WhatsApp
            </Button>
          )}
        </div>
      </div>

      {successMessage && (
        <Alert variant="success" className="bg-green-50 text-green-800 border-green-200">
          <AlertDescription>{successMessage}</AlertDescription>
        </Alert>
      )}

      {errorMessage && (
        <Alert variant="destructive">
          <AlertDescription>{errorMessage}</AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Provider Settings</CardTitle>
          <CardDescription>
            Configure your WhatsApp provider settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="twilio" onValueChange={(value) => setProvider(value as 'twilio' | '360dialog')}>
            <TabsList>
              <TabsTrigger value="twilio">Twilio</TabsTrigger>
              <TabsTrigger value="360dialog">360dialog</TabsTrigger>
            </TabsList>
            
            <TabsContent value="twilio" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="accountSid">Account SID</Label>
                <Input 
                  id="accountSid" 
                  value={config.twilio.accountSid} 
                  onChange={(e) => setConfig({
                    ...config,
                    twilio: {
                      ...config.twilio,
                      accountSid: e.target.value
                    }
                  })}
                  placeholder="Enter your Twilio Account SID"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="authToken">Auth Token</Label>
                <Input 
                  id="authToken" 
                  type="password"
                  value={config.twilio.authToken} 
                  onChange={(e) => setConfig({
                    ...config,
                    twilio: {
                      ...config.twilio,
                      authToken: e.target.value
                    }
                  })}
                  placeholder="Enter your Twilio Auth Token"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="phoneNumber">WhatsApp Phone Number</Label>
                <Input 
                  id="phoneNumber" 
                  value={config.twilio.phoneNumber} 
                  onChange={(e) => setConfig({
                    ...config,
                    twilio: {
                      ...config.twilio,
                      phoneNumber: e.target.value
                    }
                  })}
                  placeholder="Enter your WhatsApp phone number (with country code)"
                />
              </div>
            </TabsContent>
            
            <TabsContent value="360dialog" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="apiKey">API Key</Label>
                <Input 
                  id="apiKey" 
                  value={config['360dialog'].apiKey} 
                  onChange={(e) => setConfig({
                    ...config,
                    '360dialog': {
                      ...config['360dialog'],
                      apiKey: e.target.value
                    }
                  })}
                  placeholder="Enter your 360dialog API Key"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="phoneNumber360">WhatsApp Phone Number</Label>
                <Input 
                  id="phoneNumber360" 
                  value={config['360dialog'].phoneNumber} 
                  onChange={(e) => setConfig({
                    ...config,
                    '360dialog': {
                      ...config['360dialog'],
                      phoneNumber: e.target.value
                    }
                  })}
                  placeholder="Enter your WhatsApp phone number (with country code)"
                />
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
        <CardFooter>
          <Button onClick={handleSaveConfig}>Save Provider Settings</Button>
        </CardFooter>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Message Templates</CardTitle>
          <CardDescription>
            Configure your WhatsApp message templates
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {config.templates.map((template) => (
              <div key={template.id} className="p-4 border rounded-md">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-medium">{template.name}</h3>
                    <Badge 
                      variant={template.status === 'approved' ? 'default' : 'secondary'}
                      className="mt-1"
                    >
                      {template.status}
                    </Badge>
                  </div>
                  <Button variant="outline" size="sm">
                    Edit
                  </Button>
                </div>
                <Separator className="my-3" />
                <div className="text-sm text-gray-700 mt-2">
                  {template.content}
                </div>
                <div className="mt-3">
                  <h4 className="text-xs font-medium text-gray-500 mb-1">Variables:</h4>
                  <div className="flex flex-wrap gap-2">
                    {template.variables.map((variable, index) => (
                      <Badge key={index} variant="outline">
                        {variable}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            ))}
            
            <Button variant="outline" className="w-full">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Add New Template
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Notification Settings</CardTitle>
          <CardDescription>
            Configure your WhatsApp notification settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="autoSend">Automatic Notifications</Label>
                <p className="text-sm text-gray-500">
                  Automatically send WhatsApp notifications when invoices are created
                </p>
              </div>
              <Switch
                id="autoSend"
                checked={config.settings.enableAutoSend}
                onCheckedChange={handleToggleAutoSend}
              />
            </div>
            
            <Separator />
            
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="paymentReminders">Payment Reminders</Label>
                <p className="text-sm text-gray-500">
                  Send payment reminders via WhatsApp before due date
                </p>
              </div>
              <Switch
                id="paymentReminders"
                checked={config.settings.enablePaymentReminders}
                onCheckedChange={handleTogglePaymentReminders}
              />
            </div>
            
            {config.settings.enablePaymentReminders && (
              <div className="space-y-2 pl-6 border-l-2 border-gray-100">
                <Label htmlFor="reminderDays">Days Before Due Date</Label>
                <Input 
                  id="reminderDays" 
                  type="number"
                  min="1"
                  max="30"
                  value={config.settings.reminderDays.toString()} 
                  onChange={(e) => setConfig({
                    ...config,
                    settings: {
                      ...config.settings,
                      reminderDays: parseInt(e.target.value) || 0
                    }
                  })}
                />
              </div>
            )}
          </div>
        </CardContent>
        <CardFooter>
          <Button onClick={handleSaveConfig}>Save Notification Settings</Button>
        </CardFooter>
      </Card>
    </div>
  );
}
