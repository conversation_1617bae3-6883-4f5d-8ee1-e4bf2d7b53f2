import { logger } from './logger';

/**
 * Options for retry mechanism
 */
interface RetryOptions {
  /** Maximum number of retry attempts */
  maxRetries: number;
  /** Initial delay in milliseconds */
  initialDelay: number;
  /** Maximum delay in milliseconds */
  maxDelay: number;
  /** Factor to multiply delay by after each retry */
  backoffFactor: number;
  /** Function to determine if error is retryable */
  isRetryable?: (error: any) => boolean;
}

/**
 * Default retry options
 */
const defaultRetryOptions: RetryOptions = {
  maxRetries: 3,
  initialDelay: 1000, // 1 second
  maxDelay: 10000, // 10 seconds
  backoffFactor: 2,
  isRetryable: (error: any) => {
    // By default, retry on network errors and 5xx server errors
    if (!error.response) {
      // Network error
      return true;
    }

    // Retry on 5xx errors
    return error.response.status >= 500 && error.response.status < 600;
  },
};

/**
 * Execute a function with retry logic
 * @param fn Function to execute
 * @param options Retry options
 * @returns Promise with the result of the function
 */
export async function withRetry<T>(
  fn: () => Promise<T>,
  options: Partial<RetryOptions> = {}
): Promise<T> {
  const retryOptions: RetryOptions = {
    ...defaultRetryOptions,
    ...options,
  };

  let lastError: any;
  let attempt = 0;

  while (attempt <= retryOptions.maxRetries) {
    try {
      if (attempt > 0) {
        logger.info(`Retry attempt ${attempt} of ${retryOptions.maxRetries}`);
      }

      return await fn();
    } catch (error) {
      lastError = error;

      // Check if we should retry
      const isRetryable = retryOptions.isRetryable?.(error) ?? true;
      const hasAttemptsLeft = attempt < retryOptions.maxRetries;

      if (!isRetryable || !hasAttemptsLeft) {
        break;
      }

      // Calculate delay with exponential backoff
      const delay = Math.min(
        retryOptions.initialDelay * Math.pow(retryOptions.backoffFactor, attempt),
        retryOptions.maxDelay
      );

      logger.warn(`Request failed, retrying in ${delay}ms`, {
        attempt: attempt + 1,
        maxRetries: retryOptions.maxRetries,
        error: (error as any).message || String(error),
      });

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay));

      attempt++;
    }
  }

  // If we get here, all retries failed
  logger.error(`All ${retryOptions.maxRetries} retry attempts failed`, {
    error: (lastError as any).message || String(lastError),
    stack: (lastError as any).stack,
  });

  throw lastError;
}
