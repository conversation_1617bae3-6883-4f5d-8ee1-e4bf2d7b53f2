import PDFDocument from 'pdfkit';
import fs from 'fs-extra';
import path from 'path';
import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';

const prisma = new PrismaClient();

// Define the invoice data interface
interface InvoiceData {
  id: string;
  invoiceNumber: string;
  issueDate: Date;
  dueDate: Date;
  status: string;
  totalAmount: number;
  tax?: number;
  notes?: string;
  tenant: {
    id: string;
    businessName: string;
    businessAddress?: string;
    businessRegNo?: string;
    email?: string;
    phone?: string;
    logo?: string;
  };
  customer: {
    id: string;
    name: string;
    email?: string;
    phone?: string;
    address?: string;
    taxId?: string;
  };
  items: Array<{
    id: string;
    description: string;
    quantity: number;
    unitPrice: number;
    amount: number;
  }>;
  lhdnValidated?: boolean;
  lhdnValidationId?: string;
  templateId?: string;
}

// Ensure the temp directory exists
const ensureTempDir = async (): Promise<string> => {
  const tempDir = path.join(process.cwd(), 'temp');
  await fs.ensureDir(tempDir);
  return tempDir;
};

// Cache for templates
const templateCache = new Map<string, string>();

/**
 * Get a template by ID
 * @param templateId The template ID
 * @param tenantId The tenant ID
 * @returns The template content
 */
const getTemplate = async (templateId: string, tenantId: string): Promise<string | null> => {
  // Check if the template is in the cache
  const cacheKey = `${tenantId}:${templateId}`;
  if (templateCache.has(cacheKey)) {
    return templateCache.get(cacheKey) || null;
  }

  try {
    // Get the template from the database
    const template = await prisma.invoiceTemplate.findFirst({
      where: {
        id: templateId,
        tenantId,
      },
    });

    if (!template) {
      return null;
    }

    // Cache the template
    templateCache.set(cacheKey, template.template);
    return template.template;
  } catch (error) {
    logger.error('Error fetching template', { error });
    return null;
  }
};

/**
 * Generate a PDF invoice
 * @param invoice The invoice data
 * @returns The path to the generated PDF file
 */
export const generateInvoicePDF = async (invoice: InvoiceData): Promise<string> => {
  try {
    const tempDir = await ensureTempDir();
    const fileName = `invoice-${invoice.invoiceNumber.replace(/\//g, '-')}.pdf`;
    const filePath = path.join(tempDir, fileName);

    // Create a new PDF document
    const doc = new PDFDocument({ margin: 50 });

    // Pipe the PDF to a file
    const stream = fs.createWriteStream(filePath);
    doc.pipe(stream);

    // Check if a custom template should be used
    if (invoice.templateId) {
      const template = await getTemplate(invoice.templateId, invoice.tenant.id);
      if (template) {
        // Use the custom template
        await generateFromTemplate(doc, invoice, template);
      } else {
        // Fall back to the default template
        await generateDefaultTemplate(doc, invoice);
      }
    } else {
      // Use the default template
      await generateDefaultTemplate(doc, invoice);
    }

    // Finalize the PDF
    doc.end();

    // Return a promise that resolves when the PDF is written
    return new Promise((resolve, reject) => {
      stream.on('finish', () => {
        logger.info('PDF invoice generated successfully', { invoiceId: invoice.id, path: filePath });
        resolve(filePath);
      });
      stream.on('error', (error) => {
        logger.error('Error generating PDF invoice', { invoiceId: invoice.id, error });
        reject(error);
      });
    });
  } catch (error: any) {
    logger.error('Failed to generate invoice PDF', {
      invoiceId: invoice.id,
      error: error.message,
      stack: error.stack
    });
    throw new Error(`Failed to generate invoice PDF: ${error.message}`);
  }
};

/**
 * Generate a PDF from a custom template
 * @param doc The PDF document
 * @param invoice The invoice data
 * @param template The template content
 */
const generateFromTemplate = async (doc: PDFKit.PDFDocument, invoice: InvoiceData, template: string): Promise<void> => {
  try {
    // Parse the template
    const templateObj = JSON.parse(template);

    // Add company logo if available
    if (invoice.tenant.logo && templateObj.showLogo !== false) {
      try {
        const logoPath = path.join(process.cwd(), 'uploads', 'logos', invoice.tenant.logo);
        if (await fs.pathExists(logoPath)) {
          const logoX = templateObj.logo?.x || 50;
          const logoY = templateObj.logo?.y || 45;
          const logoWidth = templateObj.logo?.width || 150;
          doc.image(logoPath, logoX, logoY, { width: logoWidth });
        }
      } catch (error) {
        logger.warn('Failed to add logo to invoice', { error });
      }
    }

    // Add header
    if (templateObj.header) {
      if (templateObj.header.title) {
        doc.fontSize(templateObj.header.title.fontSize || 20)
           .text(templateObj.header.title.text || 'INVOICE', { align: templateObj.header.title.align || 'right' });
      }

      if (templateObj.header.invoiceNumber) {
        doc.fontSize(templateObj.header.invoiceNumber.fontSize || 10)
           .text(`Invoice #: ${invoice.invoiceNumber}`, { align: templateObj.header.invoiceNumber.align || 'right' });
      }

      if (templateObj.header.issueDate) {
        doc.text(`Issue Date: ${new Date(invoice.issueDate).toLocaleDateString()}`, { align: templateObj.header.issueDate.align || 'right' });
      }

      if (templateObj.header.dueDate) {
        doc.text(`Due Date: ${new Date(invoice.dueDate).toLocaleDateString()}`, { align: templateObj.header.dueDate.align || 'right' });
      }
    }

    doc.moveDown();

    // Add company information
    if (templateObj.company) {
      doc.fontSize(templateObj.company.fontSize || 12).text(invoice.tenant.businessName);
      
      if (invoice.tenant.businessAddress) {
        doc.fontSize(templateObj.company.addressFontSize || 10).text(invoice.tenant.businessAddress);
      }
      
      if (invoice.tenant.businessRegNo) {
        doc.text(`Business Reg. No: ${invoice.tenant.businessRegNo}`);
      }
      
      if (invoice.tenant.email) {
        doc.text(`Email: ${invoice.tenant.email}`);
      }
      
      if (invoice.tenant.phone) {
        doc.text(`Phone: ${invoice.tenant.phone}`);
      }
    }

    doc.moveDown();

    // Add customer information
    if (templateObj.customer) {
      doc.fontSize(templateObj.customer.fontSize || 12).text(templateObj.customer.title || 'Bill To:');
      doc.fontSize(templateObj.customer.detailsFontSize || 10).text(invoice.customer.name);
      
      if (invoice.customer.address) {
        doc.text(invoice.customer.address);
      }
      
      if (invoice.customer.taxId) {
        doc.text(`Tax ID: ${invoice.customer.taxId}`);
      }
      
      if (invoice.customer.email) {
        doc.text(`Email: ${invoice.customer.email}`);
      }
      
      if (invoice.customer.phone) {
        doc.text(`Phone: ${invoice.customer.phone}`);
      }
    }

    doc.moveDown(2);

    // Add invoice items table
    if (templateObj.items) {
      const tableTop = doc.y;
      const tableHeaders = templateObj.items.headers || ['Item', 'Description', 'Qty', 'Unit Price', 'Amount'];

      // Draw table headers
      let position = 0;
      doc.fontSize(templateObj.items.fontSize || 10);

      // Draw headers background
      if (templateObj.items.headerBackground !== false) {
        doc.fillColor(templateObj.items.headerBackgroundColor || '#f0f0f0')
           .rect(50, tableTop, doc.page.width - 100, 20)
           .fill();
      }

      // Draw headers text
      doc.fillColor(templateObj.items.headerTextColor || '#000000');
      
      const colWidths = templateObj.items.columnWidths || [150, 150, 50, 70, 70];
      const colPositions = [50];
      for (let i = 1; i < colWidths.length; i++) {
        colPositions[i] = colPositions[i-1] + colWidths[i-1];
      }
      
      for (let i = 0; i < tableHeaders.length; i++) {
        const align = i >= 2 ? 'right' : 'left'; // Right-align numeric columns
        doc.text(tableHeaders[i], colPositions[i], tableTop + 5, { 
          width: colWidths[i], 
          align: templateObj.items.headerAlign?.[i] || align 
        });
      }

      // Draw items
      position = tableTop + 25;

      // Draw table rows
      for (let i = 0; i < invoice.items.length; i++) {
        const item = invoice.items[i];

        // Check if we need a new page
        if (position > doc.page.height - 100) {
          doc.addPage();
          position = 50;

          // Redraw headers on new page
          if (templateObj.items.headerBackground !== false) {
            doc.fillColor(templateObj.items.headerBackgroundColor || '#f0f0f0')
               .rect(50, position, doc.page.width - 100, 20)
               .fill();
          }

          doc.fillColor(templateObj.items.headerTextColor || '#000000');
          for (let i = 0; i < tableHeaders.length; i++) {
            const align = i >= 2 ? 'right' : 'left';
            doc.text(tableHeaders[i], colPositions[i], position + 5, { 
              width: colWidths[i], 
              align: templateObj.items.headerAlign?.[i] || align 
            });
          }

          position += 25;
        }

        // Draw row
        doc.fillColor(templateObj.items.rowTextColor || '#000000');
        doc.text(`Item ${i + 1}`, colPositions[0], position, { width: colWidths[0] });
        doc.text(item.description, colPositions[1], position, { width: colWidths[1] });
        doc.text(item.quantity.toString(), colPositions[2], position, { width: colWidths[2], align: 'right' });
        doc.text(item.unitPrice.toFixed(2), colPositions[3], position, { width: colWidths[3], align: 'right' });
        doc.text(item.amount.toFixed(2), colPositions[4], position, { width: colWidths[4], align: 'right' });

        position += 20;
      }

      // Draw table bottom line
      if (templateObj.items.bottomLine !== false) {
        doc.moveTo(50, position).lineTo(doc.page.width - 50, position).stroke();
      }

      // Add totals
      const subtotal = invoice.items.reduce((sum, item) => sum + item.amount, 0);
      const tax = invoice.tax || 0;
      const total = invoice.totalAmount;

      if (templateObj.totals) {
        position += 10;
        doc.text('Subtotal:', colPositions[3], position, { width: colWidths[3], align: 'right' });
        doc.text(subtotal.toFixed(2), colPositions[4], position, { width: colWidths[4], align: 'right' });

        position += 20;
        doc.text('Tax:', colPositions[3], position, { width: colWidths[3], align: 'right' });
        doc.text(tax.toFixed(2), colPositions[4], position, { width: colWidths[4], align: 'right' });

        position += 20;
        doc.fontSize(templateObj.totals.totalFontSize || 12)
           .text('Total:', colPositions[3], position, { width: colWidths[3], align: 'right' });
        doc.fontSize(templateObj.totals.totalFontSize || 12)
           .text(total.toFixed(2), colPositions[4], position, { width: colWidths[4], align: 'right' });
      }
    }

    // Add notes if available
    if (invoice.notes && templateObj.notes !== false) {
      position = doc.y + 40;
      doc.fontSize(templateObj.notes?.fontSize || 10).text('Notes:', 50, position);
      position += 15;
      doc.text(invoice.notes, 50, position, { width: doc.page.width - 100 });
    }

    // Add LHDN validation information if available
    if (invoice.lhdnValidated && invoice.lhdnValidationId && templateObj.lhdn !== false) {
      position = doc.y + 40;
      doc.fontSize(templateObj.lhdn?.fontSize || 10).text('LHDN Validation:', 50, position);
      position += 15;
      doc.text(`This invoice has been validated with LHDN MyInvois (ID: ${invoice.lhdnValidationId})`, 50, position, { width: doc.page.width - 100 });
    }

    // Add footer
    if (templateObj.footer !== false) {
      const pageCount = doc.bufferedPageRange().count;
      for (let i = 0; i < pageCount; i++) {
        doc.switchToPage(i);

        // Add page number
        doc.fontSize(templateObj.footer?.fontSize || 8).text(
          `Page ${i + 1} of ${pageCount}`,
          50,
          doc.page.height - 50,
          { align: 'center', width: doc.page.width - 100 }
        );

        // Add footer text
        doc.fontSize(templateObj.footer?.fontSize || 8).text(
          templateObj.footer?.text || `Generated by Invoix - ${new Date().toLocaleString()}`,
          50,
          doc.page.height - 35,
          { align: 'center', width: doc.page.width - 100 }
        );
      }
    }
  } catch (error) {
    logger.error('Error generating PDF from template', { error });
    // Fall back to the default template
    await generateDefaultTemplate(doc, invoice);
  }
};

/**
 * Generate a PDF using the default template
 * @param doc The PDF document
 * @param invoice The invoice data
 */
const generateDefaultTemplate = async (doc: PDFKit.PDFDocument, invoice: InvoiceData): Promise<void> => {
  // Add company logo if available
  if (invoice.tenant.logo) {
    try {
      const logoPath = path.join(process.cwd(), 'uploads', 'logos', invoice.tenant.logo);
      if (await fs.pathExists(logoPath)) {
        doc.image(logoPath, 50, 45, { width: 150 });
      }
    } catch (error) {
      logger.warn('Failed to add logo to invoice', { error });
    }
  }

  // Add invoice header
  doc.fontSize(20).text('INVOICE', { align: 'right' });
  doc.fontSize(10).text(`Invoice #: ${invoice.invoiceNumber}`, { align: 'right' });
  doc.text(`Issue Date: ${new Date(invoice.issueDate).toLocaleDateString()}`, { align: 'right' });
  doc.text(`Due Date: ${new Date(invoice.dueDate).toLocaleDateString()}`, { align: 'right' });
  doc.moveDown();

  // Add company information
  doc.fontSize(12).text(invoice.tenant.businessName);
  if (invoice.tenant.businessAddress) {
    doc.fontSize(10).text(invoice.tenant.businessAddress);
  }
  if (invoice.tenant.businessRegNo) {
    doc.text(`Business Reg. No: ${invoice.tenant.businessRegNo}`);
  }
  if (invoice.tenant.email) {
    doc.text(`Email: ${invoice.tenant.email}`);
  }
  if (invoice.tenant.phone) {
    doc.text(`Phone: ${invoice.tenant.phone}`);
  }
  doc.moveDown();

  // Add customer information
  doc.fontSize(12).text('Bill To:');
  doc.fontSize(10).text(invoice.customer.name);
  if (invoice.customer.address) {
    doc.text(invoice.customer.address);
  }
  if (invoice.customer.taxId) {
    doc.text(`Tax ID: ${invoice.customer.taxId}`);
  }
  if (invoice.customer.email) {
    doc.text(`Email: ${invoice.customer.email}`);
  }
  if (invoice.customer.phone) {
    doc.text(`Phone: ${invoice.customer.phone}`);
  }
  doc.moveDown(2);

  // Add invoice items table
  const tableTop = doc.y;
  const tableHeaders = ['Item', 'Description', 'Qty', 'Unit Price', 'Amount'];

  // Draw table headers
  let position = 0;
  doc.fontSize(10);

  // Draw headers background
  doc.fillColor('#f0f0f0')
     .rect(50, tableTop, doc.page.width - 100, 20)
     .fill();

  // Draw headers text
  doc.fillColor('#000000');
  doc.text(tableHeaders[0], 50, tableTop + 5, { width: 150 });
  doc.text(tableHeaders[1], 200, tableTop + 5, { width: 150 });
  doc.text(tableHeaders[2], 350, tableTop + 5, { width: 50, align: 'right' });
  doc.text(tableHeaders[3], 400, tableTop + 5, { width: 70, align: 'right' });
  doc.text(tableHeaders[4], 470, tableTop + 5, { width: 70, align: 'right' });

  // Draw items
  position = tableTop + 25;

  // Draw table rows
  for (let i = 0; i < invoice.items.length; i++) {
    const item = invoice.items[i];

    // Check if we need a new page
    if (position > doc.page.height - 100) {
      doc.addPage();
      position = 50;

      // Redraw headers on new page
      doc.fillColor('#f0f0f0')
         .rect(50, position, doc.page.width - 100, 20)
         .fill();

      doc.fillColor('#000000');
      doc.text(tableHeaders[0], 50, position + 5, { width: 150 });
      doc.text(tableHeaders[1], 200, position + 5, { width: 150 });
      doc.text(tableHeaders[2], 350, position + 5, { width: 50, align: 'right' });
      doc.text(tableHeaders[3], 400, position + 5, { width: 70, align: 'right' });
      doc.text(tableHeaders[4], 470, position + 5, { width: 70, align: 'right' });

      position += 25;
    }

    // Draw row
    doc.text(`Item ${i + 1}`, 50, position, { width: 150 });
    doc.text(item.description, 200, position, { width: 150 });
    doc.text(item.quantity.toString(), 350, position, { width: 50, align: 'right' });
    doc.text(item.unitPrice.toFixed(2), 400, position, { width: 70, align: 'right' });
    doc.text(item.amount.toFixed(2), 470, position, { width: 70, align: 'right' });

    position += 20;
  }

  // Draw table bottom line
  doc.moveTo(50, position).lineTo(doc.page.width - 50, position).stroke();

  // Add totals
  const subtotal = invoice.items.reduce((sum, item) => sum + item.amount, 0);
  const tax = invoice.tax || 0;
  const total = invoice.totalAmount;

  position += 10;
  doc.text('Subtotal:', 350, position, { width: 100, align: 'right' });
  doc.text(subtotal.toFixed(2), 470, position, { width: 70, align: 'right' });

  position += 20;
  doc.text('Tax:', 350, position, { width: 100, align: 'right' });
  doc.text(tax.toFixed(2), 470, position, { width: 70, align: 'right' });

  position += 20;
  doc.fontSize(12).text('Total:', 350, position, { width: 100, align: 'right' });
  doc.fontSize(12).text(total.toFixed(2), 470, position, { width: 70, align: 'right' });

  // Add notes if available
  if (invoice.notes) {
    position += 40;
    doc.fontSize(10).text('Notes:', 50, position);
    position += 15;
    doc.text(invoice.notes, 50, position, { width: doc.page.width - 100 });
  }

  // Add LHDN validation information if available
  if (invoice.lhdnValidated && invoice.lhdnValidationId) {
    position += 40;
    doc.fontSize(10).text('LHDN Validation:', 50, position);
    position += 15;
    doc.text(`This invoice has been validated with LHDN MyInvois (ID: ${invoice.lhdnValidationId})`, 50, position, { width: doc.page.width - 100 });
  }

  // Add footer
  const pageCount = doc.bufferedPageRange().count;
  for (let i = 0; i < pageCount; i++) {
    doc.switchToPage(i);

    // Add page number
    doc.fontSize(8).text(
      `Page ${i + 1} of ${pageCount}`,
      50,
      doc.page.height - 50,
      { align: 'center', width: doc.page.width - 100 }
    );

    // Add footer text
    doc.fontSize(8).text(
      `Generated by Invoix - ${new Date().toLocaleString()}`,
      50,
      doc.page.height - 35,
      { align: 'center', width: doc.page.width - 100 }
    );
  }
};
