'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Spinner } from '@/components/ui/spinner';
import { Badge } from '@/components/ui/badge';

interface Certificate {
  id: string;
  name: string;
  type: string;
  issuedTo: string;
  issuedBy: string;
  validFrom: string;
  validTo: string;
  status: 'active' | 'expired' | 'revoked';
}

export default function CertificatesPage() {
  const [certificates, setCertificates] = useState<Certificate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const [uploadForm, setUploadForm] = useState({
    certificateName: '',
    certificateFile: null as File | null,
    certificatePassword: '',
  });

  useEffect(() => {
    const fetchCertificates = async () => {
      try {
        // In a real implementation, this would fetch certificates from your API
        // const response = await fetch('/api/admin/certificates');
        // const data = await response.json();
        // setCertificates(data.certificates);

        // For development, use mock data
        const mockCertificates: Certificate[] = [
          {
            id: '1',
            name: 'LHDN MyInvois Production',
            type: 'PKCS#12',
            issuedTo: 'Invoix Sdn Bhd',
            issuedBy: 'LHDN Certificate Authority',
            validFrom: '2025-01-01',
            validTo: '2026-01-01',
            status: 'active',
          },
          {
            id: '2',
            name: 'LHDN MyInvois Sandbox',
            type: 'PKCS#12',
            issuedTo: 'Invoix Sdn Bhd',
            issuedBy: 'LHDN Certificate Authority',
            validFrom: '2024-06-01',
            validTo: '2025-06-01',
            status: 'active',
          },
          {
            id: '3',
            name: 'Old Certificate',
            type: 'PKCS#12',
            issuedTo: 'Invoix Sdn Bhd',
            issuedBy: 'LHDN Certificate Authority',
            validFrom: '2023-01-01',
            validTo: '2024-01-01',
            status: 'expired',
          },
        ];

        setCertificates(mockCertificates);
      } catch (err) {
        console.error('Error fetching certificates:', err);
        setError('Failed to load certificates. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchCertificates();
  }, []);

  const handleUploadFormChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, files } = e.target;

    if (name === 'certificateFile' && files && files.length > 0) {
      setUploadForm(prev => ({
        ...prev,
        certificateFile: files[0],
      }));
    } else {
      setUploadForm(prev => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  const handleUploadCertificate = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!uploadForm.certificateName || !uploadForm.certificateFile) {
      setError('Please provide a certificate name and file');
      return;
    }

    setIsUploading(true);
    setError(null);
    setSuccessMessage(null);

    try {
      // In a real implementation, this would upload the certificate to your API
      // const formData = new FormData();
      // formData.append('name', uploadForm.certificateName);
      // formData.append('file', uploadForm.certificateFile);
      // formData.append('password', uploadForm.certificatePassword);
      //
      // const response = await fetch('/api/admin/certificates', {
      //   method: 'POST',
      //   body: formData,
      // });
      //
      // if (!response.ok) {
      //   const errorData = await response.json();
      //   throw new Error(errorData.message || 'Failed to upload certificate');
      // }
      //
      // const data = await response.json();

      // For development, simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Add mock certificate to the list
      const newCertificate: Certificate = {
        id: `${certificates.length + 1}`,
        name: uploadForm.certificateName,
        type: 'PKCS#12',
        issuedTo: 'Invoix Sdn Bhd',
        issuedBy: 'LHDN Certificate Authority',
        validFrom: new Date().toISOString().split('T')[0],
        validTo: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        status: 'active',
      };

      setCertificates(prev => [...prev, newCertificate]);

      // Reset form
      setUploadForm({
        certificateName: '',
        certificateFile: null,
        certificatePassword: '',
      });

      setSuccessMessage('Certificate uploaded successfully');
    } catch (err: any) {
      console.error('Error uploading certificate:', err);
      setError(err.message || 'Failed to upload certificate');
    } finally {
      setIsUploading(false);
    }
  };

  const handleRevokeCertificate = async (certificateId: string) => {
    if (!confirm('Are you sure you want to revoke this certificate? This action cannot be undone.')) {
      return;
    }

    try {
      // In a real implementation, this would revoke the certificate via your API
      // await fetch(`/api/admin/certificates/${certificateId}/revoke`, {
      //   method: 'POST',
      // });

      // For development, update the mock data
      setCertificates(prev =>
        prev.map(cert =>
          cert.id === certificateId
            ? { ...cert, status: 'revoked' as const }
            : cert
        )
      );

      setSuccessMessage('Certificate revoked successfully');
    } catch (err: any) {
      console.error('Error revoking certificate:', err);
      setError(err.message || 'Failed to revoke certificate');
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spinner size="lg" />
        <span className="ml-2 text-text-secondary">Loading...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow-sm mb-6">
        <h1 className="text-2xl font-bold text-text-primary flex items-center">
          <svg className="w-6 h-6 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
          LHDN Certificate Management <Badge variant="secondary" className="ml-2">Admin Only</Badge>
        </h1>
        <p className="mt-1 text-sm text-text-secondary">
          Manage platform-level digital certificates for LHDN MyInvois integration
        </p>
        <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
          <p className="text-sm text-blue-800">
            <strong>Note:</strong> This certificate is used by the Invoix platform to authenticate with the LHDN MyInvois API.
            Your customers do not need to obtain or upload their own certificates.
          </p>
        </div>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {successMessage && (
        <Alert variant="success" className="mb-4">
          <AlertTitle>Success</AlertTitle>
          <AlertDescription>{successMessage}</AlertDescription>
        </Alert>
      )}

      {/* Upload Certificate */}
      <Card className="border-none shadow-md">
        <CardHeader className="pb-2 border-b">
          <CardTitle className="text-lg font-bold text-text-primary">Upload New Certificate</CardTitle>
        </CardHeader>
        <form onSubmit={handleUploadCertificate}>
          <CardContent className="space-y-4">
            <div className="p-4 bg-amber-50 border border-amber-200 rounded-md mb-4">
              <h3 className="text-sm font-medium text-amber-800 mb-2">Platform Certificate Information</h3>
              <p className="text-sm text-amber-700 mb-2">
                This certificate will be used by the Invoix platform to:
              </p>
              <ul className="list-disc pl-5 text-sm text-amber-700 space-y-1">
                <li>Authenticate with the LHDN MyInvois API</li>
                <li>Digitally sign invoices submitted on behalf of your customers</li>
                <li>Secure the communication between Invoix and LHDN</li>
              </ul>
            </div>

            <Input
              label="Certificate Name"
              name="certificateName"
              value={uploadForm.certificateName}
              onChange={handleUploadFormChange}
              placeholder="e.g., LHDN MyInvois Production"
              required
            />

            <div>
              <label className="block text-sm font-medium text-text-primary mb-1">
                Certificate File (.p12)
              </label>
              <input
                type="file"
                name="certificateFile"
                accept=".p12,.pfx"
                onChange={handleUploadFormChange}
                className="block w-full text-sm text-text-secondary file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100"
                required
              />
              <p className="mt-1 text-xs text-text-secondary">
                Upload a PKCS#12 (.p12) certificate file obtained from an approved Malaysian Certificate Authority
              </p>
            </div>

            <Input
              label="Certificate Password"
              type="password"
              name="certificatePassword"
              value={uploadForm.certificatePassword}
              onChange={handleUploadFormChange}
              placeholder="Enter the certificate password"
              required
            />
          </CardContent>
          <CardFooter>
            <Button
              type="submit"
              disabled={isUploading}
            >
              {isUploading ? (
                <>
                  <Spinner size="sm" className="mr-2" />
                  Uploading...
                </>
              ) : (
                'Upload Certificate'
              )}
            </Button>
          </CardFooter>
        </form>
      </Card>

      {/* Certificates List */}
      <Card className="border-none shadow-md">
        <CardHeader className="pb-2 border-b">
          <CardTitle className="text-lg font-bold text-text-primary">Installed Certificates</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm text-left" className="min-w-full divide-y divide-gray-200">
              <thead>
                <tr className="bg-white hover:bg-gray-50 transition-colors">
                  <th className="px-3 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">Name</th>
                  <th className="px-3 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">Type</th>
                  <th className="px-3 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">Issued To</th>
                  <th className="px-3 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">Valid From</th>
                  <th className="px-3 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">Valid To</th>
                  <th className="px-3 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">Status</th>
                  <th className="px-3 py-3 text-right text-xs font-medium text-text-secondary uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {certificates.map((cert) => (
                  <tr key={cert.id} className="bg-white  transition-colors">
                    <td className="px-3 py-4 font-medium text-text-primary">{cert.name}</td>
                    <td className="px-3 py-4 text-text-secondary">{cert.type}</td>
                    <td className="px-3 py-4 text-text-secondary">{cert.issuedTo}</td>
                    <td className="px-3 py-4 text-text-secondary">{cert.validFrom}</td>
                    <td className="px-3 py-4 text-text-secondary">{cert.validTo}</td>
                    <td className="px-3 py-4">
                      <Badge
                        variant={
                          cert.status === 'active' ? 'success' :
                          cert.status === 'expired' ? 'destructive' :
                          'outline'
                        }
                      >
                        {cert.status === 'active' ? 'Active' :
                         cert.status === 'expired' ? 'Expired' :
                         'Revoked'}
                      </Badge>
                    </td>
                    <td className="px-3 py-4 text-right">
                      {cert.status === 'active' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleRevokeCertificate(cert.id)}
                          className="text-red-600 hover:text-red-800 hover:bg-red-50"
                        >
                          Revoke
                        </Button>
                      )}
                    </td>
                  </tr>
                ))}

                {certificates.length === 0 && (
                  <tr className="bg-white hover:bg-gray-50 transition-colors">
                    <td colSpan={7} className="px-3 py-8 text-center text-text-secondary">
                      No certificates found. Upload a certificate to get started.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Certificate Information */}
      <Card className="border-none shadow-md">
        <CardHeader className="pb-2 border-b">
          <CardTitle className="text-lg font-bold text-text-primary">About LHDN Digital Certificates</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-text-secondary space-y-4">
            <p>
              <strong>For SaaS Platform Administrators Only:</strong> Digital certificates are required for secure communication with the LHDN MyInvois API.
              These certificates authenticate your SaaS platform and encrypt the data transmitted between your system and LHDN.
            </p>

            <div className="bg-green-50 border border-green-200 rounded-md p-4 my-4">
              <h3 className="text-sm font-medium text-green-800 mb-2">Multi-Tenant SaaS Architecture</h3>
              <p className="text-sm text-green-700 mb-2">
                In your multi-tenant SaaS model:
              </p>
              <ul className="list-disc pl-5 text-sm text-green-700 space-y-1">
                <li><strong>Your platform</strong> maintains a single integration point with LHDN MyInvois API</li>
                <li><strong>Your customers</strong> do NOT need to obtain their own certificates</li>
                <li>Each invoice includes the specific customer's business details (TIN)</li>
                <li>All customers benefit from your pre-built LHDN compliance features</li>
              </ul>
            </div>

            <p>
              Important information about certificates:
            </p>
            <ul className="list-disc pl-5 space-y-2">
              <li>Certificates must be in PKCS#12 (.p12) format</li>
              <li>Certificates are typically valid for 1-2 years</li>
              <li>You should renew your certificate before it expires</li>
              <li>Keep your certificate password secure</li>
              <li>Revoke certificates that are no longer needed</li>
            </ul>

            <h3 className="font-medium text-gray-800 mt-4 mb-2">How to Obtain a Certificate</h3>
            <p>
              To obtain a certificate for LHDN MyInvois integration:
            </p>
            <ol className="list-decimal pl-5 space-y-2">
              <li>Apply for a digital certificate from an approved Malaysian Certificate Authority (CA) such as DigiCert, Pos Digicert, MSC Trustgate, or Telekom Applied Business</li>
              <li>Complete verification of your business identity (submit business registration documents)</li>
              <li>Receive the certificate in .p12 format (or convert it using tools provided by the CA)</li>
              <li>Upload the certificate here and provide the certificate password</li>
            </ol>

            <p className="mt-4">
              For more information, contact the LHDN support team or visit their developer portal at <a href="https://sdk.myinvois.hasil.gov.my/einvoicingapi/" target="_blank" rel="noopener noreferrer" className="text-indigo-600 hover:text-indigo-800">https://sdk.myinvois.hasil.gov.my/einvoicingapi/</a>.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
