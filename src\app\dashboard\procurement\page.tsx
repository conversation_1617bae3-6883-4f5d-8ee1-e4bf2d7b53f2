'use client';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { PlusCircle } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useRouter } from 'next/navigation';

// Mock data for demonstration
const mockSuppliers = [
  {
    id: '1',
    name: 'ABC Supplies Sdn Bhd',
    contactPerson: '<PERSON>',
    email: '<EMAIL>',
    phone: '+60123456789',
    taxId: '12345678',
    paymentTerms: 30,
    isActive: true,
  },
  {
    id: '2',
    name: 'XYZ Electronics',
    contactPerson: '<PERSON>',
    email: '<EMAIL>',
    phone: '+60198765432',
    taxId: '87654321',
    paymentTerms: 15,
    isActive: true,
  },
];

const mockPurchaseOrders = [
  {
    id: '1',
    poNumber: 'PO-2023-001',
    supplierName: 'ABC Supplies Sdn Bhd',
    orderDate: '2023-05-15',
    expectedDelivery: '2023-05-30',
    totalAmount: 5000.00,
    status: 'SENT',
  },
  {
    id: '2',
    poNumber: 'PO-2023-002',
    supplierName: 'XYZ Electronics',
    orderDate: '2023-05-20',
    expectedDelivery: '2023-06-05',
    totalAmount: 8500.00,
    status: 'DRAFT',
  },
  {
    id: '3',
    poNumber: 'PO-2023-003',
    supplierName: 'ABC Supplies Sdn Bhd',
    orderDate: '2023-05-10',
    expectedDelivery: '2023-05-25',
    totalAmount: 3200.00,
    status: 'RECEIVED',
  },
];

const mockGoodsReceipts = [
  {
    id: '1',
    receiptNumber: 'GR-2023-001',
    poNumber: 'PO-2023-003',
    supplierName: 'ABC Supplies Sdn Bhd',
    receiveDate: '2023-05-25',
    status: 'Completed',
  },
];

// Column definitions for tables
const supplierColumns: ColumnDef<any>[] = [
  {
    accessorKey: 'name',
    header: 'Name',
  },
  {
    accessorKey: 'contactPerson',
    header: 'Contact Person',
  },
  {
    accessorKey: 'email',
    header: 'Email',
  },
  {
    accessorKey: 'phone',
    header: 'Phone',
  },
  {
    accessorKey: 'paymentTerms',
    header: 'Payment Terms',
    cell: ({ row }) => {
      return `${row.getValue('paymentTerms')} days`;
    },
  },
  {
    accessorKey: 'isActive',
    header: 'Status',
    cell: ({ row }) => {
      return row.getValue('isActive') ? (
        <Badge className="bg-green-500">Active</Badge>
      ) : (
        <Badge variant="outline">Inactive</Badge>
      );
    },
  },
  {
    id: 'actions',
    cell: ({ row }) => {
      return (
        <div className="flex items-center justify-end gap-2">
          <Button variant="ghost" size="sm">
            Edit
          </Button>
          <Button variant="ghost" size="sm">
            View Orders
          </Button>
        </div>
      );
    },
  },
];

const purchaseOrderColumns: ColumnDef<any>[] = [
  {
    accessorKey: 'poNumber',
    header: 'PO Number',
  },
  {
    accessorKey: 'supplierName',
    header: 'Supplier',
  },
  {
    accessorKey: 'orderDate',
    header: 'Order Date',
    cell: ({ row }) => {
      return new Date(row.getValue('orderDate')).toLocaleDateString();
    },
  },
  {
    accessorKey: 'expectedDelivery',
    header: 'Expected Delivery',
    cell: ({ row }) => {
      return new Date(row.getValue('expectedDelivery')).toLocaleDateString();
    },
  },
  {
    accessorKey: 'totalAmount',
    header: 'Total Amount',
    cell: ({ row }) => {
      return `RM ${row.getValue('totalAmount').toFixed(2)}`;
    },
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.getValue('status');
      return status === 'DRAFT' ? (
        <Badge variant="outline">Draft</Badge>
      ) : status === 'SENT' ? (
        <Badge className="bg-blue-500">Sent</Badge>
      ) : status === 'PARTIALLY_RECEIVED' ? (
        <Badge className="bg-yellow-500">Partially Received</Badge>
      ) : status === 'RECEIVED' ? (
        <Badge className="bg-green-500">Received</Badge>
      ) : (
        <Badge variant="destructive">Cancelled</Badge>
      );
    },
  },
  {
    id: 'actions',
    cell: ({ row }) => {
      const status = row.getValue('status');
      return (
        <div className="flex items-center justify-end gap-2">
          <Button variant="ghost" size="sm">
            View
          </Button>
          {status === 'DRAFT' && (
            <Button variant="ghost" size="sm">
              Edit
            </Button>
          )}
          {status === 'SENT' && (
            <Button variant="ghost" size="sm" className="text-green-500">
              Receive
            </Button>
          )}
        </div>
      );
    },
  },
];

const goodsReceiptColumns: ColumnDef<any>[] = [
  {
    accessorKey: 'receiptNumber',
    header: 'Receipt Number',
  },
  {
    accessorKey: 'poNumber',
    header: 'PO Number',
  },
  {
    accessorKey: 'supplierName',
    header: 'Supplier',
  },
  {
    accessorKey: 'receiveDate',
    header: 'Receive Date',
    cell: ({ row }) => {
      return new Date(row.getValue('receiveDate')).toLocaleDateString();
    },
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      return <Badge className="bg-green-500">{row.getValue('status')}</Badge>;
    },
  },
  {
    id: 'actions',
    cell: ({ row }) => {
      return (
        <Button variant="ghost" size="sm">
          View
        </Button>
      );
    },
  },
];

export default function ProcurementPage() {
  const { toast } = useToast();
  const router = useRouter();

  return (
    <div className="space-y-4">
      {/* Page header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between bg-white p-3 md:p-4 rounded-lg shadow-sm mb-3">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <svg className="w-6 h-6 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            Procurement
          </h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage purchase orders, goods receipts, and suppliers
          </p>
        </div>
        <div className="mt-4 md:mt-0 flex space-x-3">
          <Button variant="outline" size="sm">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
            </svg>
            Filter
          </Button>
          <Button size="sm" className="bg-indigo-600 hover:bg-indigo-700">
            <PlusCircle className="mr-2 h-4 w-4" /> Create Purchase Order
          </Button>
        </div>
      </div>


      {/* Purchase Orders Table */}
      <Card className="border-none shadow-md">
        <CardHeader className="pb-2 border-b">
          <CardTitle className="text-lg font-bold">Purchase Orders</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full text-sm text-left">
              <thead className="text-xs font-medium text-gray-500 bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-3 py-2">PO Number</th>
                  <th className="px-3 py-2">Supplier</th>
                  <th className="px-3 py-2">Order Date</th>
                  <th className="px-3 py-2">Expected Delivery</th>
                  <th className="px-3 py-2">Total Amount</th>
                  <th className="px-3 py-2">Status</th>
                  <th className="px-3 py-2 text-right">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {mockPurchaseOrders.map((order) => (
                  <tr key={order.id} className="bg-white hover:bg-gray-50 transition-colors">
                    <td className="px-3 py-2 font-medium text-indigo-600">
                      {order.poNumber}
                    </td>
                    <td className="px-3 py-2">
                      {order.supplierName}
                    </td>
                    <td className="px-3 py-2 text-gray-500">
                      {new Date(order.orderDate).toLocaleDateString()}
                    </td>
                    <td className="px-3 py-2 text-gray-500">
                      {new Date(order.expectedDelivery).toLocaleDateString()}
                    </td>
                    <td className="px-3 py-2 font-medium">
                      RM {order.totalAmount.toFixed(2)}
                    </td>
                    <td className="px-3 py-2">
                      <span className={`inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium ${
                        order.status === 'DRAFT' ? 'bg-gray-100 text-gray-800' :
                        order.status === 'SENT' ? 'bg-blue-100 text-blue-800' :
                        order.status === 'PARTIALLY_RECEIVED' ? 'bg-yellow-100 text-yellow-800' :
                        order.status === 'RECEIVED' ? 'bg-green-100 text-green-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {order.status === 'SENT' && (
                          <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        )}
                        {order.status === 'RECEIVED' && (
                          <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        )}
                        {order.status === 'DRAFT' ? 'Draft' :
                         order.status === 'SENT' ? 'Sent' :
                         order.status === 'PARTIALLY_RECEIVED' ? 'Partially Received' :
                         order.status === 'RECEIVED' ? 'Received' : 'Cancelled'}
                      </span>
                    </td>
                    <td className="px-3 py-2 text-right">
                      <div className="flex justify-end space-x-1">
                        <Button variant="ghost" size="sm" className="h-7 px-2 text-indigo-600 hover:text-indigo-800 hover:bg-indigo-50">
                          View
                        </Button>
                        {order.status === 'DRAFT' && (
                          <Button variant="ghost" size="sm" className="h-7 px-2 text-gray-600 hover:text-gray-800 hover:bg-gray-50">
                            Edit
                          </Button>
                        )}
                        {order.status === 'SENT' && (
                          <Button variant="ghost" size="sm" className="h-7 px-2 text-green-600 hover:text-green-800 hover:bg-green-50">
                            Receive
                          </Button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          <div className="flex items-center justify-between p-3 border-t border-gray-200">
            <div className="text-sm text-gray-500">
              Showing <span className="font-medium">1</span> to <span className="font-medium">{mockPurchaseOrders.length}</span> of <span className="font-medium">{mockPurchaseOrders.length}</span> results
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm" disabled>
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                Previous
              </Button>
              <Button variant="outline" size="sm">
                Next
                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
