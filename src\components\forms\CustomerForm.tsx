'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/Alert';
import { Spinner } from '@/components/ui/Spinner';
import BusinessRegistrationField from './BusinessRegistrationField';

interface CustomerFormProps {
  customer?: {
    id?: string;
    name: string;
    email: string;
    phone?: string;
    address?: string;
    taxId?: string;
    notes?: string;
  };
  isEditing?: boolean;
}

export default function CustomerForm({ customer, isEditing = false }: CustomerFormProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationMessage, setValidationMessage] = useState<string | null>(null);
  const [taxIdValidated, setTaxIdValidated] = useState(false);

  const [formData, setFormData] = useState({
    name: customer?.name || '',
    email: customer?.email || '',
    phone: customer?.phone || '',
    address: customer?.address || '',
    taxId: customer?.taxId || '',
    notes: customer?.notes || '',
    notificationPreferences: {
      preferredChannel: 'default', // default, email, whatsapp, both
      optOut: false,
      customReminderDays: false,
      reminderDaysBefore: 3,
    },
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleTaxIdChange = (value: string, isValid: boolean) => {
    setFormData(prev => ({
      ...prev,
      taxId: value,
    }));
    setTaxIdValidated(isValid);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setValidationMessage(null);

    try {
      // Prepare data for API
      const apiData = {
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        address: formData.address,
        taxId: formData.taxId,
        notes: formData.notes,
      };

      // Call API to create or update customer
      const url = isEditing && customer?.id
        ? `/api/customers/${customer.id}`
        : '/api/customers';

      const method = isEditing ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(apiData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to save customer');
      }

      const data = await response.json();

      setValidationMessage('Customer saved successfully');

      // Redirect to customer details page after a short delay
      setTimeout(() => {
        router.push(`/dashboard/customers/${data.customer.id}`);
      }, 1500);
    } catch (err: any) {
      console.error('Error saving customer:', err);
      setError(err.message || 'An error occurred while saving the customer');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Customer Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Customer Name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              required
            />
            <Input
              label="Email"
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              required
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Phone"
              type="tel"
              name="phone"
              value={formData.phone}
              onChange={handleChange}
            />
            <BusinessRegistrationField
              value={formData.taxId}
              onChange={handleTaxIdChange}
              label="Tax ID (TIN)"
              autoValidate={true}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Address
            </label>
            <textarea
              name="address"
              value={formData.address}
              onChange={handleChange}
              rows={3}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              placeholder="Customer address"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Notes
            </label>
            <textarea
              name="notes"
              value={formData.notes}
              onChange={handleChange}
              rows={3}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              placeholder="Additional notes about this customer"
            />
          </div>

          <div className="border-t pt-4 mt-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Notification Preferences</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Preferred Notification Channel
                </label>
                <select
                  value={formData.notificationPreferences.preferredChannel}
                  onChange={(e) => setFormData({
                    ...formData,
                    notificationPreferences: {
                      ...formData.notificationPreferences,
                      preferredChannel: e.target.value
                    }
                  })}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                >
                  <option value="default">Use default settings</option>
                  <option value="email">Email only</option>
                  <option value="whatsapp">WhatsApp only</option>
                  <option value="both">Both Email and WhatsApp</option>
                </select>
              </div>

              <div className="flex items-center">
                <input
                  id="optOut"
                  type="checkbox"
                  checked={formData.notificationPreferences.optOut}
                  onChange={(e) => setFormData({
                    ...formData,
                    notificationPreferences: {
                      ...formData.notificationPreferences,
                      optOut: e.target.checked
                    }
                  })}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label htmlFor="optOut" className="ml-2 block text-sm text-gray-700">
                  Opt out of all notifications
                </label>
              </div>

              <div className="flex items-center">
                <input
                  id="customReminderDays"
                  type="checkbox"
                  checked={formData.notificationPreferences.customReminderDays}
                  onChange={(e) => setFormData({
                    ...formData,
                    notificationPreferences: {
                      ...formData.notificationPreferences,
                      customReminderDays: e.target.checked
                    }
                  })}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label htmlFor="customReminderDays" className="ml-2 block text-sm text-gray-700">
                  Use custom reminder days
                </label>
              </div>

              {formData.notificationPreferences.customReminderDays && (
                <div className="pl-6 border-l-2 border-gray-100">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Days Before Due Date
                  </label>
                  <input
                    type="number"
                    min="1"
                    max="30"
                    value={formData.notificationPreferences.reminderDaysBefore}
                    onChange={(e) => setFormData({
                      ...formData,
                      notificationPreferences: {
                        ...formData.notificationPreferences,
                        reminderDaysBefore: parseInt(e.target.value) || 3
                      }
                    })}
                    className="block w-24 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  />
                </div>
              )}
            </div>
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {validationMessage && (
            <Alert variant="success">
              <AlertDescription>{validationMessage}</AlertDescription>
            </Alert>
          )}

          {formData.taxId && taxIdValidated && (
            <Alert>
              <AlertDescription className="text-green-600 flex items-center">
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Tax ID validated with LHDN MyInvois
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Spinner size="sm" className="mr-2" />
                Saving...
              </>
            ) : (
              isEditing ? 'Update Customer' : 'Create Customer'
            )}
          </Button>
        </CardFooter>
      </Card>
    </form>
  );
}
