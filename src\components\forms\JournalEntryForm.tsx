'use client';

import { useState, useEffect } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { CalendarIcon, Plus, Trash2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { useToast } from '@/components/ui/use-toast';
import {
  JournalEntry,
  CreateJournalEntryRequest,
  UpdateJournalEntryRequest,
  JournalEntryStatus,
  RecurringFrequency,
  Account,
} from '@/lib/api/bookkeeping.service';
import bookkeepingService from '@/lib/api/bookkeeping.service';

// Define the form schema with Zod
const journalLineSchema = z.object({
  accountId: z.string().min(1, { message: 'Account is required' }),
  description: z.string().optional(),
  debitAmount: z.coerce.number().min(0, { message: 'Debit amount must be 0 or greater' }),
  creditAmount: z.coerce.number().min(0, { message: 'Credit amount must be 0 or greater' }),
  taxAmount: z.coerce.number().min(0, { message: 'Tax amount must be 0 or greater' }).optional(),
  taxCodeId: z.string().optional(),
});

const journalEntryFormSchema = z.object({
  entryDate: z.date({
    required_error: 'Entry date is required',
  }),
  description: z.string().min(1, { message: 'Description is required' }),
  reference: z.string().optional(),
  lines: z.array(journalLineSchema)
    .min(2, { message: 'At least two lines are required for a journal entry' })
    .refine(
      (lines) => {
        // Calculate total debits and credits
        const totalDebits = lines.reduce((sum, line) => sum + (line.debitAmount || 0), 0);
        const totalCredits = lines.reduce((sum, line) => sum + (line.creditAmount || 0), 0);
        
        // Check if they are equal (within a small epsilon for floating point comparison)
        return Math.abs(totalDebits - totalCredits) < 0.01;
      },
      {
        message: 'Total debits must equal total credits',
        path: ['lines'],
      }
    ),
  isRecurring: z.boolean().default(false),
  recurringFrequency: z.nativeEnum(RecurringFrequency).optional(),
  nextRunDate: z.date().optional(),
  endDate: z.date().optional(),
});

type JournalEntryFormValues = z.infer<typeof journalEntryFormSchema>;

interface JournalEntryFormProps {
  journalEntry?: JournalEntry;
  onSuccess?: (journalEntry: JournalEntry) => void;
  onCancel?: () => void;
}

export function JournalEntryForm({ journalEntry, onSuccess, onCancel }: JournalEntryFormProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [isLoadingAccounts, setIsLoadingAccounts] = useState(true);

  // Initialize the form with default values or existing journal entry data
  const form = useForm<JournalEntryFormValues>({
    resolver: zodResolver(journalEntryFormSchema),
    defaultValues: journalEntry
      ? {
          entryDate: new Date(journalEntry.entryDate),
          description: journalEntry.description,
          reference: journalEntry.reference || '',
          lines: journalEntry.lines.map(line => ({
            accountId: line.accountId,
            description: line.description || '',
            debitAmount: line.debitAmount,
            creditAmount: line.creditAmount,
            taxAmount: line.taxAmount || 0,
            taxCodeId: line.taxCodeId || '',
          })),
          isRecurring: journalEntry.isRecurring || false,
        }
      : {
          entryDate: new Date(),
          description: '',
          reference: '',
          lines: [
            { accountId: '', description: '', debitAmount: 0, creditAmount: 0, taxAmount: 0, taxCodeId: '' },
            { accountId: '', description: '', debitAmount: 0, creditAmount: 0, taxAmount: 0, taxCodeId: '' },
          ],
          isRecurring: false,
        },
  });

  // Use field array for journal lines
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'lines',
  });

  // Calculate totals
  const totalDebits = form.watch('lines').reduce((sum, line) => sum + (Number(line.debitAmount) || 0), 0);
  const totalCredits = form.watch('lines').reduce((sum, line) => sum + (Number(line.creditAmount) || 0), 0);
  const difference = Math.abs(totalDebits - totalCredits);
  const isBalanced = difference < 0.01;

  // Fetch accounts on component mount
  useEffect(() => {
    const fetchAccounts = async () => {
      try {
        const accountsData = await bookkeepingService.getAccounts();
        setAccounts(accountsData.filter(account => account.isActive));
      } catch (error) {
        console.error('Error fetching accounts:', error);
        toast({
          title: 'Error',
          description: 'Failed to load accounts. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setIsLoadingAccounts(false);
      }
    };

    fetchAccounts();
  }, [toast]);

  // Handle form submission
  const onSubmit = async (values: JournalEntryFormValues) => {
    setIsSubmitting(true);
    try {
      let result: JournalEntry;

      // Prepare the request data
      const journalData = {
        entryDate: format(values.entryDate, 'yyyy-MM-dd'),
        description: values.description,
        reference: values.reference,
        lines: values.lines.map(line => ({
          accountId: line.accountId,
          description: line.description,
          debitAmount: Number(line.debitAmount) || 0,
          creditAmount: Number(line.creditAmount) || 0,
          taxAmount: Number(line.taxAmount) || 0,
          taxCodeId: line.taxCodeId,
        })),
      };

      // Add recurring schedule if applicable
      if (values.isRecurring && values.recurringFrequency && values.nextRunDate) {
        const recurringSchedule = {
          frequency: values.recurringFrequency,
          nextRunDate: format(values.nextRunDate, 'yyyy-MM-dd'),
          ...(values.endDate && { endDate: format(values.endDate, 'yyyy-MM-dd') }),
        };

        Object.assign(journalData, {
          isRecurring: true,
          recurringSchedule,
        });
      }

      if (journalEntry) {
        // Update existing journal entry
        const updateData: UpdateJournalEntryRequest = journalData;
        result = await bookkeepingService.updateJournalEntry(journalEntry.id, updateData);
        toast({
          title: 'Journal Entry Updated',
          description: 'The journal entry has been updated successfully.',
          variant: 'default',
        });
      } else {
        // Create new journal entry
        const createData: CreateJournalEntryRequest = journalData as CreateJournalEntryRequest;
        result = await bookkeepingService.createJournalEntry(createData);
        toast({
          title: 'Journal Entry Created',
          description: 'The journal entry has been created successfully.',
          variant: 'default',
        });
      }

      // Call the onSuccess callback if provided
      if (onSuccess) {
        onSuccess(result);
      }
    } catch (error: any) {
      toast({
        title: journalEntry ? 'Update Failed' : 'Creation Failed',
        description: error.message || 'An error occurred. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Add a new journal line
  const addLine = () => {
    append({ accountId: '', description: '', debitAmount: 0, creditAmount: 0, taxAmount: 0, taxCodeId: '' });
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Entry Date */}
          <FormField
            control={form.control}
            name="entryDate"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Entry Date</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        {field.value ? (
                          format(field.value, "PPP")
                        ) : (
                          <span>Pick a date</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      disabled={(date) =>
                        date > new Date() || date < new Date("1900-01-01")
                      }
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormDescription>
                  The date of this journal entry
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Reference */}
          <FormField
            control={form.control}
            name="reference"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Reference</FormLabel>
                <FormControl>
                  <Input placeholder="e.g., INV-2023-0001" {...field} />
                </FormControl>
                <FormDescription>
                  Optional reference number or document ID
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Description */}
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Enter a description for this journal entry"
                  className="resize-none"
                  {...field}
                />
              </FormControl>
              <FormDescription>
                Provide a clear description of the purpose of this journal entry
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Journal Lines */}
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">Journal Lines</h3>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addLine}
              disabled={isLoadingAccounts}
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Line
            </Button>
          </div>

          {/* Journal Lines Table */}
          <div className="border rounded-md">
            <table className="w-full">
              <thead>
                <tr className="border-b bg-muted/50">
                  <th className="px-4 py-2 text-left font-medium text-sm">Account</th>
                  <th className="px-4 py-2 text-left font-medium text-sm">Description</th>
                  <th className="px-4 py-2 text-right font-medium text-sm">Debit (RM)</th>
                  <th className="px-4 py-2 text-right font-medium text-sm">Credit (RM)</th>
                  <th className="px-4 py-2 text-center font-medium text-sm w-10">Actions</th>
                </tr>
              </thead>
              <tbody>
                {fields.map((field, index) => (
                  <tr key={field.id} className="border-b last:border-b-0">
                    <td className="px-4 py-2">
                      <FormField
                        control={form.control}
                        name={`lines.${index}.accountId`}
                        render={({ field }) => (
                          <FormItem className="space-y-0">
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                              disabled={isLoadingAccounts}
                            >
                              <FormControl>
                                <SelectTrigger className="w-full">
                                  <SelectValue placeholder="Select account" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {accounts.map((account) => (
                                  <SelectItem key={account.id} value={account.id}>
                                    {account.accountCode} - {account.accountName}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </td>
                    <td className="px-4 py-2">
                      <FormField
                        control={form.control}
                        name={`lines.${index}.description`}
                        render={({ field }) => (
                          <FormItem className="space-y-0">
                            <FormControl>
                              <Input placeholder="Line description" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </td>
                    <td className="px-4 py-2">
                      <FormField
                        control={form.control}
                        name={`lines.${index}.debitAmount`}
                        render={({ field }) => (
                          <FormItem className="space-y-0">
                            <FormControl>
                              <Input
                                type="number"
                                step="0.01"
                                min="0"
                                placeholder="0.00"
                                {...field}
                                onChange={(e) => {
                                  field.onChange(e);
                                  // If debit is entered, clear credit
                                  if (e.target.value && Number(e.target.value) > 0) {
                                    form.setValue(`lines.${index}.creditAmount`, 0);
                                  }
                                }}
                                className="text-right"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </td>
                    <td className="px-4 py-2">
                      <FormField
                        control={form.control}
                        name={`lines.${index}.creditAmount`}
                        render={({ field }) => (
                          <FormItem className="space-y-0">
                            <FormControl>
                              <Input
                                type="number"
                                step="0.01"
                                min="0"
                                placeholder="0.00"
                                {...field}
                                onChange={(e) => {
                                  field.onChange(e);
                                  // If credit is entered, clear debit
                                  if (e.target.value && Number(e.target.value) > 0) {
                                    form.setValue(`lines.${index}.debitAmount`, 0);
                                  }
                                }}
                                className="text-right"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </td>
                    <td className="px-4 py-2 text-center">
                      {fields.length > 2 && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => remove(index)}
                          className="h-8 w-8 p-0"
                        >
                          <Trash2 className="h-4 w-4 text-red-500" />
                        </Button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
              <tfoot>
                <tr className="border-t bg-muted/50">
                  <th className="px-4 py-2 text-left font-medium text-sm" colSpan={2}>
                    Total
                  </th>
                  <th className="px-4 py-2 text-right font-medium text-sm">
                    {totalDebits.toFixed(2)}
                  </th>
                  <th className="px-4 py-2 text-right font-medium text-sm">
                    {totalCredits.toFixed(2)}
                  </th>
                  <th className="px-4 py-2"></th>
                </tr>
                <tr>
                  <th className="px-4 py-2 text-left font-medium text-sm" colSpan={2}>
                    Difference
                  </th>
                  <th className="px-4 py-2 text-right font-medium text-sm" colSpan={2}>
                    <span className={isBalanced ? 'text-green-600' : 'text-red-600'}>
                      {difference.toFixed(2)}
                    </span>
                  </th>
                  <th className="px-4 py-2"></th>
                </tr>
              </tfoot>
            </table>
          </div>
          {!isBalanced && (
            <p className="text-sm text-red-600">
              Total debits must equal total credits. Current difference: {difference.toFixed(2)}
            </p>
          )}
        </div>

        {/* Recurring Entry */}
        <FormField
          control={form.control}
          name="isRecurring"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base">Recurring Entry</FormLabel>
                <FormDescription>
                  Set this journal entry to recur automatically
                </FormDescription>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />

        {/* Recurring Options (conditionally rendered) */}
        {form.watch('isRecurring') && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 p-4 border rounded-lg">
            {/* Frequency */}
            <FormField
              control={form.control}
              name="recurringFrequency"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Frequency</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select frequency" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {Object.values(RecurringFrequency).map((frequency) => (
                        <SelectItem key={frequency} value={frequency}>
                          {frequency}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    How often this entry should recur
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Next Run Date */}
            <FormField
              control={form.control}
              name="nextRunDate"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Next Run Date</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          {field.value ? (
                            format(field.value, "PPP")
                          ) : (
                            <span>Pick a date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={(date) =>
                          date < new Date()
                        }
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormDescription>
                    When this entry should next be created
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* End Date (Optional) */}
            <FormField
              control={form.control}
              name="endDate"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>End Date (Optional)</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          {field.value ? (
                            format(field.value, "PPP")
                          ) : (
                            <span>Pick a date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value || undefined}
                        onSelect={field.onChange}
                        disabled={(date) =>
                          date < (form.watch('nextRunDate') || new Date())
                        }
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormDescription>
                    When this recurring entry should stop (leave blank for indefinite)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )}

        {/* Form Actions */}
        <div className="flex justify-end space-x-4">
          {onCancel && (
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
          )}
          <Button 
            type="submit" 
            disabled={isSubmitting || !isBalanced || isLoadingAccounts}
          >
            {isSubmitting ? 'Saving...' : journalEntry ? 'Update Journal Entry' : 'Create Journal Entry'}
          </Button>
        </div>
      </form>
    </Form>
  );
}
