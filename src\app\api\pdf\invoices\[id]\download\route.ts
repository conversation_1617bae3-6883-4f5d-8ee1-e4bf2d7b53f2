import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

/**
 * API route for downloading an invoice PDF
 * This route proxies the request to the backend PDF service
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the invoice ID from the URL params
    const { id } = params;

    // Get the user session
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    // Get the access token from the session
    const accessToken = session.accessToken;

    // Call the backend API to generate and download the PDF
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/pdf/invoices/${id}/download`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      },
    });

    // If the response is not OK, return an error
    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      return NextResponse.json(
        { error: errorData?.message || 'Failed to generate PDF' },
        { status: response.status }
      );
    }

    // Get the PDF data as a buffer
    const pdfBuffer = await response.arrayBuffer();

    // Get the content type and filename from the response headers
    const contentType = response.headers.get('content-type') || 'application/pdf';
    const contentDisposition = response.headers.get('content-disposition') || '';
    const filenameMatch = contentDisposition.match(/filename="(.+?)"/);
    const filename = filenameMatch ? filenameMatch[1] : `invoice-${id}.pdf`;

    // Return the PDF data with the appropriate headers
    return new NextResponse(pdfBuffer, {
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${filename}"`,
      },
    });
  } catch (error) {
    console.error('Error downloading PDF:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
