import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

/**
 * API route for generating multiple PDFs in batch
 */
export async function POST(request: NextRequest) {
  try {
    // Get the user session
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    // Get the access token from the session
    const accessToken = session.accessToken;

    // Get the request body
    const body = await request.json();

    // Call the backend API to generate batch PDFs
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/pdf/invoices/batch`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    // If the response is not OK, return an error
    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      return NextResponse.json(
        { error: errorData?.message || 'Failed to generate batch PDFs' },
        { status: response.status }
      );
    }

    // Return the batch result
    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error generating batch PDFs:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
