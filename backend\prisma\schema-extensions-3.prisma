model GoodsReceipt {
  id                String              @id @default(uuid())
  receiptNumber     String
  purchaseOrderId   String
  purchaseOrder     PurchaseOrder       @relation(fields: [purchaseOrderId], references: [id])
  receiveDate       DateTime            @default(now())
  notes             String?
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  items             GoodsReceiptItem[]
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@unique([receiptNumber, tenantId])
  @@index([tenantId])
  @@index([purchaseOrderId])
}

model GoodsReceiptItem {
  id                String              @id @default(uuid())
  goodsReceiptId    String
  goodsReceipt      GoodsReceipt        @relation(fields: [goodsReceiptId], references: [id], onDelete: Cascade)
  productId         String
  quantity          Int
  notes             String?
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([goodsReceiptId])
  @@index([tenantId])
}

// Fixed Asset Management Module
model FixedAsset {
  id                String              @id @default(uuid())
  assetNumber       String
  name              String
  description       String?
  category          AssetCategory
  purchaseDate      DateTime
  purchasePrice     Float
  currentValue      Float
  location          String?
  assignedTo        String?             // Employee ID if assigned
  status            AssetStatus         @default(ACTIVE)
  disposalDate      DateTime?
  disposalValue     Float?
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  depreciations     AssetDepreciation[]
  maintenances      AssetMaintenance[]
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@unique([assetNumber, tenantId])
  @@index([tenantId])
}

model AssetDepreciation {
  id                String              @id @default(uuid())
  assetId           String
  asset             FixedAsset          @relation(fields: [assetId], references: [id], onDelete: Cascade)
  depreciationDate  DateTime
  depreciationAmount Float
  bookValue         Float               // Value after depreciation
  notes             String?
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([assetId])
  @@index([tenantId])
}

model AssetMaintenance {
  id                String              @id @default(uuid())
  assetId           String
  asset             FixedAsset          @relation(fields: [assetId], references: [id], onDelete: Cascade)
  maintenanceDate   DateTime
  description       String
  cost              Float
  provider          String?
  nextMaintenanceDate DateTime?
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([assetId])
  @@index([tenantId])
}

enum AssetCategory {
  LAND
  BUILDING
  EQUIPMENT
  VEHICLE
  FURNITURE
  COMPUTER
  SOFTWARE
  OTHER
}

enum AssetStatus {
  ACTIVE
  MAINTENANCE
  DISPOSED
  LOST
  RETIRED
}
