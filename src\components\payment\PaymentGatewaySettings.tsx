'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Spinner } from '@/components/ui/spinner';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';

export default function PaymentGatewaySettings() {
  const [isLoading, setIsLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [activeGateway, setActiveGateway] = useState<'stripe' | 'razorpay'>('stripe');
  
  // Mock gateway data
  const [gateways, setGateways] = useState({
    stripe: {
      isActive: true,
      apiKey: '•••••••••••••••••••••••••••',
      webhookSecret: '•••••••••••••••••••••••••••',
      currency: 'MYR',
      testMode: true,
      lastSync: '2023-06-20T08:30:00Z'
    },
    razorpay: {
      isActive: false,
      apiKey: '',
      apiSecret: '',
      currency: 'MYR',
      testMode: true,
      lastSync: null
    }
  });

  const handleSaveSettings = async () => {
    setIsLoading(true);
    setSuccessMessage(null);
    setErrorMessage(null);
    
    try {
      // In a real implementation, this would call your API
      // Simulate API call with timeout
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      setSuccessMessage(`${activeGateway.charAt(0).toUpperCase() + activeGateway.slice(1)} settings saved successfully`);
    } catch (err: any) {
      console.error('Error saving payment gateway settings:', err);
      setErrorMessage(err.message || 'Failed to save payment gateway settings');
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleActive = (gateway: 'stripe' | 'razorpay') => {
    setGateways({
      ...gateways,
      [gateway]: {
        ...gateways[gateway],
        isActive: !gateways[gateway].isActive
      }
    });
  };

  const handleToggleTestMode = (gateway: 'stripe' | 'razorpay') => {
    setGateways({
      ...gateways,
      [gateway]: {
        ...gateways[gateway],
        testMode: !gateways[gateway].testMode
      }
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Payment Gateway Settings</h1>
          <p className="mt-1 text-sm text-gray-500">
            Configure your payment gateway integrations
          </p>
        </div>
      </div>

      {successMessage && (
        <Alert variant="success" className="bg-green-50 text-green-800 border-green-200">
          <AlertDescription>{successMessage}</AlertDescription>
        </Alert>
      )}

      {errorMessage && (
        <Alert variant="destructive">
          <AlertDescription>{errorMessage}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="stripe" onValueChange={(value) => setActiveGateway(value as 'stripe' | 'razorpay')}>
        <TabsList>
          <TabsTrigger value="stripe">Stripe</TabsTrigger>
          <TabsTrigger value="razorpay">Razorpay</TabsTrigger>
        </TabsList>
        
        <TabsContent value="stripe" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle>Stripe</CardTitle>
                  <CardDescription>
                    Configure your Stripe payment gateway
                  </CardDescription>
                </div>
                <Badge variant={gateways.stripe.isActive ? 'default' : 'secondary'}>
                  {gateways.stripe.isActive ? 'Active' : 'Inactive'}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="stripeActive">Active</Label>
                  <p className="text-sm text-gray-500">
                    Enable or disable Stripe payments
                  </p>
                </div>
                <Switch
                  id="stripeActive"
                  checked={gateways.stripe.isActive}
                  onCheckedChange={() => handleToggleActive('stripe')}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="stripeTestMode">Test Mode</Label>
                  <p className="text-sm text-gray-500">
                    Use Stripe test environment
                  </p>
                </div>
                <Switch
                  id="stripeTestMode"
                  checked={gateways.stripe.testMode}
                  onCheckedChange={() => handleToggleTestMode('stripe')}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="stripeApiKey">API Key</Label>
                <Input 
                  id="stripeApiKey" 
                  value={gateways.stripe.apiKey} 
                  onChange={(e) => setGateways({
                    ...gateways,
                    stripe: {
                      ...gateways.stripe,
                      apiKey: e.target.value
                    }
                  })}
                  placeholder="Enter your Stripe API Key"
                  type={gateways.stripe.isActive ? 'password' : 'text'}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="stripeWebhookSecret">Webhook Secret</Label>
                <Input 
                  id="stripeWebhookSecret" 
                  value={gateways.stripe.webhookSecret} 
                  onChange={(e) => setGateways({
                    ...gateways,
                    stripe: {
                      ...gateways.stripe,
                      webhookSecret: e.target.value
                    }
                  })}
                  placeholder="Enter your Stripe Webhook Secret"
                  type={gateways.stripe.isActive ? 'password' : 'text'}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="stripeCurrency">Currency</Label>
                <select
                  id="stripeCurrency"
                  className="w-full rounded-md border border-gray-300 py-2 px-3 text-sm"
                  value={gateways.stripe.currency}
                  onChange={(e) => setGateways({
                    ...gateways,
                    stripe: {
                      ...gateways.stripe,
                      currency: e.target.value
                    }
                  })}
                >
                  <option value="MYR">Malaysian Ringgit (MYR)</option>
                  <option value="USD">US Dollar (USD)</option>
                  <option value="SGD">Singapore Dollar (SGD)</option>
                </select>
              </div>
              
              {gateways.stripe.lastSync && (
                <div className="text-sm text-gray-500">
                  Last synced: {new Date(gateways.stripe.lastSync).toLocaleString()}
                </div>
              )}
            </CardContent>
            <CardFooter>
              <Button 
                onClick={handleSaveSettings}
                disabled={isLoading}
                className="w-full"
              >
                {isLoading ? (
                  <>
                    <Spinner size="sm" className="mr-2" />
                    Saving...
                  </>
                ) : (
                  <>Save Stripe Settings</>
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        <TabsContent value="razorpay" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle>Razorpay</CardTitle>
                  <CardDescription>
                    Configure your Razorpay payment gateway
                  </CardDescription>
                </div>
                <Badge variant={gateways.razorpay.isActive ? 'default' : 'secondary'}>
                  {gateways.razorpay.isActive ? 'Active' : 'Inactive'}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="razorpayActive">Active</Label>
                  <p className="text-sm text-gray-500">
                    Enable or disable Razorpay payments
                  </p>
                </div>
                <Switch
                  id="razorpayActive"
                  checked={gateways.razorpay.isActive}
                  onCheckedChange={() => handleToggleActive('razorpay')}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="razorpayTestMode">Test Mode</Label>
                  <p className="text-sm text-gray-500">
                    Use Razorpay test environment
                  </p>
                </div>
                <Switch
                  id="razorpayTestMode"
                  checked={gateways.razorpay.testMode}
                  onCheckedChange={() => handleToggleTestMode('razorpay')}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="razorpayApiKey">API Key</Label>
                <Input 
                  id="razorpayApiKey" 
                  value={gateways.razorpay.apiKey} 
                  onChange={(e) => setGateways({
                    ...gateways,
                    razorpay: {
                      ...gateways.razorpay,
                      apiKey: e.target.value
                    }
                  })}
                  placeholder="Enter your Razorpay API Key"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="razorpayApiSecret">API Secret</Label>
                <Input 
                  id="razorpayApiSecret" 
                  value={gateways.razorpay.apiSecret} 
                  onChange={(e) => setGateways({
                    ...gateways,
                    razorpay: {
                      ...gateways.razorpay,
                      apiSecret: e.target.value
                    }
                  })}
                  placeholder="Enter your Razorpay API Secret"
                  type="password"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="razorpayCurrency">Currency</Label>
                <select
                  id="razorpayCurrency"
                  className="w-full rounded-md border border-gray-300 py-2 px-3 text-sm"
                  value={gateways.razorpay.currency}
                  onChange={(e) => setGateways({
                    ...gateways,
                    razorpay: {
                      ...gateways.razorpay,
                      currency: e.target.value
                    }
                  })}
                >
                  <option value="MYR">Malaysian Ringgit (MYR)</option>
                  <option value="USD">US Dollar (USD)</option>
                  <option value="INR">Indian Rupee (INR)</option>
                </select>
              </div>
              
              {gateways.razorpay.lastSync && (
                <div className="text-sm text-gray-500">
                  Last synced: {new Date(gateways.razorpay.lastSync).toLocaleString()}
                </div>
              )}
            </CardContent>
            <CardFooter>
              <Button 
                onClick={handleSaveSettings}
                disabled={isLoading}
                className="w-full"
              >
                {isLoading ? (
                  <>
                    <Spinner size="sm" className="mr-2" />
                    Saving...
                  </>
                ) : (
                  <>Save Razorpay Settings</>
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
