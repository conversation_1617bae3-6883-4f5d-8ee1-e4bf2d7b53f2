import { prisma } from '../index';
import { logger } from '../utils/logger';
import Strip<PERSON> from 'stripe';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';

// Initialize payment gateways
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
  apiVersion: '2023-10-16',
});

/**
 * Payment Service
 * Handles payment processing and integration with payment gateways
 */
export class PaymentService {
  /**
   * Get all payment methods for a tenant
   * @param tenantId Tenant ID
   */
  async getPaymentMethods(tenantId: string) {
    try {
      return await prisma.paymentMethod.findMany({
        where: { tenantId },
      });
    } catch (error) {
      logger.error('Error getting payment methods:', error);
      throw error;
    }
  }

  /**
   * Get all payment gateways for a tenant
   * @param tenantId Tenant ID
   */
  async getPaymentGateways(tenantId: string) {
    try {
      return await prisma.paymentGateway.findMany({
        where: { tenantId },
      });
    } catch (error) {
      logger.error('Error getting payment gateways:', error);
      throw error;
    }
  }

  /**
   * Create a payment
   * @param data Payment data
   */
  async createPayment(data: {
    invoiceId: string;
    amount: number;
    paymentMethod: string;
    paymentGateway?: string;
    returnUrl?: string;
    metadata?: Record<string, any>;
    tenantId: string;
    userId: string;
  }) {
    try {
      // Get the invoice
      const invoice = await prisma.invoice.findUnique({
        where: { id: data.invoiceId },
      });

      if (!invoice) {
        throw new Error('Invoice not found');
      }

      // Create payment record
      const payment = await prisma.payment.create({
        data: {
          invoiceId: data.invoiceId,
          amount: data.amount,
          status: 'PENDING',
          paymentMethod: data.paymentMethod,
          paymentGateway: data.paymentGateway || 'MANUAL',
          metadata: data.metadata || {},
          tenantId: data.tenantId,
          createdBy: data.userId,
        },
      });

      // Process payment based on gateway
      if (data.paymentGateway === 'STRIPE') {
        return await this.processStripePayment(payment.id, data);
      } else if (data.paymentGateway === 'PAYPAL') {
        return await this.processPayPalPayment(payment.id, data);
      } else if (data.paymentGateway === 'BILLPLZ') {
        return await this.processBillPlzPayment(payment.id, data);
      } else {
        // Manual payment
        return payment;
      }
    } catch (error) {
      logger.error('Error creating payment:', error);
      throw error;
    }
  }

  /**
   * Process payment with Stripe
   * @param paymentId Payment ID
   * @param data Payment data
   */
  private async processStripePayment(
    paymentId: string,
    data: {
      invoiceId: string;
      amount: number;
      returnUrl?: string;
      metadata?: Record<string, any>;
    }
  ) {
    try {
      // Get invoice details
      const invoice = await prisma.invoice.findUnique({
        where: { id: data.invoiceId },
        include: { customer: true },
      });

      if (!invoice) {
        throw new Error('Invoice not found');
      }

      // Create payment intent
      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(data.amount * 100), // Convert to cents
        currency: 'myr',
        description: `Payment for Invoice #${invoice.invoiceNumber}`,
        metadata: {
          invoiceId: data.invoiceId,
          paymentId,
          ...data.metadata,
        },
      });

      // Update payment with transaction ID
      const updatedPayment = await prisma.payment.update({
        where: { id: paymentId },
        data: {
          transactionId: paymentIntent.id,
          metadata: {
            ...data.metadata,
            clientSecret: paymentIntent.client_secret,
          },
        },
      });

      return {
        ...updatedPayment,
        clientSecret: paymentIntent.client_secret,
      };
    } catch (error) {
      // Update payment status to failed
      await prisma.payment.update({
        where: { id: paymentId },
        data: {
          status: 'FAILED',
          metadata: {
            error: (error as Error).message,
          },
        },
      });

      logger.error('Error processing Stripe payment:', error);
      throw error;
    }
  }

  /**
   * Process payment with PayPal
   * @param paymentId Payment ID
   * @param data Payment data
   */
  private async processPayPalPayment(
    paymentId: string,
    data: {
      invoiceId: string;
      amount: number;
      returnUrl?: string;
      metadata?: Record<string, any>;
    }
  ) {
    try {
      // Get invoice details
      const invoice = await prisma.invoice.findUnique({
        where: { id: data.invoiceId },
        include: { customer: true },
      });

      if (!invoice) {
        throw new Error('Invoice not found');
      }

      // Mock PayPal integration
      const paypalPaymentId = `PAY-${uuidv4().substring(0, 8)}`;
      const paypalUrl = `https://www.sandbox.paypal.com/checkoutnow?token=${paypalPaymentId}`;

      // Update payment with transaction ID
      const updatedPayment = await prisma.payment.update({
        where: { id: paymentId },
        data: {
          transactionId: paypalPaymentId,
          paymentUrl: paypalUrl,
          metadata: {
            ...data.metadata,
            returnUrl: data.returnUrl,
          },
        },
      });

      return {
        ...updatedPayment,
        paymentUrl: paypalUrl,
      };
    } catch (error) {
      // Update payment status to failed
      await prisma.payment.update({
        where: { id: paymentId },
        data: {
          status: 'FAILED',
          metadata: {
            error: (error as Error).message,
          },
        },
      });

      logger.error('Error processing PayPal payment:', error);
      throw error;
    }
  }

  /**
   * Process payment with BillPlz
   * @param paymentId Payment ID
   * @param data Payment data
   */
  private async processBillPlzPayment(
    paymentId: string,
    data: {
      invoiceId: string;
      amount: number;
      returnUrl?: string;
      metadata?: Record<string, any>;
    }
  ) {
    try {
      // Get invoice details
      const invoice = await prisma.invoice.findUnique({
        where: { id: data.invoiceId },
        include: { customer: true },
      });

      if (!invoice) {
        throw new Error('Invoice not found');
      }

      // Mock BillPlz integration
      const billId = `BILL-${uuidv4().substring(0, 8)}`;
      const billUrl = `https://www.billplz-sandbox.com/bills/${billId}`;

      // Update payment with transaction ID
      const updatedPayment = await prisma.payment.update({
        where: { id: paymentId },
        data: {
          transactionId: billId,
          paymentUrl: billUrl,
          metadata: {
            ...data.metadata,
            returnUrl: data.returnUrl,
          },
        },
      });

      return {
        ...updatedPayment,
        billUrl,
        billId,
      };
    } catch (error) {
      // Update payment status to failed
      await prisma.payment.update({
        where: { id: paymentId },
        data: {
          status: 'FAILED',
          metadata: {
            error: (error as Error).message,
          },
        },
      });

      logger.error('Error processing BillPlz payment:', error);
      throw error;
    }
  }

  /**
   * Record a manual payment
   * @param data Payment data
   */
  async recordManualPayment(data: {
    invoiceId: string;
    amount: number;
    method: string;
    reference?: string;
    notes?: string;
    tenantId: string;
    userId: string;
  }) {
    try {
      // Create payment record
      const payment = await prisma.payment.create({
        data: {
          invoiceId: data.invoiceId,
          amount: data.amount,
          status: 'COMPLETED',
          paymentMethod: data.method,
          paymentGateway: 'MANUAL',
          transactionId: data.reference,
          metadata: {
            notes: data.notes,
          },
          tenantId: data.tenantId,
          createdBy: data.userId,
        },
      });

      // Update invoice status if fully paid
      await this.updateInvoiceStatus(data.invoiceId);

      return payment;
    } catch (error) {
      logger.error('Error recording manual payment:', error);
      throw error;
    }
  }

  /**
   * Update invoice status based on payments
   * @param invoiceId Invoice ID
   */
  async updateInvoiceStatus(invoiceId: string) {
    try {
      // Get invoice
      const invoice = await prisma.invoice.findUnique({
        where: { id: invoiceId },
      });

      if (!invoice) {
        throw new Error('Invoice not found');
      }

      // Get total payments
      const payments = await prisma.payment.findMany({
        where: {
          invoiceId,
          status: 'COMPLETED',
        },
      });

      const totalPaid = payments.reduce((sum, payment) => sum + payment.amount, 0);

      // Update invoice status
      if (totalPaid >= invoice.totalAmount) {
        await prisma.invoice.update({
          where: { id: invoiceId },
          data: {
            status: 'PAID',
            paidAmount: totalPaid,
            paidAt: new Date(),
          },
        });
      } else if (totalPaid > 0) {
        await prisma.invoice.update({
          where: { id: invoiceId },
          data: {
            status: 'PARTIALLY_PAID',
            paidAmount: totalPaid,
          },
        });
      }
    } catch (error) {
      logger.error('Error updating invoice status:', error);
      throw error;
    }
  }

  /**
   * Handle Stripe webhook event
   * @param event Stripe event
   */
  async handleStripeWebhook(event: Stripe.Event) {
    try {
      if (event.type === 'payment_intent.succeeded') {
        const paymentIntent = event.data.object as Stripe.PaymentIntent;
        const paymentId = paymentIntent.metadata.paymentId;

        if (paymentId) {
          // Update payment status
          await prisma.payment.update({
            where: { id: paymentId },
            data: {
              status: 'COMPLETED',
              updatedAt: new Date(),
            },
          });

          // Update invoice status
          const payment = await prisma.payment.findUnique({
            where: { id: paymentId },
          });

          if (payment) {
            await this.updateInvoiceStatus(payment.invoiceId);
          }
        }
      } else if (event.type === 'payment_intent.payment_failed') {
        const paymentIntent = event.data.object as Stripe.PaymentIntent;
        const paymentId = paymentIntent.metadata.paymentId;

        if (paymentId) {
          // Update payment status
          await prisma.payment.update({
            where: { id: paymentId },
            data: {
              status: 'FAILED',
              metadata: {
                error: paymentIntent.last_payment_error?.message || 'Payment failed',
              },
              updatedAt: new Date(),
            },
          });
        }
      }
    } catch (error) {
      logger.error('Error handling Stripe webhook:', error);
      throw error;
    }
  }
}

export const paymentService = new PaymentService();
export default paymentService;
