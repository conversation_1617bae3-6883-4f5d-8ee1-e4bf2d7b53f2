import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import multer from 'multer';
import path from 'path';
import fs from 'fs-extra';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../utils/logger';

const prisma = new PrismaClient();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(process.cwd(), 'uploads', 'logos');
    fs.ensureDirSync(uploadDir);
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    // Generate a unique filename with the original extension
    const fileExt = path.extname(file.originalname);
    const fileName = `${uuidv4()}${fileExt}`;
    cb(null, fileName);
  }
});

// File filter to only allow image files
const fileFilter = (req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // Accept only image files
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('Only image files are allowed'));
  }
};

// Create the multer upload instance
export const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB max file size
  }
}).single('logo');

/**
 * Upload a tenant logo
 * @param req Request
 * @param res Response
 */
export const uploadTenantLogo = async (req: Request, res: Response) => {
  // Handle the file upload using multer
  upload(req, res, async (err) => {
    try {
      if (err) {
        logger.error('Error uploading file', { error: err.message });
        return res.status(400).json({ message: err.message });
      }

      if (!req.file) {
        return res.status(400).json({ message: 'No file uploaded' });
      }

      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const { tenantId } = req.user;
      const fileName = req.file.filename;

      // Get the current tenant
      const tenant = await prisma.tenant.findUnique({
        where: { id: tenantId }
      });

      if (!tenant) {
        return res.status(404).json({ message: 'Tenant not found' });
      }

      // If the tenant already has a logo, delete the old file
      if (tenant.logo) {
        try {
          const oldLogoPath = path.join(process.cwd(), 'uploads', 'logos', tenant.logo);
          if (await fs.pathExists(oldLogoPath)) {
            await fs.unlink(oldLogoPath);
            logger.info('Deleted old logo file', { path: oldLogoPath });
          }
        } catch (error) {
          logger.error('Error deleting old logo file', { error });
          // Continue even if we couldn't delete the old file
        }
      }

      // Update the tenant with the new logo filename
      const updatedTenant = await prisma.tenant.update({
        where: { id: tenantId },
        data: { logo: fileName }
      });

      logger.info('Tenant logo updated successfully', { tenantId, fileName });
      return res.status(200).json({
        message: 'Logo uploaded successfully',
        logo: fileName
      });
    } catch (error: any) {
      logger.error('Error in uploadTenantLogo', { error: error.message, stack: error.stack });
      return res.status(500).json({ message: 'Failed to upload logo', error: error.message });
    }
  });
};

/**
 * Delete a tenant logo
 * @param req Request
 * @param res Response
 */
export const deleteTenantLogo = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId } = req.user;

    // Get the current tenant
    const tenant = await prisma.tenant.findUnique({
      where: { id: tenantId }
    });

    if (!tenant) {
      return res.status(404).json({ message: 'Tenant not found' });
    }

    // If the tenant has a logo, delete the file
    if (tenant.logo) {
      try {
        const logoPath = path.join(process.cwd(), 'uploads', 'logos', tenant.logo);
        if (await fs.pathExists(logoPath)) {
          await fs.unlink(logoPath);
          logger.info('Deleted logo file', { path: logoPath });
        }
      } catch (error) {
        logger.error('Error deleting logo file', { error });
        // Continue even if we couldn't delete the file
      }

      // Update the tenant to remove the logo reference
      await prisma.tenant.update({
        where: { id: tenantId },
        data: { logo: null }
      });
    }

    logger.info('Tenant logo deleted successfully', { tenantId });
    return res.status(200).json({ message: 'Logo deleted successfully' });
  } catch (error: any) {
    logger.error('Error in deleteTenantLogo', { error: error.message, stack: error.stack });
    return res.status(500).json({ message: 'Failed to delete logo', error: error.message });
  }
};
