import { prisma } from '../index';
import { logger } from '../utils/logger';
import { emailService } from './email.service';
import { whatsappService } from './whatsapp.service';

/**
 * Notification Service
 * Centralized service for managing notifications across different channels
 */
export class NotificationService {
  /**
   * Get notification settings for a tenant
   * @param tenantId Tenant ID
   * @returns Notification settings
   */
  async getSettings(tenantId: string) {
    try {
      // Get settings from database
      const settings = await prisma.notificationSetting.findUnique({
        where: { tenantId },
      });

      // If settings don't exist, create default settings
      if (!settings) {
        return await prisma.notificationSetting.create({
          data: {
            tenantId,
            invoiceCreated: {
              enabled: true,
              primary: 'email',
              fallback: true,
              fallbackDelay: 24,
            },
            paymentReminder: {
              enabled: true,
              primary: 'email',
              fallback: false,
              fallbackDelay: 24,
            },
            paymentOverdue: {
              enabled: true,
              primary: 'email',
              escalation: true,
              escalationSchedule: [
                { days: 1, method: 'email' },
                { days: 3, method: 'both' },
                { days: 7, method: 'both' },
                { days: 14, method: 'both' },
              ],
            },
            paymentReceived: {
              enabled: true,
              primary: 'email',
              fallback: false,
            },
            businessHours: {
              restrictToBusinessHours: true,
              startTime: '09:00',
              endTime: '17:00',
              workDays: [1, 2, 3, 4, 5], // Monday to Friday
            },
          },
        });
      }

      return settings;
    } catch (error) {
      logger.error('Error getting notification settings:', error);
      throw error;
    }
  }

  /**
   * Update notification settings for a tenant
   * @param tenantId Tenant ID
   * @param settings Settings to update
   * @returns Updated settings
   */
  async updateSettings(tenantId: string, settings: any) {
    try {
      const existingSettings = await prisma.notificationSetting.findUnique({
        where: { tenantId },
      });

      if (existingSettings) {
        return await prisma.notificationSetting.update({
          where: { id: existingSettings.id },
          data: settings,
        });
      } else {
        return await prisma.notificationSetting.create({
          data: {
            tenantId,
            ...settings,
          },
        });
      }
    } catch (error) {
      logger.error('Error updating notification settings:', error);
      throw error;
    }
  }

  /**
   * Get customer notification preferences
   * @param customerId Customer ID
   * @returns Customer notification preferences
   */
  async getCustomerPreferences(customerId: string) {
    try {
      // Get preferences from database
      const preferences = await prisma.customerNotificationPreference.findUnique({
        where: { customerId },
      });

      // If preferences don't exist, create default preferences
      if (!preferences) {
        return await prisma.customerNotificationPreference.create({
          data: {
            customerId,
            preferredChannel: 'default',
            optOut: false,
            customReminderDays: false,
            reminderDaysBefore: 3,
          },
        });
      }

      return preferences;
    } catch (error) {
      logger.error('Error getting customer notification preferences:', error);
      throw error;
    }
  }

  /**
   * Update customer notification preferences
   * @param customerId Customer ID
   * @param preferences Preferences to update
   * @returns Updated preferences
   */
  async updateCustomerPreferences(customerId: string, preferences: any) {
    try {
      const existingPreferences = await prisma.customerNotificationPreference.findUnique({
        where: { customerId },
      });

      if (existingPreferences) {
        return await prisma.customerNotificationPreference.update({
          where: { id: existingPreferences.id },
          data: preferences,
        });
      } else {
        return await prisma.customerNotificationPreference.create({
          data: {
            customerId,
            ...preferences,
          },
        });
      }
    } catch (error) {
      logger.error('Error updating customer notification preferences:', error);
      throw error;
    }
  }

  /**
   * Get invoice notification settings
   * @param invoiceId Invoice ID
   * @returns Invoice notification settings
   */
  async getInvoiceNotificationSettings(invoiceId: string) {
    try {
      // Get settings from database
      const settings = await prisma.invoiceNotification.findUnique({
        where: { invoiceId },
      });

      // If settings don't exist, create default settings
      if (!settings) {
        return await prisma.invoiceNotification.create({
          data: {
            invoiceId,
            autoSend: true,
            channels: {
              email: true,
              whatsapp: false,
            },
            reminders: {
              enabled: true,
              beforeDue: 3,
              afterDue: 7,
            },
            escalation: {
              enabled: false,
              days: 14,
            },
          },
        });
      }

      return settings;
    } catch (error) {
      logger.error('Error getting invoice notification settings:', error);
      throw error;
    }
  }

  /**
   * Update invoice notification settings
   * @param invoiceId Invoice ID
   * @param settings Settings to update
   * @returns Updated settings
   */
  async updateInvoiceNotificationSettings(invoiceId: string, settings: any) {
    try {
      const existingSettings = await prisma.invoiceNotification.findUnique({
        where: { invoiceId },
      });

      if (existingSettings) {
        return await prisma.invoiceNotification.update({
          where: { id: existingSettings.id },
          data: settings,
        });
      } else {
        return await prisma.invoiceNotification.create({
          data: {
            invoiceId,
            ...settings,
          },
        });
      }
    } catch (error) {
      logger.error('Error updating invoice notification settings:', error);
      throw error;
    }
  }

  /**
   * Send notification
   * @param options Notification options
   * @returns Notification result
   */
  async sendNotification(options: {
    type: string;
    channel: string;
    recipient: string;
    recipientName?: string;
    subject?: string;
    content?: string;
    templateName?: string;
    templateData?: Record<string, any>;
    tenantId: string;
    relatedId?: string;
    relatedType?: string;
    attachments?: Array<{
      filename: string;
      path: string;
      contentType?: string;
    }>;
  }) {
    try {
      // Create notification record
      const notification = await prisma.notification.create({
        data: {
          type: options.type,
          channel: options.channel,
          recipient: options.recipient,
          recipientName: options.recipientName,
          subject: options.subject,
          content: options.content,
          status: 'queued',
          sentAt: new Date(),
          relatedId: options.relatedId,
          relatedType: options.relatedType,
          tenantId: options.tenantId,
        },
      });

      // Send notification based on channel
      let result: { success: boolean; messageId?: string; error?: string };

      if (options.channel === 'email') {
        // Send email
        if (options.templateName && options.templateData) {
          result = await emailService.sendTemplatedEmail({
            to: options.recipient,
            templateName: options.templateName,
            templateData: options.templateData,
            tenantId: options.tenantId,
            attachments: options.attachments,
          });
        } else if (options.subject && options.content) {
          result = await emailService.sendEmail({
            to: options.recipient,
            subject: options.subject,
            html: options.content,
            tenantId: options.tenantId,
            attachments: options.attachments,
          });
        } else {
          throw new Error('Email requires either template or subject/content');
        }
      } else if (options.channel === 'whatsapp') {
        // Send WhatsApp message
        if (options.templateName && options.templateData) {
          const message = await whatsappService.sendTemplateMessage(
            options.tenantId,
            options.recipient,
            options.templateName,
            options.templateData
          );
          result = { success: true, messageId: message.providerMessageId };
        } else if (options.content) {
          result = await whatsappService.sendMessage({
            to: options.recipient,
            body: options.content,
            tenantId: options.tenantId,
            mediaUrl: options.attachments?.[0]?.path,
          });
        } else {
          throw new Error('WhatsApp requires either template or content');
        }
      } else {
        throw new Error(`Unsupported channel: ${options.channel}`);
      }

      // Update notification record
      await prisma.notification.update({
        where: { id: notification.id },
        data: {
          status: result.success ? 'sent' : 'failed',
          error: result.error,
          updatedAt: new Date(),
        },
      });

      return {
        success: result.success,
        notificationId: notification.id,
        messageId: result.messageId,
        error: result.error,
      };
    } catch (error: any) {
      logger.error('Error sending notification:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Schedule notification
   * @param options Notification options
   * @returns Scheduled notification
   */
  async scheduleNotification(options: {
    type: string;
    channel: string;
    data: Record<string, any>;
    scheduledFor: Date;
    tenantId: string;
    relatedId?: string;
    relatedType?: string;
  }) {
    try {
      // Create notification queue record
      const queuedNotification = await prisma.notificationQueue.create({
        data: {
          type: options.type,
          channel: options.channel,
          data: JSON.stringify(options.data),
          scheduledFor: options.scheduledFor,
          status: 'PENDING',
          tenantId: options.tenantId,
          relatedId: options.relatedId,
          relatedType: options.relatedType,
        },
      });

      return queuedNotification;
    } catch (error) {
      logger.error('Error scheduling notification:', error);
      throw error;
    }
  }

  /**
   * Get notification history
   * @param tenantId Tenant ID
   * @param params Query parameters
   * @returns Notification history
   */
  async getNotificationHistory(
    tenantId: string,
    params: {
      page?: number;
      limit?: number;
      type?: string;
      channel?: string;
      status?: string;
      startDate?: Date;
      endDate?: Date;
      recipient?: string;
      relatedId?: string;
      relatedType?: string;
    }
  ) {
    try {
      const { page = 1, limit = 10 } = params;
      const skip = (page - 1) * limit;

      // Build where clause
      const where: any = { tenantId };

      if (params.type) {
        where.type = params.type;
      }

      if (params.channel) {
        where.channel = params.channel;
      }

      if (params.status) {
        where.status = params.status;
      }

      if (params.recipient) {
        where.recipient = { contains: params.recipient };
      }

      if (params.relatedId) {
        where.relatedId = params.relatedId;
      }

      if (params.relatedType) {
        where.relatedType = params.relatedType;
      }

      if (params.startDate || params.endDate) {
        where.sentAt = {};
        if (params.startDate) {
          where.sentAt.gte = params.startDate;
        }
        if (params.endDate) {
          where.sentAt.lte = params.endDate;
        }
      }

      // Get notifications
      const notifications = await prisma.notification.findMany({
        where,
        skip,
        take: limit,
        orderBy: { sentAt: 'desc' },
      });

      // Get total count
      const total = await prisma.notification.count({ where });

      return {
        data: notifications,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      };
    } catch (error) {
      logger.error('Error getting notification history:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const notificationService = new NotificationService();
export default notificationService;
