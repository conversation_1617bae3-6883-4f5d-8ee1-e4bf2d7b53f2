'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { apiClient } from '@/lib/api/client';
import { useToast } from '@/components/ui/use-toast';

export default function AccountSettingsPage() {
  const router = useRouter();
  const { user, logout } = useAuth();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isDeactivateDialogOpen, setIsDeactivateDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [confirmEmail, setConfirmEmail] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState<string | null>(null);

  const handleDeactivateAccount = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Validate email
      if (confirmEmail !== user?.email) {
        setError('Email address does not match your account');
        setIsLoading(false);
        return;
      }

      // In a real app, this would call the API to deactivate the account
      // await apiClient.post('/auth/deactivate', { password: confirmPassword });
      
      // Mock deactivation
      setTimeout(async () => {
        setIsDeactivateDialogOpen(false);
        
        toast({
          title: 'Account Deactivated',
          description: 'Your account has been deactivated. You can reactivate it by logging in again.',
          variant: 'default',
        });
        
        // Logout the user
        await logout();
        router.push('/login');
      }, 1000);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to deactivate account',
        variant: 'destructive',
      });
      setIsLoading(false);
    }
  };

  const handleDeleteAccount = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Validate email
      if (confirmEmail !== user?.email) {
        setError('Email address does not match your account');
        setIsLoading(false);
        return;
      }

      // In a real app, this would call the API to delete the account
      // await apiClient.delete('/auth/account', { data: { password: confirmPassword } });
      
      // Mock deletion
      setTimeout(async () => {
        setIsDeleteDialogOpen(false);
        
        toast({
          title: 'Account Deleted',
          description: 'Your account has been permanently deleted.',
          variant: 'default',
        });
        
        // Logout the user
        await logout();
        router.push('/');
      }, 1000);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete account',
        variant: 'destructive',
      });
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-2xl font-bold">Account Settings</h1>
        <p className="text-text-secondary">Manage your account settings and preferences</p>
      </div>

      {/* Data Export */}
      <Card className="border-none shadow-md">
        <CardHeader className="pb-2 border-b">
          <CardTitle className="text-lg font-bold text-text-primary">Data Export</CardTitle>
          <CardDescription className="text-text-secondary">
            Export all your data in a machine-readable format
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-text-secondary mb-4">
            You can export all your data including invoices, customers, and account information.
            The export will be delivered to your email address as a ZIP file.
          </p>
        </CardContent>
        <CardFooter>
          <Button variant="outline" size="sm">
            Request Data Export
          </Button>
        </CardFooter>
      </Card>

      {/* Account Deactivation */}
      <Card className="border-none shadow-md">
        <CardHeader className="pb-2 border-b">
          <CardTitle className="text-lg font-bold text-text-primary">Deactivate Account</CardTitle>
          <CardDescription className="text-text-secondary">
            Temporarily deactivate your account
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-text-secondary mb-4">
            Deactivating your account will hide your profile and suspend your access to the platform.
            You can reactivate your account at any time by logging in again.
          </p>
          <Alert variant="warning" className="bg-yellow-50 text-yellow-800 border-yellow-200">
            <AlertDescription>
              All your data will be preserved, but you won't be able to access the platform until you reactivate your account.
            </AlertDescription>
          </Alert>
        </CardContent>
        <CardFooter>
          <Dialog open={isDeactivateDialogOpen} onOpenChange={setIsDeactivateDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" className="text-yellow-600 border-yellow-200 hover:bg-yellow-50">
                Deactivate Account
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Deactivate Account</DialogTitle>
                <DialogDescription>
                  Are you sure you want to deactivate your account? You can reactivate it at any time by logging in again.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 mt-4">
                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}
                <Input
                  label="Confirm Email"
                  type="email"
                  placeholder="Enter your email address"
                  value={confirmEmail}
                  onChange={(e) => setConfirmEmail(e.target.value)}
                  helpText={`Type ${user?.email} to confirm`}
                  required
                />
                <Input
                  label="Password"
                  type="password"
                  placeholder="Enter your password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                />
              </div>
              <DialogFooter className="mt-6">
                <Button
                  variant="outline"
                  onClick={() => setIsDeactivateDialogOpen(false)}
                  disabled={isLoading}
                >
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleDeactivateAccount}
                  disabled={isLoading || confirmEmail !== user?.email || !confirmPassword}
                >
                  {isLoading ? 'Deactivating...' : 'Deactivate Account'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </CardFooter>
      </Card>

      {/* Account Deletion */}
      <Card className="border-none shadow-md">
        <CardHeader className="pb-2 border-b">
          <CardTitle className="text-lg font-bold text-text-primary">Delete Account</CardTitle>
          <CardDescription className="text-text-secondary">
            Permanently delete your account and all your data
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-text-secondary mb-4">
            Deleting your account will permanently remove all your data from our systems.
            This action cannot be undone.
          </p>
          <Alert variant="destructive">
            <AlertDescription>
              All your data will be permanently deleted. This action cannot be undone.
            </AlertDescription>
          </Alert>
        </CardContent>
        <CardFooter>
          <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="destructive">
                Delete Account
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Delete Account</DialogTitle>
                <DialogDescription>
                  Are you sure you want to delete your account? This action cannot be undone and all your data will be permanently deleted.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 mt-4">
                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}
                <Input
                  label="Confirm Email"
                  type="email"
                  placeholder="Enter your email address"
                  value={confirmEmail}
                  onChange={(e) => setConfirmEmail(e.target.value)}
                  helpText={`Type ${user?.email} to confirm`}
                  required
                />
                <Input
                  label="Password"
                  type="password"
                  placeholder="Enter your password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                />
              </div>
              <DialogFooter className="mt-6">
                <Button
                  variant="outline"
                  onClick={() => setIsDeleteDialogOpen(false)}
                  disabled={isLoading}
                >
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleDeleteAccount}
                  disabled={isLoading || confirmEmail !== user?.email || !confirmPassword}
                >
                  {isLoading ? 'Deleting...' : 'Delete Account Permanently'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </CardFooter>
      </Card>
    </div>
  );
}
