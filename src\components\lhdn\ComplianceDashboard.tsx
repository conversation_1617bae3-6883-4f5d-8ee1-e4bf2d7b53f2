'use client';

import { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface ComplianceStats {
  totalInvoices: number;
  validatedInvoices: number;
  pendingInvoices: number;
  failedInvoices: number;
  complianceRate: number;
  recentIssues: Array<{
    id: string;
    invoiceNumber: string;
    issue: string;
    date: string;
  }>;
  aiInsights: Array<{
    id: string;
    message: string;
    severity: 'info' | 'warning' | 'critical';
  }>;
}

interface ComplianceDashboardProps {
  className?: string;
}

export default function ComplianceDashboard({ className }: ComplianceDashboardProps) {
  const [stats, setStats] = useState<ComplianceStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'issues' | 'insights'>('overview');

  useEffect(() => {
    const fetchStats = async () => {
      setIsLoading(true);
      try {
        // In a real implementation, this would fetch from your API
        // For now, we'll use mock data
        setTimeout(() => {
          setStats({
            totalInvoices: 124,
            validatedInvoices: 98,
            pendingInvoices: 22,
            failedInvoices: 4,
            complianceRate: 79,
            recentIssues: [
              {
                id: '1',
                invoiceNumber: 'INV-2023-042',
                issue: 'Missing customer TIN',
                date: '2023-05-08',
              },
              {
                id: '2',
                invoiceNumber: 'INV-2023-039',
                issue: 'Invalid tax calculation',
                date: '2023-05-07',
              },
              {
                id: '3',
                invoiceNumber: 'INV-2023-036',
                issue: 'Missing item descriptions',
                date: '2023-05-05',
              },
            ],
            aiInsights: [
              {
                id: '1',
                message: 'Your compliance rate has improved by 12% this month. Keep up the good work!',
                severity: 'info',
              },
              {
                id: '2',
                message: 'Several invoices have missing customer TINs. Consider implementing mandatory TIN validation.',
                severity: 'warning',
              },
              {
                id: '3',
                message: 'Tax calculation errors detected in 3 invoices. Review your tax calculation logic.',
                severity: 'critical',
              },
            ],
          });
          setIsLoading(false);
        }, 1000);
      } catch (error) {
        console.error('Error fetching compliance stats:', error);
        setIsLoading(false);
      }
    };

    fetchStats();
  }, []);

  if (isLoading) {
    return (
      <div className={cn("bg-white rounded-lg shadow p-6", className)}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-20 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-40 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className={cn("bg-white rounded-lg shadow p-6", className)}>
        <p className="text-gray-500">Failed to load compliance data</p>
      </div>
    );
  }

  return (
    <div className={cn("bg-white rounded-lg shadow", className)}>
      <div className="p-6">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">LHDN Compliance Dashboard</h2>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-indigo-50 p-4 rounded-lg">
            <p className="text-sm text-indigo-600 font-medium">Total Invoices</p>
            <p className="text-2xl font-bold text-indigo-800">{stats.totalInvoices}</p>
          </div>
          
          <div className="bg-green-50 p-4 rounded-lg">
            <p className="text-sm text-green-600 font-medium">Validated</p>
            <p className="text-2xl font-bold text-green-800">{stats.validatedInvoices}</p>
          </div>
          
          <div className="bg-yellow-50 p-4 rounded-lg">
            <p className="text-sm text-yellow-600 font-medium">Pending</p>
            <p className="text-2xl font-bold text-yellow-800">{stats.pendingInvoices}</p>
          </div>
          
          <div className="bg-red-50 p-4 rounded-lg">
            <p className="text-sm text-red-600 font-medium">Failed</p>
            <p className="text-2xl font-bold text-red-800">{stats.failedInvoices}</p>
          </div>
        </div>
        
        <div className="border-b border-gray-200 mb-4">
          <nav className="flex -mb-px">
            <button
              onClick={() => setActiveTab('overview')}
              className={cn(
                "py-2 px-4 text-sm font-medium",
                activeTab === 'overview'
                  ? "border-b-2 border-indigo-500 text-indigo-600"
                  : "text-gray-500 hover:text-gray-700 hover:border-gray-300"
              )}
            >
              Overview
            </button>
            <button
              onClick={() => setActiveTab('issues')}
              className={cn(
                "py-2 px-4 text-sm font-medium",
                activeTab === 'issues'
                  ? "border-b-2 border-indigo-500 text-indigo-600"
                  : "text-gray-500 hover:text-gray-700 hover:border-gray-300"
              )}
            >
              Issues
            </button>
            <button
              onClick={() => setActiveTab('insights')}
              className={cn(
                "py-2 px-4 text-sm font-medium",
                activeTab === 'insights'
                  ? "border-b-2 border-indigo-500 text-indigo-600"
                  : "text-gray-500 hover:text-gray-700 hover:border-gray-300"
              )}
            >
              AI Insights
            </button>
          </nav>
        </div>
        
        {activeTab === 'overview' && (
          <div>
            <div className="mb-6">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium text-gray-700">Compliance Rate</h3>
                <span className="text-sm font-medium text-indigo-600">{stats.complianceRate}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div 
                  className={cn(
                    "h-2.5 rounded-full",
                    stats.complianceRate >= 80 ? "bg-green-600" :
                    stats.complianceRate >= 60 ? "bg-yellow-500" : "bg-red-600"
                  )}
                  style={{ width: `${stats.complianceRate}%` }}
                ></div>
              </div>
            </div>
            
            <div className="bg-indigo-50 p-4 rounded-lg border border-indigo-100">
              <h3 className="text-sm font-medium text-indigo-800 mb-2">AI Recommendation</h3>
              <p className="text-sm text-indigo-700">
                Based on your invoice patterns, we recommend validating invoices in batches at the end of each day to optimize API usage and improve compliance rates.
              </p>
            </div>
          </div>
        )}
        
        {activeTab === 'issues' && (
          <div className="space-y-4">
            {stats.recentIssues.length > 0 ? (
              stats.recentIssues.map((issue) => (
                <div key={issue.id} className="border border-gray-200 rounded-lg p-3">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="text-sm font-medium text-gray-800">{issue.invoiceNumber}</h4>
                      <p className="text-sm text-red-600 mt-1">{issue.issue}</p>
                    </div>
                    <span className="text-xs text-gray-500">{issue.date}</span>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-sm text-gray-500">No compliance issues found.</p>
            )}
          </div>
        )}
        
        {activeTab === 'insights' && (
          <div className="space-y-4">
            {stats.aiInsights.map((insight) => (
              <div 
                key={insight.id} 
                className={cn(
                  "p-4 rounded-lg border",
                  insight.severity === 'info' ? "bg-blue-50 border-blue-100" :
                  insight.severity === 'warning' ? "bg-yellow-50 border-yellow-100" :
                  "bg-red-50 border-red-100"
                )}
              >
                <div className="flex">
                  <span className="mr-2">
                    {insight.severity === 'info' ? (
                      <svg className="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    ) : insight.severity === 'warning' ? (
                      <svg className="w-5 h-5 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                      </svg>
                    ) : (
                      <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    )}
                  </span>
                  <p className={cn(
                    "text-sm",
                    insight.severity === 'info' ? "text-blue-700" :
                    insight.severity === 'warning' ? "text-yellow-700" :
                    "text-red-700"
                  )}>
                    {insight.message}
                  </p>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
