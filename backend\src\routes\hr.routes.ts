import express from 'express';
import { authenticateJWT } from '../middleware/auth';
import {
  createEmployee,
  getEmployees,
  getEmployee,
  updateEmployee,
  deleteEmployee,
  createSalaryRecord,
  getSalaryRecords,
  updateSalaryRecord,
  createAttendanceRecord,
  getAttendanceRecords,
  updateAttendanceRecord,
  createLeaveRequest,
  getLeaveRequests,
  updateLeaveRequest
} from '../controllers/hr.controller';

const router = express.Router();

// Apply authentication middleware to all HR routes
router.use(authenticateJWT);

// Employee routes
router.post('/employees', createEmployee);
router.get('/employees', getEmployees);
router.get('/employees/:id', getEmployee);
router.put('/employees/:id', updateEmployee);
router.delete('/employees/:id', deleteEmployee);

// Salary record routes
router.post('/salary', createSalaryRecord);
router.get('/salary', getSalaryRecords);
router.put('/salary/:id', updateSalaryRecord);

// Attendance record routes
router.post('/attendance', createAttendanceRecord);
router.get('/attendance', getAttendanceRecords);
router.put('/attendance/:id', updateAttendanceRecord);

// Leave request routes
router.post('/leave', createLeaveRequest);
router.get('/leave', getLeaveRequests);
router.put('/leave/:id', updateLeaveRequest);

export default router;
