'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import TemplateManager from '@/components/templates/TemplateManager';
import { Input } from '@/components/ui/input';

export default function TemplatesPage() {
  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between bg-white p-6 rounded-lg shadow-sm mb-6">
        <div>
          <h1 className="text-2xl font-bold text-text-primary">PDF Templates</h1>
          <p className="mt-1 text-sm text-text-secondary">
            Manage your PDF templates for invoices and other documents.
          </p>
        </div>
      </div>

      <Tabs defaultValue="templates" className="space-y-4">
        <TabsList className="mb-6 bg-white p-1 rounded-lg border">
          <TabsTrigger value="templates" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">Templates</TabsTrigger>
          <TabsTrigger value="preview" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">Preview</TabsTrigger>
        </TabsList>

        <TabsContent value="templates" className="space-y-4">
          <TemplateManager />
        </TabsContent>

        <TabsContent value="preview">
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <h2 className="text-lg font-medium mb-4">Template Preview</h2>
            <p className="text-text-secondary">
              Select an invoice and a template to preview how it will look.
            </p>
            <div className="mt-4">
              <p className="text-sm text-text-secondary">
                This feature will be available soon.
              </p>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
