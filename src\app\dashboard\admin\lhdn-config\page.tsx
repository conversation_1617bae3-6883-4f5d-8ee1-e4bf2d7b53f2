'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/Tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/Alert';
import { getLHDNConfig, updateLHDNConfig, testLHDNConfig } from '@/lib/api/lhdn-config';
import { Input } from '@/components/ui/input';

export default function LHDNConfigPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [config, setConfig] = useState({
    apiBaseUrl: 'https://sandbox.myinvois.hasil.gov.my/einvoicing',
    certificatePath: '/path/to/certificate.p12',
    certificatePassword: '',
  });

  const [testResult, setTestResult] = useState<{
    success: boolean;
    message: string;
  } | null>(null);

  useEffect(() => {
    const fetchConfig = async () => {
      setIsLoading(true);
      try {
        const config = await getLHDNConfig();
        setConfig({
          apiBaseUrl: config.apiBaseUrl || 'https://sandbox.myinvois.hasil.gov.my/einvoicing',
          certificatePath: config.certificatePath || '',
          certificatePassword: '', // Password is not returned for security reasons
          isActive: config.isActive || false,
        });
      } catch (error) {
        console.error('Error fetching LHDN configuration:', error);
        setError('Failed to load LHDN configuration');
      } finally {
        setIsLoading(false);
      }
    };

    fetchConfig();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setConfig(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setSaveSuccess(false);
    setError(null);

    try {
      // Save the configuration to the backend
      await updateLHDNConfig(config);

      setSaveSuccess(true);
    } catch (err: any) {
      console.error('Error saving LHDN configuration:', err);
      setError(err.message || 'Failed to save configuration');
    } finally {
      setIsLoading(false);
    }
  };

  const testConnection = async () => {
    setIsLoading(true);
    setTestResult(null);

    try {
      // Test the connection to the LHDN API
      const result = await testLHDNConfig();

      setTestResult({
        success: result.success,
        message: result.message,
      });
    } catch (error: any) {
      console.error('Error testing connection:', error);
      setTestResult({
        success: false,
        message: error.message || 'An error occurred while testing the connection',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-6">LHDN MyInvois Configuration</h1>

      <div className="grid gap-6">
        <Card className="border-none shadow-md">
          <CardHeader className="pb-2 border-b">
            <CardTitle className="text-lg font-bold text-text-primary">Platform-Level LHDN Configuration</CardTitle>
            <CardDescription className="text-text-secondary">
              Configure the LHDN MyInvois API integration for all tenants. This configuration will be used for all invoice submissions.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <Input
                label="API Base URL"
                name="apiBaseUrl"
                value={config.apiBaseUrl}
                onChange={handleChange}
                required
                disabled={isLoading}
              />

              <Input
                label="Certificate Path"
                name="certificatePath"
                value={config.certificatePath}
                onChange={handleChange}
                required
                disabled={isLoading}
                helperText="Path to the LHDN MyInvois digital certificate (.p12 file)"
              />

              <Input
                label="Certificate Password"
                type="password"
                name="certificatePassword"
                value={config.certificatePassword}
                onChange={handleChange}
                required
                disabled={isLoading}
                helperText="Password for the digital certificate"
              />

              {saveSuccess && (
                <Alert className="bg-green-50 border-green-200">
                  <AlertTitle className="text-green-800">Success</AlertTitle>
                  <AlertDescription className="text-green-700">
                    LHDN configuration saved successfully.
                  </AlertDescription>
                </Alert>
              )}

              {error && (
                <Alert className="bg-red-50 border-red-200">
                  <AlertTitle className="text-red-800">Error</AlertTitle>
                  <AlertDescription className="text-red-700">
                    {error}
                  </AlertDescription>
                </Alert>
              )}

              {testResult && (
                <Alert className={testResult.success ? "bg-green-50 border-green-200" : "bg-red-50 border-red-200"}>
                  <AlertTitle className={testResult.success ? "text-green-800" : "text-red-800"}>
                    {testResult.success ? "Connection Successful" : "Connection Failed"}
                  </AlertTitle>
                  <AlertDescription className={testResult.success ? "text-green-700" : "text-red-700"}>
                    {testResult.message}
                  </AlertDescription>
                </Alert>
              )}
            </form>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={testConnection}
              disabled={isLoading}
            >
              {isLoading ? 'Testing...' : 'Test Connection'}
            </Button>
            <Button
              type="submit"
              onClick={handleSubmit}
              disabled={isLoading}
            >
              {isLoading ? 'Saving...' : 'Save Configuration'}
            </Button>
          </CardFooter>
        </Card>

        <Card className="border-none shadow-md">
          <CardHeader className="pb-2 border-b">
            <CardTitle className="text-lg font-bold text-text-primary">How It Works</CardTitle>
            <CardDescription className="text-text-secondary">
              Understanding how the LHDN MyInvois integration works in your platform
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="overview">
              <TabsList className="mb-6 bg-white p-1 rounded-lg border">
                <TabsTrigger value="overview" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">Overview</TabsTrigger>
                <TabsTrigger value="technical" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">Technical Details</TabsTrigger>
                <TabsTrigger value="security" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=inactive]:text-indigo-600 rounded-md px-4 py-2 font-medium">Security</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-4">
                <p className="text-text-primary">
                  The LHDN MyInvois integration allows your platform to automatically validate and submit invoices to the Malaysian tax authority on behalf of your tenants.
                </p>
                <div className="bg-blue-50 p-4 rounded-md border border-blue-100">
                  <h3 className="text-sm font-medium text-blue-800 mb-2">Simplified User Experience</h3>
                  <p className="text-sm text-blue-700">
                    Your tenants only need to provide their business registration number during signup. The platform handles all the technical details of LHDN integration automatically.
                  </p>
                </div>
              </TabsContent>

              <TabsContent value="technical" className="space-y-4">
                <p className="text-text-primary">
                  The platform uses a single digital certificate to communicate with the LHDN MyInvois API for all tenants. This certificate is used to:
                </p>
                <ul className="list-disc pl-5 space-y-2 text-text-primary">
                  <li>Validate business registration numbers</li>
                  <li>Submit invoices in UBL 2.1 format</li>
                  <li>Check validation status</li>
                  <li>Retrieve document details</li>
                </ul>
              </TabsContent>

              <TabsContent value="security" className="space-y-4">
                <p className="text-text-primary">
                  The platform implements several security measures to protect the LHDN integration:
                </p>
                <ul className="list-disc pl-5 space-y-2 text-text-primary">
                  <li>The digital certificate is stored securely and never exposed to tenants</li>
                  <li>All communication with the LHDN API is encrypted using HTTPS</li>
                  <li>Access to this configuration page is restricted to platform administrators</li>
                  <li>Tenant data is kept separate and secure</li>
                </ul>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
