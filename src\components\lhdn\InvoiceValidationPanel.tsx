'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/Alert';
import { Spinner } from '@/components/ui/Spinner';
import { Badge } from '@/components/ui/Badge';
import { cn } from '@/lib/utils';

interface ValidationError {
  field: string;
  message: string;
}

interface ValidationResult {
  isValid: boolean;
  validationId?: string;
  errors?: ValidationError[];
  message?: string;
}

interface InvoiceValidationPanelProps {
  invoiceId?: string;
  isNewInvoice: boolean;
  onValidationComplete?: (result: ValidationResult) => void;
  className?: string;
}

export default function InvoiceValidationPanel({
  invoiceId,
  isNewInvoice,
  onValidationComplete,
  className,
}: InvoiceValidationPanelProps) {
  const [isValidating, setIsValidating] = useState(false);
  const [validationResult, setValidationResult] = useState<ValidationResult | null>(null);
  const [expanded, setExpanded] = useState(false);

  const validateInvoice = async () => {
    if (!invoiceId || isNewInvoice) {
      setValidationResult({
        isValid: false,
        message: 'Please save the invoice before validating with LHDN MyInvois',
      });
      return;
    }

    setIsValidating(true);
    setValidationResult(null);

    try {
      // Call the API to validate the invoice
      const response = await fetch(`/api/lhdn/validate/${invoiceId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (response.ok) {
        const result: ValidationResult = {
          isValid: true,
          validationId: data.validationId,
          message: data.message || 'Invoice validated successfully with LHDN MyInvois',
        };
        
        setValidationResult(result);
        
        if (onValidationComplete) {
          onValidationComplete(result);
        }
      } else {
        const result: ValidationResult = {
          isValid: false,
          errors: data.errors?.map((error: string) => ({
            field: 'general',
            message: error,
          })) || [{ field: 'general', message: data.message || 'Validation failed' }],
          message: data.message || 'Failed to validate invoice with LHDN MyInvois',
        };
        
        setValidationResult(result);
        
        if (onValidationComplete) {
          onValidationComplete(result);
        }
      }
    } catch (error) {
      console.error('Error validating invoice:', error);
      
      const result: ValidationResult = {
        isValid: false,
        errors: [{ field: 'general', message: 'An error occurred during validation' }],
        message: 'Failed to communicate with LHDN MyInvois API',
      };
      
      setValidationResult(result);
      
      if (onValidationComplete) {
        onValidationComplete(result);
      }
    } finally {
      setIsValidating(false);
    }
  };

  return (
    <Card className={cn("p-4", className)}>
      <div className="flex flex-col space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium">LHDN MyInvois Validation</h3>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setExpanded(!expanded)}
          >
            {expanded ? 'Hide Details' : 'Show Details'}
          </Button>
        </div>

        {validationResult && (
          <div className="mt-2">
            <Badge
              variant={validationResult.isValid ? "success" : "destructive"}
              className="mb-2"
            >
              {validationResult.isValid ? 'Valid' : 'Invalid'}
            </Badge>
            
            <p className="text-sm text-gray-600">{validationResult.message}</p>
            
            {validationResult.validationId && (
              <p className="text-xs text-gray-500 mt-1">
                Validation ID: {validationResult.validationId}
              </p>
            )}
          </div>
        )}

        {expanded && validationResult && !validationResult.isValid && validationResult.errors && (
          <Alert variant="destructive" className="mt-2">
            <AlertTitle>Validation Errors</AlertTitle>
            <AlertDescription>
              <ul className="list-disc pl-5 mt-2 space-y-1">
                {validationResult.errors.map((error, index) => (
                  <li key={index} className="text-sm">
                    {error.field !== 'general' && <span className="font-medium">{error.field}: </span>}
                    {error.message}
                  </li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        {expanded && (
          <div className="mt-2 text-sm text-gray-600">
            <p>
              LHDN MyInvois validation ensures your invoice complies with Malaysian tax regulations.
              Validated invoices are automatically submitted to LHDN for tax compliance.
            </p>
          </div>
        )}

        <div className="flex justify-end mt-4">
          <Button
            onClick={validateInvoice}
            disabled={isValidating || isNewInvoice || !invoiceId}
            className="relative"
          >
            {isValidating ? (
              <>
                <Spinner size="sm" className="mr-2" />
                Validating...
              </>
            ) : (
              'Validate with LHDN MyInvois'
            )}
          </Button>
        </div>
      </div>
    </Card>
  );
}
