-- Inventory Management Module

-- Create Product table if not exists
CREATE TABLE IF NOT EXISTS "Product" (
  "id" TEXT NOT NULL,
  "sku" TEXT NOT NULL,
  "name" TEXT NOT NULL,
  "description" TEXT,
  "category" TEXT,
  "unitPrice" DOUBLE PRECISION NOT NULL,
  "costPrice" DOUBLE PRECISION NOT NULL,
  "taxRate" DOUBLE PRECISION NOT NULL DEFAULT 0,
  "minStockLevel" INTEGER,
  "isActive" BOOLEAN NOT NULL DEFAULT true,
  "tenantId" TEXT NOT NULL,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  CONSTRAINT "Product_pkey" PRIMARY KEY ("id")
);

-- Create unique constraint on Product
ALTER TABLE "Product" ADD CONSTRAINT "Product_sku_tenantId_key" UNIQUE ("sku", "tenantId");

-- Create Warehouse table if not exists
CREATE TABLE IF NOT EXISTS "Warehouse" (
  "id" TEXT NOT NULL,
  "name" TEXT NOT NULL,
  "location" TEXT,
  "description" TEXT,
  "isActive" BOOLEAN NOT NULL DEFAULT true,
  "tenantId" TEXT NOT NULL,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  CONSTRAINT "Warehouse_pkey" PRIMARY KEY ("id")
);

-- Create unique constraint on Warehouse
ALTER TABLE "Warehouse" ADD CONSTRAINT "Warehouse_name_tenantId_key" UNIQUE ("name", "tenantId");

-- Create InventoryItem table if not exists
CREATE TABLE IF NOT EXISTS "InventoryItem" (
  "id" TEXT NOT NULL,
  "productId" TEXT NOT NULL,
  "warehouseId" TEXT NOT NULL,
  "quantity" DOUBLE PRECISION NOT NULL,
  "location" TEXT,
  "tenantId" TEXT NOT NULL,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  CONSTRAINT "InventoryItem_pkey" PRIMARY KEY ("id")
);

-- Create unique constraint on InventoryItem
ALTER TABLE "InventoryItem" ADD CONSTRAINT "InventoryItem_productId_warehouseId_tenantId_key" UNIQUE ("productId", "warehouseId", "tenantId");

-- Create InventoryTransaction table if not exists
CREATE TABLE IF NOT EXISTS "InventoryTransaction" (
  "id" TEXT NOT NULL,
  "type" "TransactionType" NOT NULL,
  "productId" TEXT NOT NULL,
  "warehouseId" TEXT NOT NULL,
  "inventoryItemId" TEXT NOT NULL,
  "quantity" DOUBLE PRECISION NOT NULL,
  "referenceType" TEXT,
  "referenceId" TEXT,
  "notes" TEXT,
  "tenantId" TEXT NOT NULL,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  CONSTRAINT "InventoryTransaction_pkey" PRIMARY KEY ("id")
);

-- Human Resources Module

-- Create Employee table if not exists
CREATE TABLE IF NOT EXISTS "Employee" (
  "id" TEXT NOT NULL,
  "employeeId" TEXT NOT NULL,
  "name" TEXT NOT NULL,
  "email" TEXT NOT NULL,
  "phone" TEXT,
  "address" TEXT,
  "position" TEXT,
  "department" TEXT,
  "joinDate" TIMESTAMP(3) NOT NULL,
  "terminationDate" TIMESTAMP(3),
  "isActive" BOOLEAN NOT NULL DEFAULT true,
  "tenantId" TEXT NOT NULL,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  CONSTRAINT "Employee_pkey" PRIMARY KEY ("id")
);

-- Create unique constraints on Employee
ALTER TABLE "Employee" ADD CONSTRAINT "Employee_email_tenantId_key" UNIQUE ("email", "tenantId");
ALTER TABLE "Employee" ADD CONSTRAINT "Employee_employeeId_tenantId_key" UNIQUE ("employeeId", "tenantId");

-- Create SalaryRecord table if not exists
CREATE TABLE IF NOT EXISTS "SalaryRecord" (
  "id" TEXT NOT NULL,
  "employeeId" TEXT NOT NULL,
  "month" INTEGER NOT NULL,
  "year" INTEGER NOT NULL,
  "basicSalary" DOUBLE PRECISION NOT NULL,
  "allowances" DOUBLE PRECISION NOT NULL DEFAULT 0,
  "deductions" DOUBLE PRECISION NOT NULL DEFAULT 0,
  "tax" DOUBLE PRECISION NOT NULL DEFAULT 0,
  "epf" DOUBLE PRECISION NOT NULL DEFAULT 0,
  "socso" DOUBLE PRECISION NOT NULL DEFAULT 0,
  "netSalary" DOUBLE PRECISION NOT NULL,
  "paymentDate" TIMESTAMP(3),
  "paymentStatus" "PaymentStatus" NOT NULL DEFAULT 'PENDING',
  "tenantId" TEXT NOT NULL,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  CONSTRAINT "SalaryRecord_pkey" PRIMARY KEY ("id")
);

-- Create unique constraint on SalaryRecord
ALTER TABLE "SalaryRecord" ADD CONSTRAINT "SalaryRecord_employeeId_month_year_tenantId_key" UNIQUE ("employeeId", "month", "year", "tenantId");

-- Create AttendanceRecord table if not exists
CREATE TABLE IF NOT EXISTS "AttendanceRecord" (
  "id" TEXT NOT NULL,
  "employeeId" TEXT NOT NULL,
  "date" TIMESTAMP(3) NOT NULL,
  "checkIn" TIMESTAMP(3),
  "checkOut" TIMESTAMP(3),
  "status" "AttendanceStatus" NOT NULL DEFAULT 'PRESENT',
  "notes" TEXT,
  "tenantId" TEXT NOT NULL,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  CONSTRAINT "AttendanceRecord_pkey" PRIMARY KEY ("id")
);

-- Create unique constraint on AttendanceRecord
ALTER TABLE "AttendanceRecord" ADD CONSTRAINT "AttendanceRecord_employeeId_date_tenantId_key" UNIQUE ("employeeId", "date", "tenantId");

-- Create LeaveRequest table if not exists
CREATE TABLE IF NOT EXISTS "LeaveRequest" (
  "id" TEXT NOT NULL,
  "employeeId" TEXT NOT NULL,
  "leaveType" "LeaveType" NOT NULL,
  "startDate" TIMESTAMP(3) NOT NULL,
  "endDate" TIMESTAMP(3) NOT NULL,
  "totalDays" DOUBLE PRECISION NOT NULL,
  "reason" TEXT,
  "status" "ApprovalStatus" NOT NULL DEFAULT 'PENDING',
  "approvedBy" TEXT,
  "approvalDate" TIMESTAMP(3),
  "tenantId" TEXT NOT NULL,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  CONSTRAINT "LeaveRequest_pkey" PRIMARY KEY ("id")
);
