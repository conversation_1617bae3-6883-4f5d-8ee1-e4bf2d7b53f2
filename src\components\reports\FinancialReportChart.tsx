'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface FinancialReportChartProps {
  reportType: string;
  dateRange: string;
  startDate?: string;
  endDate?: string;
}

export default function FinancialReportChart({ 
  reportType, 
  dateRange,
  startDate,
  endDate 
}: FinancialReportChartProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [chartView, setChartView] = useState<'chart' | 'table'>('chart');
  
  // Mock financial data
  const profitLossData = {
    revenue: [
      { category: 'Product Sales', amount: 120000 },
      { category: 'Service Revenue', amount: 85000 },
      { category: 'Other Income', amount: 15000 },
    ],
    expenses: [
      { category: 'Cost of Goods Sold', amount: 65000 },
      { category: 'Salaries', amount: 45000 },
      { category: 'Rent', amount: 12000 },
      { category: 'Utilities', amount: 5000 },
      { category: 'Marketing', amount: 8000 },
      { category: 'Other Expenses', amount: 10000 },
    ],
    summary: {
      totalRevenue: 220000,
      totalExpenses: 145000,
      grossProfit: 155000,
      netProfit: 75000,
      profitMargin: 34.1,
    },
    months: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    monthlyRevenue: [18000, 22000, 19000, 23000, 25000, 28000],
    monthlyExpenses: [12000, 14000, 13000, 15000, 16000, 17000],
    monthlyProfit: [6000, 8000, 6000, 8000, 9000, 11000],
  };
  
  const balanceSheetData = {
    assets: [
      { category: 'Cash and Cash Equivalents', amount: 85000 },
      { category: 'Accounts Receivable', amount: 45000 },
      { category: 'Inventory', amount: 65000 },
      { category: 'Property and Equipment', amount: 120000 },
      { category: 'Other Assets', amount: 25000 },
    ],
    liabilities: [
      { category: 'Accounts Payable', amount: 35000 },
      { category: 'Short-term Loans', amount: 25000 },
      { category: 'Long-term Debt', amount: 80000 },
      { category: 'Other Liabilities', amount: 15000 },
    ],
    equity: [
      { category: 'Owner\'s Capital', amount: 100000 },
      { category: 'Retained Earnings', amount: 85000 },
    ],
    summary: {
      totalAssets: 340000,
      totalLiabilities: 155000,
      totalEquity: 185000,
      debtToEquityRatio: 0.84,
      currentRatio: 2.43,
    },
  };
  
  const cashFlowData = {
    operating: [
      { category: 'Net Income', amount: 75000 },
      { category: 'Depreciation', amount: 15000 },
      { category: 'Changes in Working Capital', amount: -8000 },
    ],
    investing: [
      { category: 'Purchase of Equipment', amount: -25000 },
      { category: 'Sale of Assets', amount: 5000 },
    ],
    financing: [
      { category: 'Loan Repayments', amount: -12000 },
      { category: 'Owner Withdrawals', amount: -15000 },
    ],
    summary: {
      netOperatingCashFlow: 82000,
      netInvestingCashFlow: -20000,
      netFinancingCashFlow: -27000,
      netCashChange: 35000,
      beginningCashBalance: 50000,
      endingCashBalance: 85000,
    },
    months: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    monthlyCashFlow: [5000, 7000, 4000, 8000, 6000, 5000],
  };

  useEffect(() => {
    // Simulate loading data
    setIsLoading(true);
    
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1500);
    
    return () => clearTimeout(timer);
  }, [reportType, dateRange, startDate, endDate]);

  const getReportTitle = () => {
    switch (reportType) {
      case 'profit-loss':
        return 'Profit & Loss Statement';
      case 'balance-sheet':
        return 'Balance Sheet';
      case 'cash-flow':
        return 'Cash Flow Statement';
      default:
        return 'Financial Report';
    }
  };

  const getDateRangeText = () => {
    switch (dateRange) {
      case 'today':
        return 'Today';
      case 'yesterday':
        return 'Yesterday';
      case 'this-week':
        return 'This Week';
      case 'last-week':
        return 'Last Week';
      case 'this-month':
        return 'This Month';
      case 'last-month':
        return 'Last Month';
      case 'this-quarter':
        return 'This Quarter';
      case 'last-quarter':
        return 'Last Quarter';
      case 'this-year':
        return 'This Year';
      case 'last-year':
        return 'Last Year';
      case 'custom':
        return `${startDate} to ${endDate}`;
      default:
        return 'Custom Period';
    }
  };

  const renderProfitLossReport = () => {
    if (chartView === 'chart') {
      return (
        <div className="space-y-6">
          <div className="h-64 flex items-end justify-between px-4">
            {profitLossData.months.map((month, index) => (
              <div key={month} className="flex flex-col items-center">
                <div className="flex flex-col items-center space-y-1">
                  <div className="w-12 bg-green-500 rounded-t" style={{ height: `${profitLossData.monthlyRevenue[index] / 300}px` }}></div>
                  <div className="w-12 bg-red-500 rounded-t" style={{ height: `${profitLossData.monthlyExpenses[index] / 300}px` }}></div>
                  <div className="w-12 bg-blue-500 rounded-t" style={{ height: `${profitLossData.monthlyProfit[index] / 300}px` }}></div>
                </div>
                <div className="mt-2 text-xs font-medium">{month}</div>
              </div>
            ))}
          </div>
          <div className="flex justify-center space-x-4">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-green-500 rounded-full mr-1"></div>
              <span className="text-xs">Revenue</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-red-500 rounded-full mr-1"></div>
              <span className="text-xs">Expenses</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-blue-500 rounded-full mr-1"></div>
              <span className="text-xs">Profit</span>
            </div>
          </div>
        </div>
      );
    } else {
      return (
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-2">Revenue</h3>
            <div className="space-y-2">
              {profitLossData.revenue.map((item) => (
                <div key={item.category} className="flex justify-between">
                  <span>{item.category}</span>
                  <span className="font-medium">RM {item.amount.toLocaleString()}</span>
                </div>
              ))}
              <div className="flex justify-between font-bold pt-2 border-t">
                <span>Total Revenue</span>
                <span>RM {profitLossData.summary.totalRevenue.toLocaleString()}</span>
              </div>
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-medium mb-2">Expenses</h3>
            <div className="space-y-2">
              {profitLossData.expenses.map((item) => (
                <div key={item.category} className="flex justify-between">
                  <span>{item.category}</span>
                  <span className="font-medium">RM {item.amount.toLocaleString()}</span>
                </div>
              ))}
              <div className="flex justify-between font-bold pt-2 border-t">
                <span>Total Expenses</span>
                <span>RM {profitLossData.summary.totalExpenses.toLocaleString()}</span>
              </div>
            </div>
          </div>
          
          <div className="pt-2 border-t">
            <div className="flex justify-between font-bold">
              <span>Gross Profit</span>
              <span>RM {profitLossData.summary.grossProfit.toLocaleString()}</span>
            </div>
            <div className="flex justify-between font-bold">
              <span>Net Profit</span>
              <span>RM {profitLossData.summary.netProfit.toLocaleString()}</span>
            </div>
            <div className="flex justify-between text-sm mt-1">
              <span>Profit Margin</span>
              <span>{profitLossData.summary.profitMargin}%</span>
            </div>
          </div>
        </div>
      );
    }
  };

  const renderBalanceSheetReport = () => {
    if (chartView === 'chart') {
      return (
        <div className="space-y-6">
          <div className="h-64 flex items-center justify-center">
            <div className="w-64 h-64 relative">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <div className="text-lg font-bold">RM {balanceSheetData.summary.totalAssets.toLocaleString()}</div>
                  <div className="text-xs text-gray-500">Total Assets</div>
                </div>
              </div>
              <svg viewBox="0 0 100 100" className="w-full h-full">
                <circle cx="50" cy="50" r="40" fill="none" stroke="#e5e7eb" strokeWidth="10" />
                <circle 
                  cx="50" 
                  cy="50" 
                  r="40" 
                  fill="none" 
                  stroke="#3b82f6" 
                  strokeWidth="10" 
                  strokeDasharray="251.2" 
                  strokeDashoffset="0" 
                  transform="rotate(-90 50 50)" 
                />
                <circle 
                  cx="50" 
                  cy="50" 
                  r="40" 
                  fill="none" 
                  stroke="#ef4444" 
                  strokeWidth="10" 
                  strokeDasharray="251.2" 
                  strokeDashoffset={251.2 * (1 - balanceSheetData.summary.totalLiabilities / balanceSheetData.summary.totalAssets)} 
                  transform="rotate(-90 50 50)" 
                />
              </svg>
            </div>
          </div>
          <div className="flex justify-center space-x-4">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-blue-500 rounded-full mr-1"></div>
              <span className="text-xs">Equity: RM {balanceSheetData.summary.totalEquity.toLocaleString()}</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-red-500 rounded-full mr-1"></div>
              <span className="text-xs">Liabilities: RM {balanceSheetData.summary.totalLiabilities.toLocaleString()}</span>
            </div>
          </div>
        </div>
      );
    } else {
      return (
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-2">Assets</h3>
            <div className="space-y-2">
              {balanceSheetData.assets.map((item) => (
                <div key={item.category} className="flex justify-between">
                  <span>{item.category}</span>
                  <span className="font-medium">RM {item.amount.toLocaleString()}</span>
                </div>
              ))}
              <div className="flex justify-between font-bold pt-2 border-t">
                <span>Total Assets</span>
                <span>RM {balanceSheetData.summary.totalAssets.toLocaleString()}</span>
              </div>
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-medium mb-2">Liabilities</h3>
            <div className="space-y-2">
              {balanceSheetData.liabilities.map((item) => (
                <div key={item.category} className="flex justify-between">
                  <span>{item.category}</span>
                  <span className="font-medium">RM {item.amount.toLocaleString()}</span>
                </div>
              ))}
              <div className="flex justify-between font-bold pt-2 border-t">
                <span>Total Liabilities</span>
                <span>RM {balanceSheetData.summary.totalLiabilities.toLocaleString()}</span>
              </div>
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-medium mb-2">Equity</h3>
            <div className="space-y-2">
              {balanceSheetData.equity.map((item) => (
                <div key={item.category} className="flex justify-between">
                  <span>{item.category}</span>
                  <span className="font-medium">RM {item.amount.toLocaleString()}</span>
                </div>
              ))}
              <div className="flex justify-between font-bold pt-2 border-t">
                <span>Total Equity</span>
                <span>RM {balanceSheetData.summary.totalEquity.toLocaleString()}</span>
              </div>
            </div>
          </div>
          
          <div className="pt-2 border-t">
            <div className="flex justify-between text-sm">
              <span>Debt to Equity Ratio</span>
              <span>{balanceSheetData.summary.debtToEquityRatio}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Current Ratio</span>
              <span>{balanceSheetData.summary.currentRatio}</span>
            </div>
          </div>
        </div>
      );
    }
  };

  const renderCashFlowReport = () => {
    if (chartView === 'chart') {
      return (
        <div className="space-y-6">
          <div className="h-64 flex items-end justify-between px-4">
            {cashFlowData.months.map((month, index) => (
              <div key={month} className="flex flex-col items-center">
                <div 
                  className={`w-12 ${cashFlowData.monthlyCashFlow[index] >= 0 ? 'bg-green-500' : 'bg-red-500'} rounded-t`} 
                  style={{ height: `${Math.abs(cashFlowData.monthlyCashFlow[index]) / 100}px` }}
                ></div>
                <div className="mt-2 text-xs font-medium">{month}</div>
              </div>
            ))}
          </div>
          <div className="flex justify-center space-x-4">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-green-500 rounded-full mr-1"></div>
              <span className="text-xs">Positive Cash Flow</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-red-500 rounded-full mr-1"></div>
              <span className="text-xs">Negative Cash Flow</span>
            </div>
          </div>
        </div>
      );
    } else {
      return (
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-2">Operating Activities</h3>
            <div className="space-y-2">
              {cashFlowData.operating.map((item) => (
                <div key={item.category} className="flex justify-between">
                  <span>{item.category}</span>
                  <span className={`font-medium ${item.amount >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {item.amount >= 0 ? '+' : ''}{item.amount.toLocaleString()}
                  </span>
                </div>
              ))}
              <div className="flex justify-between font-bold pt-2 border-t">
                <span>Net Operating Cash Flow</span>
                <span className={cashFlowData.summary.netOperatingCashFlow >= 0 ? 'text-green-600' : 'text-red-600'}>
                  RM {cashFlowData.summary.netOperatingCashFlow.toLocaleString()}
                </span>
              </div>
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-medium mb-2">Investing Activities</h3>
            <div className="space-y-2">
              {cashFlowData.investing.map((item) => (
                <div key={item.category} className="flex justify-between">
                  <span>{item.category}</span>
                  <span className={`font-medium ${item.amount >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {item.amount >= 0 ? '+' : ''}{item.amount.toLocaleString()}
                  </span>
                </div>
              ))}
              <div className="flex justify-between font-bold pt-2 border-t">
                <span>Net Investing Cash Flow</span>
                <span className={cashFlowData.summary.netInvestingCashFlow >= 0 ? 'text-green-600' : 'text-red-600'}>
                  RM {cashFlowData.summary.netInvestingCashFlow.toLocaleString()}
                </span>
              </div>
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-medium mb-2">Financing Activities</h3>
            <div className="space-y-2">
              {cashFlowData.financing.map((item) => (
                <div key={item.category} className="flex justify-between">
                  <span>{item.category}</span>
                  <span className={`font-medium ${item.amount >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {item.amount >= 0 ? '+' : ''}{item.amount.toLocaleString()}
                  </span>
                </div>
              ))}
              <div className="flex justify-between font-bold pt-2 border-t">
                <span>Net Financing Cash Flow</span>
                <span className={cashFlowData.summary.netFinancingCashFlow >= 0 ? 'text-green-600' : 'text-red-600'}>
                  RM {cashFlowData.summary.netFinancingCashFlow.toLocaleString()}
                </span>
              </div>
            </div>
          </div>
          
          <div className="pt-2 border-t">
            <div className="flex justify-between font-bold">
              <span>Net Change in Cash</span>
              <span className={cashFlowData.summary.netCashChange >= 0 ? 'text-green-600' : 'text-red-600'}>
                RM {cashFlowData.summary.netCashChange.toLocaleString()}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Beginning Cash Balance</span>
              <span>RM {cashFlowData.summary.beginningCashBalance.toLocaleString()}</span>
            </div>
            <div className="flex justify-between font-bold">
              <span>Ending Cash Balance</span>
              <span>RM {cashFlowData.summary.endingCashBalance.toLocaleString()}</span>
            </div>
          </div>
        </div>
      );
    }
  };

  const renderReportContent = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          <span className="ml-2 text-gray-600">Loading report data...</span>
        </div>
      );
    }

    switch (reportType) {
      case 'profit-loss':
        return renderProfitLossReport();
      case 'balance-sheet':
        return renderBalanceSheetReport();
      case 'cash-flow':
        return renderCashFlowReport();
      default:
        return (
          <div className="flex items-center justify-center h-64">
            <p className="text-gray-500">Select a report type to view data</p>
          </div>
        );
    }
  };

  return (
    <Card className="h-full">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle>{getReportTitle()}</CardTitle>
            <CardDescription>{getDateRangeText()}</CardDescription>
          </div>
          <div className="flex space-x-2">
            <Button 
              variant={chartView === 'chart' ? 'default' : 'outline'} 
              size="sm"
              onClick={() => setChartView('chart')}
            >
              Chart
            </Button>
            <Button 
              variant={chartView === 'table' ? 'default' : 'outline'} 
              size="sm"
              onClick={() => setChartView('table')}
            >
              Table
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {renderReportContent()}
      </CardContent>
      <CardFooter className="flex justify-between">
        <div className="text-sm text-gray-500">
          Generated on {new Date().toLocaleDateString()}
        </div>
        <Button variant="outline" size="sm">
          Export
        </Button>
      </CardFooter>
    </Card>
  );
}
