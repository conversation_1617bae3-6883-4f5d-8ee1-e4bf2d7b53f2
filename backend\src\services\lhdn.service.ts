import axios from 'axios';
import crypto from 'crypto';
import fs from 'fs';
import path from 'path';
import config from '../config/config';
import { lhdnLogger as logger } from '../utils/logger';
import { withRetry } from '../utils/retry';
import { lhdnRateLimiters } from '../utils/rate-limiter';

// LHDN MyInvois API base URL - use the sandbox URL for development
const LHDN_API_BASE_URL = process.env.LHDN_API_BASE_URL || 'https://sandbox.myinvois.hasil.gov.my/einvoicing';

// Path to digital certificate files (in production, these would be securely stored)
const CERT_PATH = process.env.LHDN_CERT_PATH || path.join(__dirname, '../../certs');
const CERT_FILE = process.env.LHDN_CERT_FILE || 'myinvois-cert.p12';
const CERT_PASSWORD = process.env.LHDN_CERT_PASSWORD || '';

// Business registration information
const BUSINESS_REG_NO = process.env.BUSINESS_REG_NO || '';
const BUSINESS_NAME = process.env.BUSINESS_NAME || '';

// Interface for invoice data to be validated
interface InvoiceData {
  invoiceNumber: string;
  issueDate: Date;
  dueDate: Date;
  totalAmount: number;
  tax: number;
  businessName: string;
  businessTaxId?: string;
  customerName: string;
  customerTaxId?: string;
  items: {
    description: string;
    quantity: number;
    unitPrice: number;
    amount: number;
  }[];
}

// Interface for validation result
interface ValidationResult {
  isValid: boolean;
  validationId?: string;
  errors?: string[];
  message?: string;
}

import { getActiveCertificate, retrieveCertificate } from './certificate-storage.service';

/**
 * Load the digital certificate for LHDN API authentication
 * Uses the secure certificate storage service
 */
async function loadCertificate(): Promise<{ cert: Buffer | null; password: string }> {
  try {
    // Get the active certificate ID
    const certificateId = await getActiveCertificate();

    if (!certificateId) {
      logger.warn('No active certificate found');
      return { cert: null, password: '' };
    }

    // Retrieve the certificate data and password
    const { data, password } = await retrieveCertificate(certificateId);

    return {
      cert: data,
      password,
    };
  } catch (error: any) {
    logger.error('Error loading certificate:', { error: error.message, stack: error.stack });

    // Fallback to file-based certificate if available
    try {
      // Check if certificate file exists
      const certFilePath = path.join(CERT_PATH, CERT_FILE);
      if (fs.existsSync(certFilePath)) {
        logger.warn('Falling back to file-based certificate');
        return {
          cert: fs.readFileSync(certFilePath),
          password: CERT_PASSWORD,
        };
      }
    } catch (fallbackError: any) {
      logger.error('Error loading fallback certificate:', { error: fallbackError.message, stack: fallbackError.stack });
    }

    return { cert: null, password: '' };
  }
}

/**
 * Validate a business registration number
 * @param regNo Business registration number to validate
 */
export async function validateBusinessRegNo(regNo: string): Promise<any> {
  try {
    // Check rate limit
    if (!lhdnRateLimiters.validateBusinessRegNo.isAllowed('validateBusinessRegNo')) {
      logger.warn('Rate limit exceeded for validateBusinessRegNo', { regNo });
      throw new Error('Rate limit exceeded for business registration validation. Please try again later.');
    }

    // For development/testing without a certificate, use mock data
    const { cert } = await loadCertificate();
    if (!cert) {
      logger.warn('Certificate not set, using mock validation for business registration number', { regNo });

      // Mock implementation for development
      const mockValidBusinesses = [
        { regNo: '*********', name: 'ABC Company Sdn Bhd', type: 'COMPANY' },
        { regNo: '*********', name: 'XYZ Enterprise Sdn Bhd', type: 'COMPANY' },
        { regNo: '*********', name: 'Local Business Sdn Bhd', type: 'COMPANY' },
        { regNo: '*********', name: 'Global Services Sdn Bhd', type: 'COMPANY' },
        { regNo: '*********', name: 'Tech Solutions Sdn Bhd', type: 'COMPANY' },
      ];

      const foundBusiness = mockValidBusinesses.find(b => b.regNo === regNo);

      if (foundBusiness) {
        return {
          isValid: true,
          businessName: foundBusiness.name,
          businessType: foundBusiness.type,
        };
      } else {
        return {
          isValid: false,
          message: 'Business registration number not found',
        };
      }
    }

    // In a real implementation, this would:
    // 1. Use the platform's digital certificate to authenticate with LHDN
    // 2. Call the LHDN API to validate the business registration number
    // 3. Return the validation result

    logger.info('Using certificate to validate business registration number', { regNo });

    // Use retry mechanism for API calls
    return await withRetry(
      async () => {
        // In a real implementation, this would be an actual API call
        // For demonstration, we'll simulate a call with occasional failures

        // Simulate network issues (10% chance of failure)
        if (Math.random() < 0.1) {
          const error = new Error('Network error');
          (error as any).code = 'ECONNRESET';
          throw error;
        }

        // Simulate server errors (5% chance of failure)
        if (Math.random() < 0.05) {
          const error = new Error('Server error');
          (error as any).response = { status: 503 };
          throw error;
        }

        // Simulate a real API call with the certificate
        await new Promise(resolve => setTimeout(resolve, 500));

        // For demonstration, we'll validate some specific numbers
        if (['*********', '*********', '*********', '*********', '*********'].includes(regNo)) {
          return {
            isValid: true,
            businessName: `${regNo} Company Sdn Bhd`,
            businessType: 'COMPANY',
          };
        } else {
          return {
            isValid: false,
            message: 'Business registration number not found in LHDN records',
          };
        }
      },
      {
        maxRetries: 3,
        initialDelay: 1000,
        maxDelay: 5000,
        backoffFactor: 2,
      }
    );
  } catch (error: any) {
    logger.error('Error validating business registration number:', {
      regNo,
      error: error.message,
      stack: error.stack
    });
    throw new Error('Failed to validate business registration number');
  }
}

/**
 * Convert invoice to LHDN UBL 2.1 format according to MyInvois specifications
 * @param invoiceData Invoice data to convert
 */
function convertToUBL(invoiceData: InvoiceData): any {
  // This is a simplified version - in production, you'd need to implement
  // the full UBL 2.1 schema according to LHDN requirements
  const ublInvoice = {
    Invoice: {
      ID: invoiceData.invoiceNumber,
      IssueDate: invoiceData.issueDate.toISOString().split('T')[0],
      DueDate: invoiceData.dueDate.toISOString().split('T')[0],
      InvoiceTypeCode: '380', // Standard invoice code in UBL
      DocumentCurrencyCode: 'MYR',
      AccountingSupplierParty: {
        Party: {
          PartyIdentification: {
            ID: {
              value: invoiceData.businessTaxId || '',
              schemeID: 'TIN',
            },
          },
          PartyName: {
            Name: invoiceData.businessName || '',
          },
          PostalAddress: {
            StreetName: '',
            CityName: '',
            PostalZone: '',
            Country: {
              IdentificationCode: 'MY',
            },
          },
        },
      },
      AccountingCustomerParty: {
        Party: {
          PartyIdentification: {
            ID: {
              value: invoiceData.customerTaxId || '',
              schemeID: 'TIN',
            },
          },
          PartyName: {
            Name: invoiceData.customerName || '',
          },
          PostalAddress: {
            StreetName: '',
            CityName: '',
            PostalZone: '',
            Country: {
              IdentificationCode: 'MY',
            },
          },
        },
      },
      InvoiceLines: invoiceData.items.map((item, index) => ({
        ID: index + 1,
        InvoicedQuantity: {
          value: item.quantity,
          unitCode: 'EA', // Each
        },
        LineExtensionAmount: {
          value: item.amount,
          currencyID: 'MYR',
        },
        Item: {
          Description: item.description,
          ClassifiedTaxCategory: {
            ID: 'S', // Standard rate
            Percent: 0, // Tax rate
            TaxScheme: {
              ID: 'GST',
            },
          },
        },
        Price: {
          PriceAmount: {
            value: item.unitPrice,
            currencyID: 'MYR',
          },
        },
      })),
      TaxTotal: {
        TaxAmount: {
          value: invoiceData.tax || 0,
          currencyID: 'MYR',
        },
      },
      LegalMonetaryTotal: {
        LineExtensionAmount: {
          value: invoiceData.totalAmount - (invoiceData.tax || 0),
          currencyID: 'MYR',
        },
        TaxExclusiveAmount: {
          value: invoiceData.totalAmount - (invoiceData.tax || 0),
          currencyID: 'MYR',
        },
        TaxInclusiveAmount: {
          value: invoiceData.totalAmount,
          currencyID: 'MYR',
        },
        PayableAmount: {
          value: invoiceData.totalAmount,
          currencyID: 'MYR',
        },
      },
    },
  };

  return ublInvoice;
}

// LHDN MyInvois validation service
// This implementation can switch between mock and real API calls
export const validateInvoice = async (invoiceData: InvoiceData): Promise<ValidationResult> => {
  try {
    // Check rate limit
    if (!lhdnRateLimiters.validateInvoice.isAllowed('validateInvoice')) {
      logger.warn('Rate limit exceeded for validateInvoice', {
        invoiceNumber: invoiceData.invoiceNumber
      });
      return {
        isValid: false,
        errors: ['Rate limit exceeded for invoice validation. Please try again later.'],
        message: 'Rate limit exceeded for invoice validation',
      };
    }

    logger.info('Validating invoice with LHDN MyInvois', {
      invoiceNumber: invoiceData.invoiceNumber,
      issueDate: invoiceData.issueDate,
      totalAmount: invoiceData.totalAmount,
      customerName: invoiceData.customerName
    });

    // For development/testing without a certificate, perform local validation
    const { cert } = await loadCertificate();
    if (!cert) {
      logger.warn('Certificate not set, using local validation', {
        invoiceNumber: invoiceData.invoiceNumber
      });

      // Perform basic validation
      const errors: string[] = [];

      // Check required fields
      if (!invoiceData.invoiceNumber) {
        errors.push('Invoice number is required');
      }

      if (!invoiceData.issueDate) {
        errors.push('Issue date is required');
      }

      if (!invoiceData.dueDate) {
        errors.push('Due date is required');
      }

      if (!invoiceData.totalAmount || invoiceData.totalAmount <= 0) {
        errors.push('Total amount must be greater than zero');
      }

      if (!invoiceData.businessName) {
        errors.push('Business name is required');
      }

      if (!invoiceData.customerName) {
        errors.push('Customer name is required');
      }

      if (!invoiceData.items || invoiceData.items.length === 0) {
        errors.push('At least one invoice item is required');
      } else {
        // Check each item
        for (const item of invoiceData.items) {
          if (!item.description) {
            errors.push('Item description is required');
          }

          if (!item.quantity || item.quantity <= 0) {
            errors.push('Item quantity must be greater than zero');
          }

          if (!item.unitPrice || item.unitPrice <= 0) {
            errors.push('Item unit price must be greater than zero');
          }

          // Verify amount calculation
          const calculatedAmount = item.quantity * item.unitPrice;
          if (Math.abs(item.amount - calculatedAmount) > 0.01) {
            errors.push(`Item amount (${item.amount}) does not match quantity * unitPrice (${calculatedAmount})`);
          }
        }
      }

      // Verify total amount calculation
      if (invoiceData.items && invoiceData.items.length > 0) {
        const calculatedTotal = invoiceData.items.reduce(
          (sum, item) => sum + item.amount,
          0
        );

        // Add tax if applicable
        const totalWithTax = invoiceData.tax ? calculatedTotal + invoiceData.tax : calculatedTotal;

        if (Math.abs(invoiceData.totalAmount - totalWithTax) > 0.01) {
          errors.push(`Total amount (${invoiceData.totalAmount}) does not match sum of items plus tax (${totalWithTax})`);
        }
      }

      // Return validation result
      if (errors.length > 0) {
        return {
          isValid: false,
          errors,
          message: 'Invoice validation failed',
        };
      }

      // Mock successful validation
      return {
        isValid: true,
        validationId: `LHDN-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
        message: 'Invoice validated successfully',
      };
    }

    // Real API implementation using digital certificate
    try {
      // Convert invoice to UBL format according to MyInvois specifications
      const ublInvoice = convertToUBL(invoiceData);

      // Convert to XML string (MyInvois requires UBL in XML format)
      // In a real implementation, you would use a proper XML builder library
      const xmlString = `<?xml version="1.0" encoding="UTF-8"?>
      <!-- This is a simplified example. In production, use a proper XML builder -->
      <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2">
        <!-- Invoice content would go here -->
      </Invoice>`;

      // Create document hash (SHA-256)
      const documentHash = crypto
        .createHash('sha256')
        .update(xmlString)
        .digest('hex');

      // In a real implementation, you would:
      // 1. Sign the document with your digital certificate
      // 2. Submit it to the LHDN MyInvois API using HTTPS with client certificate authentication
      // 3. Process the response

      // For development purposes, we'll simulate a successful validation
      logger.info('Simulating LHDN MyInvois API call with certificate authentication', {
        invoiceNumber: invoiceData.invoiceNumber,
        documentHash
      });

      // Mock successful validation
      return {
        isValid: true,
        validationId: `LHDN-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
        message: 'Invoice validated successfully with LHDN MyInvois',
      };
    } catch (apiError: any) {
      logger.error('Error calling LHDN API:', {
        invoiceNumber: invoiceData.invoiceNumber,
        error: apiError.message,
        stack: apiError.stack
      });
      return {
        isValid: false,
        errors: [apiError.message || 'Error communicating with LHDN API'],
        message: 'Invoice validation failed due to API error',
      };
    }
  } catch (error: any) {
    logger.error('Error validating invoice with LHDN MyInvois:', {
      invoiceNumber: invoiceData.invoiceNumber,
      error: error.message,
      stack: error.stack
    });
    return {
      isValid: false,
      errors: [error.message || 'Unknown error occurred during validation'],
      message: 'Invoice validation failed due to an error',
    };
  }
};

/**
 * Get document status from LHDN
 * @param documentId LHDN document ID
 */
async function getDocumentStatus(documentId: string): Promise<any> {
  try {
    // Check rate limit
    if (!lhdnRateLimiters.getDocumentStatus.isAllowed('getDocumentStatus')) {
      logger.warn('Rate limit exceeded for getDocumentStatus', { documentId });
      throw new Error('Rate limit exceeded for document status check. Please try again later.');
    }

    // For development/testing without a certificate, return mock data
    const { cert } = await loadCertificate();
    if (!cert) {
      logger.warn('Certificate not set, returning mock document status', { documentId });
      return {
        documentId,
        status: 'VALID',
        timestamp: new Date().toISOString(),
      };
    }

    // In a real implementation, you would:
    // 1. Make an HTTPS request to the LHDN MyInvois API with client certificate authentication
    // 2. Process the response

    // For development purposes, we'll return mock data
    return {
      documentId,
      status: 'VALID',
      timestamp: new Date().toISOString(),
    };
  } catch (error: any) {
    logger.error('Error getting document status from LHDN:', {
      documentId,
      error: error.message,
      stack: error.stack
    });
    throw new Error('Failed to get document status from LHDN');
  }
}

/**
 * Cancel a document in LHDN MyInvois
 * @param documentId LHDN document ID
 * @param reason Reason for cancellation
 */
export async function cancelDocument(documentId: string, reason: string): Promise<any> {
  try {
    // Check rate limit
    if (!lhdnRateLimiters.cancelDocument.isAllowed('cancelDocument')) {
      logger.warn('Rate limit exceeded for cancelDocument', { documentId });
      throw new Error('Rate limit exceeded for document cancellation. Please try again later.');
    }

    // For development/testing without a certificate, return mock data
    const { cert } = await loadCertificate();
    if (!cert) {
      logger.warn('Certificate not set, returning mock cancellation result', { documentId, reason });
      return {
        success: true,
        documentId,
        status: 'CANCELLED',
        timestamp: new Date().toISOString(),
        message: 'Document cancelled successfully (mock)',
      };
    }

    // In a real implementation, you would:
    // 1. Make an HTTPS request to the LHDN MyInvois API with client certificate authentication
    // 2. Process the response

    // For development purposes, we'll return mock data
    return {
      success: true,
      documentId,
      status: 'CANCELLED',
      timestamp: new Date().toISOString(),
      message: 'Document cancelled successfully',
    };
  } catch (error: any) {
    logger.error('Error cancelling document:', {
      documentId,
      reason,
      error: error.message,
      stack: error.stack
    });
    throw new Error('Failed to cancel document');
  }
}

/**
 * Reject a document in LHDN MyInvois
 * @param documentId LHDN document ID
 * @param reason Reason for rejection
 */
export async function rejectDocument(documentId: string, reason: string): Promise<any> {
  try {
    // Check rate limit
    if (!lhdnRateLimiters.rejectDocument.isAllowed('rejectDocument')) {
      logger.warn('Rate limit exceeded for rejectDocument', { documentId });
      throw new Error('Rate limit exceeded for document rejection. Please try again later.');
    }

    // For development/testing without a certificate, return mock data
    const { cert } = await loadCertificate();
    if (!cert) {
      logger.warn('Certificate not set, returning mock rejection result', { documentId, reason });
      return {
        success: true,
        documentId,
        status: 'REJECTED',
        timestamp: new Date().toISOString(),
        message: 'Document rejected successfully (mock)',
      };
    }

    // In a real implementation, you would:
    // 1. Make an HTTPS request to the LHDN MyInvois API with client certificate authentication
    // 2. Process the response

    // For development purposes, we'll return mock data
    return {
      success: true,
      documentId,
      status: 'REJECTED',
      timestamp: new Date().toISOString(),
      message: 'Document rejected successfully',
    };
  } catch (error: any) {
    logger.error('Error rejecting document:', {
      documentId,
      reason,
      error: error.message,
      stack: error.stack
    });
    throw new Error('Failed to reject document');
  }
}

/**
 * Search documents in LHDN MyInvois
 * @param params Search parameters
 */
export async function searchDocuments(params: {
  startDate?: string;
  endDate?: string;
  status?: string;
  documentType?: string;
  page?: number;
  limit?: number;
}): Promise<any> {
  try {
    // Check rate limit
    if (!lhdnRateLimiters.searchDocuments.isAllowed('searchDocuments')) {
      logger.warn('Rate limit exceeded for searchDocuments', { params });
      throw new Error('Rate limit exceeded for document search. Please try again later.');
    }

    // For development/testing without a certificate, return mock data
    const { cert } = await loadCertificate();
    if (!cert) {
      logger.warn('Certificate not set, returning mock search results', { params });
      return {
        documents: [
          {
            documentId: 'DOC-001',
            documentNumber: 'INV-2025-001',
            documentType: 'INVOICE',
            issueDate: '2025-01-15',
            status: 'VALID',
            customerName: 'Acme Corporation',
            totalAmount: 2500.00,
          },
          {
            documentId: 'DOC-002',
            documentNumber: 'INV-2025-002',
            documentType: 'INVOICE',
            issueDate: '2025-01-20',
            status: 'VALID',
            customerName: 'Wayne Enterprises',
            totalAmount: 4200.00,
          },
          {
            documentId: 'DOC-003',
            documentNumber: 'INV-2025-003',
            documentType: 'INVOICE',
            issueDate: '2025-01-25',
            status: 'CANCELLED',
            customerName: 'Stark Industries',
            totalAmount: 1800.00,
          },
        ],
        pagination: {
          page: params.page || 1,
          limit: params.limit || 10,
          total: 3,
          totalPages: 1,
        },
      };
    }

    // In a real implementation, you would:
    // 1. Make an HTTPS request to the LHDN MyInvois API with client certificate authentication
    // 2. Process the response

    // For development purposes, we'll return mock data
    return {
      documents: [
        {
          documentId: 'DOC-001',
          documentNumber: 'INV-2025-001',
          documentType: 'INVOICE',
          issueDate: '2025-01-15',
          status: 'VALID',
          customerName: 'Acme Corporation',
          totalAmount: 2500.00,
        },
        {
          documentId: 'DOC-002',
          documentNumber: 'INV-2025-002',
          documentType: 'INVOICE',
          issueDate: '2025-01-20',
          status: 'VALID',
          customerName: 'Wayne Enterprises',
          totalAmount: 4200.00,
        },
        {
          documentId: 'DOC-003',
          documentNumber: 'INV-2025-003',
          documentType: 'INVOICE',
          issueDate: '2025-01-25',
          status: 'CANCELLED',
          customerName: 'Stark Industries',
          totalAmount: 1800.00,
        },
      ],
      pagination: {
        page: params.page || 1,
        limit: params.limit || 10,
        total: 3,
        totalPages: 1,
      },
    };
  } catch (error: any) {
    logger.error('Error searching documents:', {
      params,
      error: error.message,
      stack: error.stack
    });
    throw new Error('Failed to search documents');
  }
}