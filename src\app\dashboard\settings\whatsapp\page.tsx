'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function WhatsAppSettingsPage() {
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [config, setConfig] = useState({
    provider: 'twilio',
    twilio: {
      accountSid: '',
      authToken: '',
      phoneNumber: '',
    },
    templates: [
      {
        id: 'invoice_created',
        name: 'Invoice Created',
        content: 'Hello {{1}}, your invoice #{{2}} for {{3}} has been created. Due date: {{4}}. View it here: {{5}}',
        status: 'approved',
      },
      {
        id: 'payment_reminder',
        name: 'Payment Reminder',
        content: 'Hello {{1}}, this is a reminder that invoice #{{2}} for {{3}} is due on {{4}}. Please make payment at your earliest convenience.',
        status: 'approved',
      },
      {
        id: 'payment_overdue',
        name: 'Payment Overdue',
        content: 'Hello {{1}}, invoice #{{2}} for {{3}} was due on {{4}} and is now overdue. Please make payment as soon as possible.',
        status: 'pending',
      },
      {
        id: 'payment_received',
        name: 'Payment Received',
        content: 'Hello {{1}}, we have received your payment of {{2}} for invoice #{{3}}. Thank you for your business!',
        status: 'approved',
      },
    ],
    settings: {
      enableWhatsApp: true,
      businessHoursOnly: true,
      businessHoursStart: '09:00',
      businessHoursEnd: '17:00',
      businessDays: {
        monday: true,
        tuesday: true,
        wednesday: true,
        thursday: true,
        friday: true,
        saturday: false,
        sunday: false,
      },
    }
  });

  const handleProviderChange = (provider: string) => {
    setConfig({
      ...config,
      provider,
    });
  };

  const handleTwilioChange = (field: string, value: string) => {
    setConfig({
      ...config,
      twilio: {
        ...config.twilio,
        [field]: value,
      },
    });
  };

  const handleSettingChange = (field: string, value: any) => {
    setConfig({
      ...config,
      settings: {
        ...config.settings,
        [field]: value,
      },
    });
  };

  const handleBusinessDayChange = (day: string, checked: boolean) => {
    setConfig({
      ...config,
      settings: {
        ...config.settings,
        businessDays: {
          ...config.settings.businessDays,
          [day]: checked,
        },
      },
    });
  };

  const handleConnect = async () => {
    setIsConnecting(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    setIsConnected(true);
    setIsConnecting(false);
    setSuccessMessage('WhatsApp Business API connected successfully');
    
    // Clear success message after 3 seconds
    setTimeout(() => {
      setSuccessMessage(null);
    }, 3000);
  };

  const handleSaveConfig = async () => {
    // In a real implementation, this would call your API
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    
    setSuccessMessage('WhatsApp configuration saved successfully');
    
    // Clear success message after 3 seconds
    setTimeout(() => {
      setSuccessMessage(null);
    }, 3000);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between bg-white p-6 rounded-lg shadow-sm mb-6">
        <div>
          <h1 className="text-2xl font-bold text-text-primary">WhatsApp Settings</h1>
          <p className="mt-1 text-sm text-text-secondary">
            Configure WhatsApp Business API for invoice notifications and reminders
          </p>
        </div>
      </div>

      {successMessage && (
        <Alert className="bg-green-50 text-green-800 border-green-200">
          <AlertDescription>{successMessage}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="connection" className="space-y-6">
        <TabsList>
          <TabsTrigger value="connection">Connection</TabsTrigger>
          <TabsTrigger value="templates">Message Templates</TabsTrigger>
          <TabsTrigger value="settings">Notification Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="connection">
          <Card>
            <CardHeader>
              <CardTitle>WhatsApp Business API Connection</CardTitle>
              <CardDescription>
                Connect to WhatsApp Business API to send notifications
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Provider</Label>
                <div className="flex space-x-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="twilio"
                      name="provider"
                      value="twilio"
                      checked={config.provider === 'twilio'}
                      onChange={() => handleProviderChange('twilio')}
                      className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                    />
                    <Label htmlFor="twilio">Twilio</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="direct"
                      name="provider"
                      value="direct"
                      checked={config.provider === 'direct'}
                      onChange={() => handleProviderChange('direct')}
                      className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                    />
                    <Label htmlFor="direct">Direct API</Label>
                  </div>
                </div>
              </div>

              {config.provider === 'twilio' && (
                <div className="space-y-4 pt-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="accountSid">Twilio Account SID</Label>
                      <Input
                        id="accountSid"
                        value={config.twilio.accountSid}
                        onChange={(e) => handleTwilioChange('accountSid', e.target.value)}
                        placeholder="AC1234567890abcdef1234567890abcdef"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="authToken">Twilio Auth Token</Label>
                      <Input
                        id="authToken"
                        type="password"
                        value={config.twilio.authToken}
                        onChange={(e) => handleTwilioChange('authToken', e.target.value)}
                        placeholder="••••••••••••••••••••••••••••••"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phoneNumber">WhatsApp Phone Number</Label>
                    <Input
                      id="phoneNumber"
                      value={config.twilio.phoneNumber}
                      onChange={(e) => handleTwilioChange('phoneNumber', e.target.value)}
                      placeholder="+601234567890"
                    />
                    <p className="text-sm text-gray-500">
                      This must be a WhatsApp Business number registered with Twilio
                    </p>
                  </div>
                </div>
              )}

              {config.provider === 'direct' && (
                <div className="pt-4">
                  <Alert>
                    <AlertDescription>
                      Direct WhatsApp Business API integration requires approval from Meta. Please contact support for assistance.
                    </AlertDescription>
                  </Alert>
                </div>
              )}
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button
                variant="outline"
                onClick={handleConnect}
                disabled={isConnecting || (config.provider === 'twilio' && (!config.twilio.accountSid || !config.twilio.authToken || !config.twilio.phoneNumber))}
              >
                {isConnecting ? 'Connecting...' : isConnected ? 'Reconnect' : 'Connect'}
              </Button>
              <Button onClick={handleSaveConfig}>Save Configuration</Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="templates">
          <Card>
            <CardHeader>
              <CardTitle>WhatsApp Message Templates</CardTitle>
              <CardDescription>
                Configure templates for different notification types
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {config.templates.map((template) => (
                  <div key={template.id} className="p-4 border rounded-md">
                    <div className="flex justify-between items-start">
                      <div>
                        <div className="flex items-center space-x-2">
                          <h3 className="font-medium">{template.name}</h3>
                          <span className={`text-xs px-2 py-0.5 rounded-full ${
                            template.status === 'approved' 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {template.status === 'approved' ? 'Approved' : 'Pending'}
                          </span>
                        </div>
                        <p className="text-sm text-gray-500 mt-1">{template.content}</p>
                      </div>
                      <Button variant="outline" size="sm">
                        Edit
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSaveConfig}>Save Templates</Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="settings">
          <Card>
            <CardHeader>
              <CardTitle>WhatsApp Notification Settings</CardTitle>
              <CardDescription>
                Configure when and how WhatsApp notifications are sent
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="enableWhatsApp">Enable WhatsApp Notifications</Label>
                  <p className="text-sm text-gray-500">
                    Turn on/off all WhatsApp notifications
                  </p>
                </div>
                <Switch
                  id="enableWhatsApp"
                  checked={config.settings.enableWhatsApp}
                  onCheckedChange={(checked) => handleSettingChange('enableWhatsApp', checked)}
                />
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="businessHoursOnly">Business Hours Only</Label>
                  <p className="text-sm text-gray-500">
                    Only send WhatsApp messages during business hours
                  </p>
                </div>
                <Switch
                  id="businessHoursOnly"
                  checked={config.settings.businessHoursOnly}
                  onCheckedChange={(checked) => handleSettingChange('businessHoursOnly', checked)}
                />
              </div>

              {config.settings.businessHoursOnly && (
                <div className="space-y-4 pl-6 border-l-2 border-gray-100">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="businessHoursStart">Start Time</Label>
                      <Input
                        id="businessHoursStart"
                        type="time"
                        value={config.settings.businessHoursStart}
                        onChange={(e) => handleSettingChange('businessHoursStart', e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="businessHoursEnd">End Time</Label>
                      <Input
                        id="businessHoursEnd"
                        type="time"
                        value={config.settings.businessHoursEnd}
                        onChange={(e) => handleSettingChange('businessHoursEnd', e.target.value)}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm">Business Days</Label>
                    <div className="grid grid-cols-7 gap-2">
                      {Object.entries(config.settings.businessDays).map(([day, checked]) => (
                        <div key={day} className="flex flex-col items-center">
                          <Label htmlFor={`day-${day}`} className="text-xs capitalize mb-1">
                            {day.substring(0, 3)}
                          </Label>
                          <Switch
                            id={`day-${day}`}
                            checked={checked}
                            onCheckedChange={(checked) => handleBusinessDayChange(day, checked)}
                            className="data-[state=checked]:bg-primary"
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
            <CardFooter>
              <Button onClick={handleSaveConfig}>Save Settings</Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
