'use client';

import { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { PlusCircle, Filter, Calendar } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

// Mock data for development
const mockJournalEntries = [
  {
    id: '1',
    entryNumber: 'JE-2023-0001',
    entryDate: '2023-05-15',
    description: 'Monthly rent payment',
    reference: 'INV-2023-0042',
    status: 'POSTED',
    amount: 2500.00,
    createdBy: 'Admin User',
    createdAt: '2023-05-15T10:30:00Z',
  },
  {
    id: '2',
    entryNumber: 'JE-2023-0002',
    entryDate: '2023-05-16',
    description: 'Office supplies purchase',
    reference: 'PO-2023-0015',
    status: 'POSTED',
    amount: 750.50,
    createdBy: 'Admin User',
    createdAt: '2023-05-16T14:20:00Z',
  },
  {
    id: '3',
    entryNumber: 'JE-2023-0003',
    entryDate: '2023-05-20',
    description: 'Customer payment received',
    reference: 'PMT-2023-0025',
    status: 'POSTED',
    amount: 5000.00,
    createdBy: 'Admin User',
    createdAt: '2023-05-20T09:15:00Z',
  },
  {
    id: '4',
    entryNumber: 'JE-2023-0004',
    entryDate: '2023-05-25',
    description: 'Salary payment',
    reference: 'PAY-2023-0005',
    status: 'POSTED',
    amount: 15000.00,
    createdBy: 'Admin User',
    createdAt: '2023-05-25T16:45:00Z',
  },
  {
    id: '5',
    entryNumber: 'JE-2023-0005',
    entryDate: '2023-05-28',
    description: 'Utility bills payment',
    reference: 'INV-2023-0055',
    status: 'POSTED',
    amount: 850.75,
    createdBy: 'Admin User',
    createdAt: '2023-05-28T11:10:00Z',
  },
  {
    id: '6',
    entryNumber: 'JE-2023-0006',
    entryDate: '2023-05-30',
    description: 'Sales revenue recognition',
    reference: 'INV-2023-0060',
    status: 'POSTED',
    amount: 12500.00,
    createdBy: 'Admin User',
    createdAt: '2023-05-30T15:30:00Z',
  },
  {
    id: '7',
    entryNumber: 'JE-2023-0007',
    entryDate: '2023-06-01',
    description: 'Monthly depreciation',
    reference: 'AUTO-2023-0001',
    status: 'DRAFT',
    amount: 1200.00,
    createdBy: 'System',
    createdAt: '2023-06-01T00:01:00Z',
  },
];

export default function JournalEntriesPage() {
  const { toast } = useToast();
  const [journalEntries, setJournalEntries] = useState(mockJournalEntries);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('ALL');

  // Filter journal entries based on search term and filter status
  const filteredEntries = journalEntries.filter(entry => {
    const matchesSearch = 
      entry.entryNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      entry.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      entry.reference.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = filterStatus === 'ALL' || entry.status === filterStatus;
    
    return matchesSearch && matchesFilter;
  });

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between bg-white p-6 rounded-lg shadow-sm mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <svg className="w-6 h-6 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Journal Entries
          </h1>
          <p className="mt-1 text-sm text-gray-500">
            Create and manage journal entries
          </p>
        </div>
        <div className="mt-4 md:mt-0 flex space-x-3">
          <Button variant="outline" size="sm">
            <Filter className="mr-2 h-4 w-4" />
            Filter
          </Button>
          <Button variant="outline" size="sm">
            <Calendar className="mr-2 h-4 w-4" />
            Date Range
          </Button>
          <Button size="sm" className="bg-indigo-600 hover:bg-indigo-700">
            <PlusCircle className="mr-2 h-4 w-4" /> New Journal Entry
          </Button>
        </div>
      </div>

      {/* Journal Entries Table */}
      <Card className="border-none shadow-md">
        <CardHeader className="pb-2 border-b">
          <CardTitle className="text-lg font-bold">Journal Entries</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full text-sm text-left">
              <thead className="text-xs font-medium text-gray-500 bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-6 py-3">Entry Number</th>
                  <th className="px-6 py-3">Date</th>
                  <th className="px-6 py-3">Description</th>
                  <th className="px-6 py-3">Reference</th>
                  <th className="px-6 py-3">Amount (RM)</th>
                  <th className="px-6 py-3">Status</th>
                  <th className="px-6 py-3">Created By</th>
                  <th className="px-6 py-3 text-right">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {filteredEntries.map((entry) => (
                  <tr key={entry.id} className="bg-white hover:bg-gray-50 transition-colors">
                    <td className="px-6 py-4 font-medium text-indigo-600">
                      {entry.entryNumber}
                    </td>
                    <td className="px-6 py-4 text-gray-500">
                      {new Date(entry.entryDate).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 font-medium">
                      {entry.description}
                    </td>
                    <td className="px-6 py-4">
                      {entry.reference}
                    </td>
                    <td className="px-6 py-4 font-medium">
                      {entry.amount.toFixed(2)}
                    </td>
                    <td className="px-6 py-4">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        entry.status === 'POSTED' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {entry.status === 'POSTED' && (
                          <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        )}
                        {entry.status === 'DRAFT' && (
                          <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                          </svg>
                        )}
                        {entry.status}
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      {entry.createdBy}
                    </td>
                    <td className="px-6 py-4 text-right">
                      <div className="flex justify-end space-x-2">
                        <Button variant="ghost" size="sm" className="text-indigo-600 hover:text-indigo-800 hover:bg-indigo-50">
                          View
                        </Button>
                        {entry.status === 'DRAFT' && (
                          <Button variant="ghost" size="sm" className="text-gray-600 hover:text-gray-800 hover:bg-gray-50">
                            Edit
                          </Button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          <div className="flex items-center justify-between p-6 border-t border-gray-200">
            <div className="text-sm text-gray-500">
              Showing <span className="font-medium">1</span> to <span className="font-medium">{filteredEntries.length}</span> of <span className="font-medium">{filteredEntries.length}</span> entries
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm" disabled>
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                Previous
              </Button>
              <Button variant="outline" size="sm">
                Next
                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
