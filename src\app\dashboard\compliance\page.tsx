'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/button';
import { Spinner } from '@/components/ui/Spinner';
import Link from 'next/link';

interface ComplianceStats {
  totalInvoices: number;
  validatedInvoices: number;
  complianceRate: number;
  pendingValidation: number;
  validationIssues: number;
}

interface ValidationIssue {
  invoiceId: string;
  invoiceNumber: string;
  customerName: string;
  issueDate: string;
  errors: string[];
}

export default function ComplianceDashboardPage() {
  const [stats, setStats] = useState<ComplianceStats | null>(null);
  const [issues, setIssues] = useState<ValidationIssue[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // In a real implementation, this would fetch data from your API
        // const [statsResponse, issuesResponse] = await Promise.all([
        //   fetch('/api/compliance/stats'),
        //   fetch('/api/compliance/issues')
        // ]);
        // 
        // const statsData = await statsResponse.json();
        // const issuesData = await issuesResponse.json();
        // 
        // setStats(statsData);
        // setIssues(issuesData.issues);
        
        // For development, use mock data
        const mockStats: ComplianceStats = {
          totalInvoices: 120,
          validatedInvoices: 98,
          complianceRate: 81.67,
          pendingValidation: 15,
          validationIssues: 7,
        };
        
        const mockIssues: ValidationIssue[] = [
          {
            invoiceId: '1',
            invoiceNumber: 'INV-2025-042',
            customerName: 'Acme Corporation',
            issueDate: '2025-09-15',
            errors: ['Missing customer tax ID', 'Invalid tax calculation'],
          },
          {
            invoiceId: '2',
            invoiceNumber: 'INV-2025-056',
            customerName: 'Wayne Enterprises',
            issueDate: '2025-09-22',
            errors: ['Missing item descriptions'],
          },
          {
            invoiceId: '3',
            invoiceNumber: 'INV-2025-061',
            customerName: 'Stark Industries',
            issueDate: '2025-09-28',
            errors: ['Invalid tax rate', 'Missing business registration number'],
          },
          {
            invoiceId: '4',
            invoiceNumber: 'INV-2025-075',
            customerName: 'Daily Planet',
            issueDate: '2025-10-05',
            errors: ['Invalid customer information'],
          },
          {
            invoiceId: '5',
            invoiceNumber: 'INV-2025-082',
            customerName: 'LexCorp',
            issueDate: '2025-10-10',
            errors: ['Missing required fields', 'Invalid amount calculation'],
          },
          {
            invoiceId: '6',
            invoiceNumber: 'INV-2025-089',
            customerName: 'Queen Industries',
            issueDate: '2025-10-12',
            errors: ['Invalid tax ID format'],
          },
          {
            invoiceId: '7',
            invoiceNumber: 'INV-2025-094',
            customerName: 'Oscorp',
            issueDate: '2025-10-15',
            errors: ['Missing item details', 'Invalid customer tax ID'],
          },
        ];
        
        setStats(mockStats);
        setIssues(mockIssues);
      } catch (err) {
        console.error('Error fetching compliance data:', err);
        setError('Failed to load compliance data. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spinner size="lg" />
        <span className="ml-2 text-text-secondary">Loading...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4">
        <p>{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-2 text-red-600 hover:text-red-800 font-medium"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="bg-white p-6 rounded-lg shadow-sm mb-6">
        <h1 className="text-2xl font-bold text-text-primary flex items-center">
          <svg className="w-6 h-6 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          LHDN Compliance Dashboard
        </h1>
        <p className="mt-1 text-sm text-text-secondary">
          Monitor your tax compliance status with LHDN MyInvois
        </p>
      </div>

      {/* Stats cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="border-none shadow-md">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-text-secondary">Compliance Rate</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-baseline">
                <span className="text-3xl font-bold text-text-primary">{stats.complianceRate}%</span>
                <span className="ml-2 text-sm text-text-secondary">of invoices</span>
              </div>
              <div className="mt-4 h-2 bg-gray-200 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-green-500 rounded-full" 
                  style={{ width: `${stats.complianceRate}%` }}
                ></div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="border-none shadow-md">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-text-secondary">Validated Invoices</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-baseline">
                <span className="text-3xl font-bold text-text-primary">{stats.validatedInvoices}</span>
                <span className="ml-2 text-sm text-text-secondary">of {stats.totalInvoices}</span>
              </div>
              <p className="mt-2 text-sm text-green-600">
                {Math.round((stats.validatedInvoices / stats.totalInvoices) * 100)}% of total invoices
              </p>
            </CardContent>
          </Card>
          
          <Card className="border-none shadow-md">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-text-secondary">Pending Validation</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-baseline">
                <span className="text-3xl font-bold text-text-primary">{stats.pendingValidation}</span>
                <span className="ml-2 text-sm text-text-secondary">invoices</span>
              </div>
              <p className="mt-2 text-sm text-yellow-600">
                {Math.round((stats.pendingValidation / stats.totalInvoices) * 100)}% of total invoices
              </p>
            </CardContent>
          </Card>
          
          <Card className="border-none shadow-md">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-text-secondary">Validation Issues</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-baseline">
                <span className="text-3xl font-bold text-text-primary">{stats.validationIssues}</span>
                <span className="ml-2 text-sm text-text-secondary">invoices</span>
              </div>
              <p className="mt-2 text-sm text-red-600">
                {Math.round((stats.validationIssues / stats.totalInvoices) * 100)}% of total invoices
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Validation issues */}
      <Card className="border-none shadow-md">
        <CardHeader className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <CardTitle className="text-lg font-bold text-text-primary">Validation Issues</CardTitle>
          <Button size="sm" className="mt-2 sm:mt-0">
            Validate All Pending
          </Button>
        </CardHeader>
        <CardContent>
          {issues.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full text-sm text-left" className="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr className="bg-white hover:bg-gray-50 transition-colors">
                    <th className="px-3 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">Invoice</th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">Customer</th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">Issue Date</th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">Issues</th>
                    <th className="px-3 py-3 text-right text-xs font-medium text-text-secondary uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {issues.map((issue) => (
                    <tr key={issue.invoiceId}>
                      <td className="px-3 py-4 text-sm text-text-primary">
                        <Link href={`/dashboard/invoices/${issue.invoiceId}`} className="text-indigo-600 hover:text-indigo-900">
                          {issue.invoiceNumber}
                        </Link>
                      </td>
                      <td className="px-3 py-4 text-sm text-text-primary">{issue.customerName}</td>
                      <td className="px-3 py-4 text-sm text-text-primary">{issue.issueDate}</td>
                      <td className="px-3 py-4 text-sm text-text-primary">
                        <ul className="list-disc pl-5 space-y-1">
                          {issue.errors.map((error, index) => (
                            <li key={index} className="text-red-600">{error}</li>
                          ))}
                        </ul>
                      </td>
                      <td className="px-3 py-4 text-sm text-right">
                        <Link href={`/dashboard/invoices/${issue.invoiceId}/edit`} className="text-indigo-600 hover:text-indigo-900 font-medium">
                          Fix Issues
                        </Link>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-8">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-text-primary">No validation issues</h3>
              <p className="mt-1 text-sm text-text-secondary">All your invoices are compliant with LHDN requirements.</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Compliance tips */}
      <Card className="border-none shadow-md">
        <CardHeader className="pb-2 border-b">
          <CardTitle className="text-lg font-bold text-text-primary">Compliance Tips</CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="space-y-3">
            <li className="flex">
              <svg className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <span className="text-sm text-text-secondary">Always include customer Tax ID (TIN) for B2B transactions</span>
            </li>
            <li className="flex">
              <svg className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <span className="text-sm text-text-secondary">Ensure all invoice items have clear descriptions</span>
            </li>
            <li className="flex">
              <svg className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <span className="text-sm text-text-secondary">Verify tax calculations match the current rates</span>
            </li>
            <li className="flex">
              <svg className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <span className="text-sm text-text-secondary">Validate all invoices before sending to customers</span>
            </li>
            <li className="flex">
              <svg className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <span className="text-sm text-text-secondary">Keep your business registration information up to date</span>
            </li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
}
