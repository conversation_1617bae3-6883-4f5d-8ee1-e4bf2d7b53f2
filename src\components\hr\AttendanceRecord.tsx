'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Spinner } from '@/components/ui/spinner';
import { Badge } from '@/components/ui/badge';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Calendar } from '@/components/ui/calendar';
import { format } from 'date-fns';

interface AttendanceRecordProps {
  employeeId: string;
  employeeName: string;
}

export default function AttendanceRecord({ employeeId, employeeName }: AttendanceRecordProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [date, setDate] = useState<Date | undefined>(new Date());
  const [isCheckInDialogOpen, setIsCheckInDialogOpen] = useState(false);
  const [isCheckOutDialogOpen, setIsCheckOutDialogOpen] = useState(false);
  
  // Mock attendance data for the current month
  const [attendanceData, setAttendanceData] = useState([
    {
      date: '2023-06-01',
      checkIn: '09:00:00',
      checkOut: '18:00:00',
      status: 'Present',
      workHours: 9,
    },
    {
      date: '2023-06-02',
      checkIn: '08:45:00',
      checkOut: '17:30:00',
      status: 'Present',
      workHours: 8.75,
    },
    {
      date: '2023-06-05',
      checkIn: '09:15:00',
      checkOut: '18:15:00',
      status: 'Present',
      workHours: 9,
    },
    {
      date: '2023-06-06',
      checkIn: '09:00:00',
      checkOut: '18:00:00',
      status: 'Present',
      workHours: 9,
    },
    {
      date: '2023-06-07',
      checkIn: null,
      checkOut: null,
      status: 'Absent',
      workHours: 0,
    },
    {
      date: '2023-06-08',
      checkIn: '09:30:00',
      checkOut: '18:30:00',
      status: 'Present',
      workHours: 9,
    },
    {
      date: '2023-06-09',
      checkIn: '09:00:00',
      checkOut: '18:00:00',
      status: 'Present',
      workHours: 9,
    },
    {
      date: '2023-06-12',
      checkIn: '09:00:00',
      checkOut: '18:00:00',
      status: 'Present',
      workHours: 9,
    },
    {
      date: '2023-06-13',
      checkIn: '09:00:00',
      checkOut: '18:00:00',
      status: 'Present',
      workHours: 9,
    },
    {
      date: '2023-06-14',
      checkIn: '09:00:00',
      checkOut: '18:00:00',
      status: 'Present',
      workHours: 9,
    },
    {
      date: '2023-06-15',
      checkIn: '09:00:00',
      checkOut: '18:00:00',
      status: 'Present',
      workHours: 9,
    },
    {
      date: '2023-06-16',
      checkIn: null,
      checkOut: null,
      status: 'On Leave',
      workHours: 0,
    },
  ]);

  const handleCheckIn = async () => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);
    
    try {
      // In a real implementation, this would call your API
      // Simulate API call with timeout
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const now = new Date();
      const formattedDate = format(now, 'yyyy-MM-dd');
      const formattedTime = format(now, 'HH:mm:ss');
      
      // Check if there's already an attendance record for today
      const existingRecordIndex = attendanceData.findIndex(record => record.date === formattedDate);
      
      if (existingRecordIndex >= 0) {
        // Update existing record
        const updatedAttendanceData = [...attendanceData];
        updatedAttendanceData[existingRecordIndex] = {
          ...updatedAttendanceData[existingRecordIndex],
          checkIn: formattedTime,
          status: 'Present',
        };
        setAttendanceData(updatedAttendanceData);
      } else {
        // Create new record
        setAttendanceData([
          ...attendanceData,
          {
            date: formattedDate,
            checkIn: formattedTime,
            checkOut: null,
            status: 'Present',
            workHours: 0,
          }
        ]);
      }
      
      setSuccess(`Check-in recorded at ${formattedTime}`);
      setIsCheckInDialogOpen(false);
    } catch (err: any) {
      console.error('Error recording check-in:', err);
      setError(err.message || 'Failed to record check-in');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCheckOut = async () => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);
    
    try {
      // In a real implementation, this would call your API
      // Simulate API call with timeout
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const now = new Date();
      const formattedDate = format(now, 'yyyy-MM-dd');
      const formattedTime = format(now, 'HH:mm:ss');
      
      // Check if there's already an attendance record for today
      const existingRecordIndex = attendanceData.findIndex(record => record.date === formattedDate);
      
      if (existingRecordIndex >= 0) {
        // Update existing record
        const updatedAttendanceData = [...attendanceData];
        const record = updatedAttendanceData[existingRecordIndex];
        
        if (!record.checkIn) {
          throw new Error('Cannot check out without checking in first');
        }
        
        // Calculate work hours
        const checkInTime = new Date(`${formattedDate}T${record.checkIn}`);
        const checkOutTime = new Date(`${formattedDate}T${formattedTime}`);
        const workHours = (checkOutTime.getTime() - checkInTime.getTime()) / (1000 * 60 * 60);
        
        updatedAttendanceData[existingRecordIndex] = {
          ...record,
          checkOut: formattedTime,
          workHours: parseFloat(workHours.toFixed(2)),
        };
        
        setAttendanceData(updatedAttendanceData);
      } else {
        throw new Error('Cannot check out without checking in first');
      }
      
      setSuccess(`Check-out recorded at ${formattedTime}`);
      setIsCheckOutDialogOpen(false);
    } catch (err: any) {
      console.error('Error recording check-out:', err);
      setError(err.message || 'Failed to record check-out');
    } finally {
      setIsLoading(false);
    }
  };

  const getTodayAttendance = () => {
    const today = format(new Date(), 'yyyy-MM-dd');
    return attendanceData.find(record => record.date === today);
  };

  const todayAttendance = getTodayAttendance();

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      
      {success && (
        <Alert variant="success" className="bg-green-50 text-green-800 border-green-200">
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}
      
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h2 className="text-xl font-bold">{employeeName}'s Attendance</h2>
          <p className="text-sm text-gray-500">Employee ID: {employeeId}</p>
        </div>
        <div className="mt-4 md:mt-0 flex space-x-3">
          <Button 
            variant="outline" 
            onClick={() => setIsCheckInDialogOpen(true)}
            disabled={!!todayAttendance?.checkIn}
          >
            Check In
          </Button>
          <Button 
            onClick={() => setIsCheckOutDialogOpen(true)}
            disabled={!todayAttendance?.checkIn || !!todayAttendance?.checkOut}
          >
            Check Out
          </Button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h3 className="text-lg font-medium mb-4">Today's Status</h3>
          <div className="p-4 border rounded-md">
            <div className="flex justify-between items-center mb-4">
              <div>
                <p className="text-sm text-gray-500">
                  {format(new Date(), 'EEEE, MMMM d, yyyy')}
                </p>
              </div>
              {todayAttendance ? (
                <Badge 
                  className={
                    todayAttendance.status === 'Present' 
                      ? 'bg-green-100 text-green-800 border-green-200' 
                      : todayAttendance.status === 'On Leave'
                      ? 'bg-yellow-100 text-yellow-800 border-yellow-200'
                      : 'bg-red-100 text-red-800 border-red-200'
                  }
                >
                  {todayAttendance.status}
                </Badge>
              ) : (
                <Badge variant="outline">Not Recorded</Badge>
              )}
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-500">Check In</p>
                <p className="text-lg font-semibold">
                  {todayAttendance?.checkIn || '-'}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Check Out</p>
                <p className="text-lg font-semibold">
                  {todayAttendance?.checkOut || '-'}
                </p>
              </div>
              {todayAttendance?.workHours > 0 && (
                <div className="col-span-2">
                  <p className="text-sm font-medium text-gray-500">Work Hours</p>
                  <p className="text-lg font-semibold">
                    {todayAttendance.workHours} hours
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
        
        <div>
          <h3 className="text-lg font-medium mb-4">Monthly Overview</h3>
          <div className="p-4 border rounded-md">
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <p className="text-sm font-medium text-gray-500">Present Days</p>
                <p className="text-lg font-semibold">
                  {attendanceData.filter(record => record.status === 'Present').length}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Absent Days</p>
                <p className="text-lg font-semibold">
                  {attendanceData.filter(record => record.status === 'Absent').length}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Leave Days</p>
                <p className="text-lg font-semibold">
                  {attendanceData.filter(record => record.status === 'On Leave').length}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Total Work Hours</p>
                <p className="text-lg font-semibold">
                  {attendanceData.reduce((total, record) => total + record.workHours, 0)} hours
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div>
        <h3 className="text-lg font-medium mb-4">Attendance Calendar</h3>
        <div className="p-4 border rounded-md">
          <Calendar
            mode="single"
            selected={date}
            onSelect={setDate}
            className="rounded-md border"
            disabled={{ after: new Date() }}
          />
        </div>
      </div>
      
      {/* Check In Dialog */}
      <Dialog open={isCheckInDialogOpen} onOpenChange={setIsCheckInDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Check In</DialogTitle>
            <DialogDescription>
              Record your attendance for today.
            </DialogDescription>
          </DialogHeader>
          
          <div className="py-4">
            <p className="text-center text-lg font-semibold">
              {format(new Date(), 'EEEE, MMMM d, yyyy')}
            </p>
            <p className="text-center text-3xl font-bold mt-2">
              {format(new Date(), 'HH:mm:ss')}
            </p>
          </div>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsCheckInDialogOpen(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleCheckIn}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Spinner size="sm" className="mr-2" />
                  Processing...
                </>
              ) : (
                <>Confirm Check In</>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Check Out Dialog */}
      <Dialog open={isCheckOutDialogOpen} onOpenChange={setIsCheckOutDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Check Out</DialogTitle>
            <DialogDescription>
              Record your check out time for today.
            </DialogDescription>
          </DialogHeader>
          
          <div className="py-4">
            <p className="text-center text-lg font-semibold">
              {format(new Date(), 'EEEE, MMMM d, yyyy')}
            </p>
            <p className="text-center text-3xl font-bold mt-2">
              {format(new Date(), 'HH:mm:ss')}
            </p>
            {todayAttendance?.checkIn && (
              <p className="text-center text-sm text-gray-500 mt-2">
                Check In: {todayAttendance.checkIn}
              </p>
            )}
          </div>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsCheckOutDialogOpen(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleCheckOut}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Spinner size="sm" className="mr-2" />
                  Processing...
                </>
              ) : (
                <>Confirm Check Out</>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
