'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Filter, Calendar, Download } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

// Mock data for development
const mockLedgerEntries = [
  {
    id: '1',
    date: '2023-05-01',
    accountCode: '1-1000',
    accountName: 'Cash',
    journalEntry: 'JE-2023-0001',
    description: 'Initial balance',
    debit: 10000.00,
    credit: 0.00,
    balance: 10000.00,
  },
  {
    id: '2',
    date: '2023-05-05',
    accountCode: '1-1000',
    accountName: 'Cash',
    journalEntry: 'JE-2023-0002',
    description: 'Customer payment',
    debit: 5000.00,
    credit: 0.00,
    balance: 15000.00,
  },
  {
    id: '3',
    date: '2023-05-10',
    accountCode: '1-1000',
    accountName: 'Cash',
    journalEntry: 'JE-2023-0003',
    description: 'Rent payment',
    debit: 0.00,
    credit: 2500.00,
    balance: 12500.00,
  },
  {
    id: '4',
    date: '2023-05-15',
    accountCode: '1-1000',
    accountName: 'Cash',
    journalEntry: 'JE-2023-0004',
    description: 'Office supplies',
    debit: 0.00,
    credit: 750.50,
    balance: 11749.50,
  },
  {
    id: '5',
    date: '2023-05-20',
    accountCode: '1-1000',
    accountName: 'Cash',
    journalEntry: 'JE-2023-0005',
    description: 'Customer payment',
    debit: 3500.00,
    credit: 0.00,
    balance: 15249.50,
  },
  {
    id: '6',
    date: '2023-05-25',
    accountCode: '1-1000',
    accountName: 'Cash',
    journalEntry: 'JE-2023-0006',
    description: 'Salary payment',
    debit: 0.00,
    credit: 15000.00,
    balance: 249.50,
  },
  {
    id: '7',
    date: '2023-05-28',
    accountCode: '1-1000',
    accountName: 'Cash',
    journalEntry: 'JE-2023-0007',
    description: 'Utility bills',
    debit: 0.00,
    credit: 850.75,
    balance: -601.25,
  },
  {
    id: '8',
    date: '2023-05-30',
    accountCode: '1-1000',
    accountName: 'Cash',
    journalEntry: 'JE-2023-0008',
    description: 'Bank transfer',
    debit: 5000.00,
    credit: 0.00,
    balance: 4398.75,
  },
];

// Mock accounts for filtering
const mockAccounts = [
  { code: '1-1000', name: 'Cash' },
  { code: '1-1100', name: 'Bank' },
  { code: '1-1200', name: 'Accounts Receivable' },
  { code: '2-1000', name: 'Accounts Payable' },
  { code: '4-1000', name: 'Sales Revenue' },
  { code: '5-1000', name: 'Cost of Goods Sold' },
];

export default function GeneralLedgerPage() {
  const { toast } = useToast();
  const [ledgerEntries, setLedgerEntries] = useState(mockLedgerEntries);
  const [selectedAccount, setSelectedAccount] = useState('1-1000'); // Default to Cash account
  const [dateRange, setDateRange] = useState({ start: '2023-05-01', end: '2023-05-31' });

  // Filter ledger entries based on selected account and date range
  const filteredEntries = ledgerEntries.filter(entry => {
    const matchesAccount = entry.accountCode === selectedAccount;
    const entryDate = new Date(entry.date);
    const startDate = new Date(dateRange.start);
    const endDate = new Date(dateRange.end);
    const matchesDateRange = entryDate >= startDate && entryDate <= endDate;
    
    return matchesAccount && matchesDateRange;
  });

  // Get the selected account name
  const selectedAccountName = mockAccounts.find(account => account.code === selectedAccount)?.name || '';

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between bg-white p-6 rounded-lg shadow-sm mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <svg className="w-6 h-6 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
            </svg>
            General Ledger
          </h1>
          <p className="mt-1 text-sm text-gray-500">
            View and analyze your general ledger entries
          </p>
        </div>
        <div className="mt-4 md:mt-0 flex space-x-3">
          <Button variant="outline" size="sm">
            <Filter className="mr-2 h-4 w-4" />
            Filter
          </Button>
          <Button variant="outline" size="sm">
            <Calendar className="mr-2 h-4 w-4" />
            Date Range
          </Button>
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Account Selection */}
      <Card className="border-none shadow-md">
        <CardHeader className="pb-2 border-b">
          <CardTitle className="text-lg font-bold">Account: {selectedAccountName} ({selectedAccount})</CardTitle>
        </CardHeader>
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Select Account</label>
              <select 
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                value={selectedAccount}
                onChange={(e) => setSelectedAccount(e.target.value)}
              >
                {mockAccounts.map(account => (
                  <option key={account.code} value={account.code}>
                    {account.name} ({account.code})
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
              <input 
                type="date" 
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                value={dateRange.start}
                onChange={(e) => setDateRange({ ...dateRange, start: e.target.value })}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">End Date</label>
              <input 
                type="date" 
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                value={dateRange.end}
                onChange={(e) => setDateRange({ ...dateRange, end: e.target.value })}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Ledger Entries Table */}
      <Card className="border-none shadow-md">
        <CardHeader className="pb-2 border-b">
          <CardTitle className="text-lg font-bold">Ledger Entries</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full text-sm text-left">
              <thead className="text-xs font-medium text-gray-500 bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-6 py-3">Date</th>
                  <th className="px-6 py-3">Journal Entry</th>
                  <th className="px-6 py-3">Description</th>
                  <th className="px-6 py-3 text-right">Debit (RM)</th>
                  <th className="px-6 py-3 text-right">Credit (RM)</th>
                  <th className="px-6 py-3 text-right">Balance (RM)</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {filteredEntries.map((entry) => (
                  <tr key={entry.id} className="bg-white hover:bg-gray-50 transition-colors">
                    <td className="px-6 py-4 text-gray-500">
                      {new Date(entry.date).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 font-medium text-indigo-600">
                      {entry.journalEntry}
                    </td>
                    <td className="px-6 py-4 font-medium">
                      {entry.description}
                    </td>
                    <td className="px-6 py-4 text-right">
                      {entry.debit > 0 ? entry.debit.toFixed(2) : '-'}
                    </td>
                    <td className="px-6 py-4 text-right">
                      {entry.credit > 0 ? entry.credit.toFixed(2) : '-'}
                    </td>
                    <td className={`px-6 py-4 text-right font-medium ${
                      entry.balance >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {entry.balance.toFixed(2)}
                    </td>
                  </tr>
                ))}
              </tbody>
              <tfoot className="bg-gray-50 border-t border-gray-200 font-medium">
                <tr>
                  <td colSpan={3} className="px-6 py-4 text-right">
                    Total:
                  </td>
                  <td className="px-6 py-4 text-right">
                    {filteredEntries.reduce((sum, entry) => sum + entry.debit, 0).toFixed(2)}
                  </td>
                  <td className="px-6 py-4 text-right">
                    {filteredEntries.reduce((sum, entry) => sum + entry.credit, 0).toFixed(2)}
                  </td>
                  <td className={`px-6 py-4 text-right font-medium ${
                    filteredEntries.length > 0 && filteredEntries[filteredEntries.length - 1].balance >= 0 
                      ? 'text-green-600' 
                      : 'text-red-600'
                  }`}>
                    {filteredEntries.length > 0 
                      ? filteredEntries[filteredEntries.length - 1].balance.toFixed(2) 
                      : '0.00'}
                  </td>
                </tr>
              </tfoot>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
