// This file runs once before all tests
import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.test' });

export default async function globalSetup() {
  // Set up test environment
  process.env.NODE_ENV = 'test';
  process.env.DATABASE_URL = process.env.TEST_DATABASE_URL || process.env.DATABASE_URL;
  
  // Create a test database connection
  const prisma = new PrismaClient();
  
  try {
    // Test database connection
    await prisma.$connect();
    console.log('Connected to test database');
    
    // Run any setup migrations or seed data if needed
    
  } catch (error) {
    console.error('Failed to connect to test database', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}
