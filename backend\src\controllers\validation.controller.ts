import { Request, Response } from 'express';
import { validateBusinessRegNo } from '../services/lhdn.service';

/**
 * Validate a business registration number
 */
export const validateBusinessRegistration = async (req: Request, res: Response) => {
  try {
    const { regNo } = req.params;

    if (!regNo) {
      return res.status(400).json({ message: 'Business registration number is required' });
    }

    // Validate business registration number with LHDN
    const validationResult = await validateBusinessRegNo(regNo);

    return res.status(200).json({
      regNo,
      isValid: validationResult.isValid,
      businessName: validationResult.businessName,
      businessType: validationResult.businessType,
    });
  } catch (error: any) {
    console.error('Error validating business registration number:', error);
    return res.status(500).json({ message: 'Failed to validate business registration number', error: error.message });
  }
};
