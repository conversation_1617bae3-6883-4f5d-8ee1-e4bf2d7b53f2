# Test User Guide for Invoix

This guide explains how to use the test user functionality in the Invoix application without requiring a running backend server.

## Test User Credentials

You can log in with the following credentials:

- **Email**: <EMAIL>
- **Password**: password123

## Features Available with Test User

When logged in as the test user, the following features will work without a backend:

1. **Authentication**: Login, token management, and session persistence
2. **Dashboard**: View mock dashboard statistics
3. **Invoices**: View a list of mock invoices
4. **Customers**: View a list of mock customers
5. **Tenant Information**: View mock tenant details

## How It Works

The application has been modified to detect when the test user email is used and bypass the normal authentication flow. Instead, it:

1. Creates mock tokens and stores them in localStorage
2. Creates a mock user profile
3. Intercepts API calls and returns mock data based on the endpoint

## Limitations

While using the test user, be aware of these limitations:

1. **Create/Update/Delete Operations**: These operations will appear to work but won't persist data between sessions
2. **Limited Data**: Only a small set of mock data is available
3. **No Backend Features**: Features that require complex backend processing won't work fully

## For Developers

The test user functionality is implemented in `src/lib/auth/index.ts` with the following key components:

1. Modified `login()` function to detect the test user email
2. Added mock data generation in the `createMockResponse()` function
3. Intercepted API calls in the `createAuthenticatedFetch()` function

To add more mock endpoints, modify the `createMockResponse()` function to handle additional URL patterns.
