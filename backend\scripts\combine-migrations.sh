#!/bin/bash

# Combine all migration parts into a single file
cat prisma/migrations/20240601000000_complete_schema/migration.sql \
    prisma/migrations/20240601000000_complete_schema/migration.sql.part2 \
    prisma/migrations/20240601000000_complete_schema/migration.sql.part3 \
    prisma/migrations/20240601000000_complete_schema/migration.sql.part4 \
    > prisma/migrations/20240601000000_complete_schema/migration.sql.combined

# Replace the original migration file with the combined one
mv prisma/migrations/20240601000000_complete_schema/migration.sql.combined prisma/migrations/20240601000000_complete_schema/migration.sql

# Remove the part files
rm prisma/migrations/20240601000000_complete_schema/migration.sql.part*

echo "Migration files combined successfully."
