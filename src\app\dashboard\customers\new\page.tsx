'use client';

import CustomerForm from '@/components/forms/CustomerForm';

export default function NewCustomerPage() {
  return (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow-sm mb-6">
        <h1 className="text-2xl font-bold text-text-primary flex items-center">
          <svg className="w-6 h-6 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Add New Customer
        </h1>
        <p className="mt-1 text-sm text-text-secondary">
          Create a new customer with LHDN tax ID validation
        </p>
      </div>

      <CustomerForm />
    </div>
  );
}
