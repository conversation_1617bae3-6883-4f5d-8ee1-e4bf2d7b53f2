import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import path from 'path';
import fs from 'fs/promises';

/**
 * API route for serving tenant logo images
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { filename: string } }
) {
  try {
    // Get the filename from the URL params
    const { filename } = params;

    // Get the user session
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    // Call the backend API to get the logo image
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/tenants/logo/${filename}`, {
      headers: {
        'Authorization': `Bearer ${session.accessToken}`
      },
    });

    // If the response is not OK, return an error
    if (!response.ok) {
      return new NextResponse('Logo not found', { status: 404 });
    }

    // Get the image data as a buffer
    const imageBuffer = await response.arrayBuffer();

    // Get the content type from the response headers
    const contentType = response.headers.get('content-type') || 'image/jpeg';

    // Return the image data with the appropriate headers
    return new NextResponse(imageBuffer, {
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=86400', // Cache for 1 day
      },
    });
  } catch (error) {
    console.error('Error serving logo image:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}
