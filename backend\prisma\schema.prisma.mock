// This is a mock schema for development purposes
// It includes the models needed for the ERP modules

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Existing models
model User {
  id                String              @id @default(uuid())
  email             String              @unique
  password          String
  name              String?
  role              String              @default("USER")
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id])
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([tenantId])
}

model Tenant {
  id                String              @id @default(uuid())
  name              String
  businessRegNo     String?
  businessRegValid  Boolean             @default(false)
  address           String?
  phone             String?
  email             String?
  logo              String?
  isActive          Boolean             @default(true)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
  users             User[]
  invoices          Invoice[]
  products          Product[]
  warehouses        Warehouse[]
  inventoryItems    InventoryItem[]
  inventoryTransactions InventoryTransaction[]
  employees         Employee[]
  salaryRecords     SalaryRecord[]
  attendanceRecords AttendanceRecord[]
  leaveRequests     LeaveRequest[]
  suppliers         Supplier[]
  purchaseOrders    PurchaseOrder[]
  goodsReceipts     GoodsReceipt[]
  fixedAssets       FixedAsset[]
  assetDepreciations AssetDepreciation[]
  assetMaintenances AssetMaintenance[]
  projects          Project[]
  projectTasks      ProjectTask[]
  timeEntries       TimeEntry[]
  projectExpenses   ProjectExpense[]
}

model Invoice {
  id                String              @id @default(uuid())
  invoiceNumber     String
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id])
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([tenantId])
}

// HR Module
model Employee {
  id                String              @id @default(uuid())
  employeeId        String
  name              String
  email             String
  phone             String?
  address           String?
  position          String?
  department        String?
  joinDate          DateTime
  terminationDate   DateTime?
  isActive          Boolean             @default(true)
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id])
  salaryRecords     SalaryRecord[]
  attendanceRecords AttendanceRecord[]
  leaveRequests     LeaveRequest[]
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([tenantId])
}

model SalaryRecord {
  id                String              @id @default(uuid())
  month             Int
  year              Int
  basicSalary       Float
  allowances        Float               @default(0)
  deductions        Float               @default(0)
  tax               Float               @default(0)
  epf               Float               @default(0)
  socso             Float               @default(0)
  netSalary         Float
  paymentDate       DateTime?
  paymentStatus     String              @default("PENDING") // PENDING, PAID
  employeeId        String
  employee          Employee            @relation(fields: [employeeId], references: [id])
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id])
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([employeeId])
  @@index([tenantId])
}

model AttendanceRecord {
  id                String              @id @default(uuid())
  date              DateTime
  checkIn           DateTime?
  checkOut          DateTime?
  status            String              @default("PRESENT") // PRESENT, ABSENT, LATE, ON_LEAVE
  notes             String?
  employeeId        String
  employee          Employee            @relation(fields: [employeeId], references: [id])
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id])
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([employeeId])
  @@index([tenantId])
}

model LeaveRequest {
  id                String              @id @default(uuid())
  leaveType         String              // ANNUAL, SICK, PERSONAL, etc.
  startDate         DateTime
  endDate           DateTime
  totalDays         Int
  reason            String?
  status            String              @default("PENDING") // PENDING, APPROVED, REJECTED
  approvedBy        String?
  approvalDate      DateTime?
  employeeId        String
  employee          Employee            @relation(fields: [employeeId], references: [id])
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id])
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([employeeId])
  @@index([tenantId])
}

// Inventory Module
model Product {
  id                String              @id @default(uuid())
  sku               String
  name              String
  description       String?
  category          String?
  unitPrice         Float
  costPrice         Float
  taxRate           Float               @default(0)
  isActive          Boolean             @default(true)
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id])
  inventoryItems    InventoryItem[]
  purchaseItems     PurchaseOrderItem[]
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([tenantId])
}

model Warehouse {
  id                String              @id @default(uuid())
  name              String
  location          String?
  isDefault         Boolean             @default(false)
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id])
  inventoryItems    InventoryItem[]
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([tenantId])
}

model InventoryItem {
  id                String              @id @default(uuid())
  quantity          Int                 @default(0)
  reorderLevel      Int                 @default(0)
  lastStockTake     DateTime?
  productId         String
  product           Product             @relation(fields: [productId], references: [id])
  warehouseId       String
  warehouse         Warehouse           @relation(fields: [warehouseId], references: [id])
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id])
  transactions      InventoryTransaction[]
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([productId])
  @@index([warehouseId])
  @@index([tenantId])
}

model InventoryTransaction {
  id                String              @id @default(uuid())
  type              String              // PURCHASE, SALE, ADJUSTMENT, STOCK_TAKE, TRANSFER
  quantity          Int
  notes             String?
  referenceId       String?
  referenceType     String?
  createdBy         String?
  inventoryItemId   String
  inventoryItem     InventoryItem       @relation(fields: [inventoryItemId], references: [id])
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id])
  createdAt         DateTime            @default(now())

  @@index([inventoryItemId])
  @@index([tenantId])
}

// Procurement Module
model Supplier {
  id                String              @id @default(uuid())
  name              String
  contactPerson     String?
  email             String?
  phone             String?
  address           String?
  taxId             String?
  paymentTerms      Int                 @default(30)
  isActive          Boolean             @default(true)
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id])
  purchaseOrders    PurchaseOrder[]
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([tenantId])
}

model PurchaseOrder {
  id                String              @id @default(uuid())
  poNumber          String
  orderDate         DateTime
  expectedDelivery  DateTime?
  status            String              @default("DRAFT") // DRAFT, SENT, PARTIALLY_RECEIVED, RECEIVED, CANCELLED
  totalAmount       Float
  tax               Float               @default(0)
  notes             String?
  supplierId        String
  supplier          Supplier            @relation(fields: [supplierId], references: [id])
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id])
  items             PurchaseOrderItem[]
  receipts          GoodsReceipt[]
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([supplierId])
  @@index([tenantId])
}

model PurchaseOrderItem {
  id                String              @id @default(uuid())
  description       String
  quantity          Int
  receivedQuantity  Int                 @default(0)
  unitPrice         Float
  amount            Float
  purchaseOrderId   String
  purchaseOrder     PurchaseOrder       @relation(fields: [purchaseOrderId], references: [id], onDelete: Cascade)
  productId         String
  product           Product             @relation(fields: [productId], references: [id])
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id])
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([purchaseOrderId])
  @@index([productId])
  @@index([tenantId])
}

model GoodsReceipt {
  id                String              @id @default(uuid())
  receiptNumber     String
  receiveDate       DateTime
  notes             String?
  purchaseOrderId   String
  purchaseOrder     PurchaseOrder       @relation(fields: [purchaseOrderId], references: [id])
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id])
  items             GoodsReceiptItem[]
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([purchaseOrderId])
  @@index([tenantId])
}

model GoodsReceiptItem {
  id                String              @id @default(uuid())
  productId         String
  quantity          Int
  notes             String?
  goodsReceiptId    String
  goodsReceipt      GoodsReceipt        @relation(fields: [goodsReceiptId], references: [id], onDelete: Cascade)
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id])
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([goodsReceiptId])
  @@index([tenantId])
}

// Fixed Assets Module
model FixedAsset {
  id                String              @id @default(uuid())
  assetNumber       String
  name              String
  description       String?
  category          String
  purchaseDate      DateTime
  purchasePrice     Float
  currentValue      Float
  location          String?
  assignedTo        String?
  status            String              @default("ACTIVE") // ACTIVE, MAINTENANCE, DISPOSED
  disposalDate      DateTime?
  disposalValue     Float?
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id])
  depreciations     AssetDepreciation[]
  maintenances      AssetMaintenance[]
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([tenantId])
}

model AssetDepreciation {
  id                String              @id @default(uuid())
  depreciationDate  DateTime
  depreciationAmount Float
  bookValue         Float
  notes             String?
  assetId           String
  asset             FixedAsset          @relation(fields: [assetId], references: [id])
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id])
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([assetId])
  @@index([tenantId])
}

model AssetMaintenance {
  id                String              @id @default(uuid())
  maintenanceDate   DateTime
  description       String
  cost              Float
  provider          String?
  nextMaintenanceDate DateTime?
  assetId           String
  asset             FixedAsset          @relation(fields: [assetId], references: [id])
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id])
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([assetId])
  @@index([tenantId])
}

// Project Management Module
model Project {
  id                String              @id @default(uuid())
  name              String
  description       String?
  startDate         DateTime
  endDate           DateTime?
  status            String              @default("PLANNING") // PLANNING, ACTIVE, ON_HOLD, COMPLETED, CANCELLED
  budget            Float?
  actualCost        Float?              @default(0)
  completionPercentage Float?           @default(0)
  clientId          String?
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id])
  tasks             ProjectTask[]
  timeEntries       TimeEntry[]
  expenses          ProjectExpense[]
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([tenantId])
}

model ProjectTask {
  id                String              @id @default(uuid())
  name              String
  description       String?
  startDate         DateTime?
  dueDate           DateTime?
  completionDate    DateTime?
  status            String              @default("TODO") // TODO, IN_PROGRESS, REVIEW, DONE, CANCELLED
  priority          String              @default("MEDIUM") // LOW, MEDIUM, HIGH
  assignedTo        String?
  estimatedHours    Float?
  actualHours       Float?              @default(0)
  projectId         String
  project           Project             @relation(fields: [projectId], references: [id])
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id])
  timeEntries       TimeEntry[]
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([projectId])
  @@index([tenantId])
}

model TimeEntry {
  id                String              @id @default(uuid())
  description       String?
  startTime         DateTime
  endTime           DateTime?
  duration          Float
  billable          Boolean             @default(true)
  employeeId        String?
  projectId         String
  project           Project             @relation(fields: [projectId], references: [id])
  taskId            String?
  task              ProjectTask?        @relation(fields: [taskId], references: [id])
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id])
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([projectId])
  @@index([taskId])
  @@index([tenantId])
}

model ProjectExpense {
  id                String              @id @default(uuid())
  description       String
  amount            Float
  date              DateTime
  category          String?
  receiptUrl        String?
  reimbursable      Boolean             @default(false)
  status            String              @default("PENDING") // PENDING, APPROVED, REJECTED, REIMBURSED
  projectId         String
  project           Project             @relation(fields: [projectId], references: [id])
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id])
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([projectId])
  @@index([tenantId])
}
