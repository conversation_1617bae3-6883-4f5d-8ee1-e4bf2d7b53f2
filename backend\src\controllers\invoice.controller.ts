import { Request, Response } from 'express';
import { prisma } from '../index';
import config from '../config/config';

// Create a new invoice
export const createInvoice = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId } = req.user;
    const {
      invoiceNumber,
      dueDate,
      customerId,
      items,
      tax,
      notes,
    } = req.body;

    // Check if customer exists and belongs to the tenant
    const customer = await prisma.customer.findFirst({
      where: {
        id: customerId,
        tenantId,
      },
    });

    if (!customer) {
      return res.status(404).json({ message: 'Customer not found or does not belong to your tenant' });
    }

    // Check if invoice number already exists for this tenant
    const existingInvoice = await prisma.invoice.findFirst({
      where: {
        invoiceNumber,
        tenantId,
      },
    });

    if (existingInvoice) {
      return res.status(400).json({ message: 'Invoice number already exists for this tenant' });
    }

    // Calculate total amount
    const totalAmount = items.reduce(
      (sum: number, item: { quantity: number; unitPrice: number }) =>
        sum + item.quantity * item.unitPrice,
      0
    );

    // Create invoice with items in a transaction
    const invoice = await prisma.$transaction(async (tx: any) => {
      // Create invoice
      const newInvoice = await tx.invoice.create({
        data: {
          invoiceNumber,
          dueDate: new Date(dueDate),
          totalAmount,
          tax: tax || 0,
          notes,
          tenantId,
          customerId,
        },
      });

      // Create invoice items
      for (const item of items) {
        await tx.invoiceItem.create({
          data: {
            description: item.description,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            amount: item.quantity * item.unitPrice,
            invoiceId: newInvoice.id,
          },
        });
      }

      return newInvoice;
    });

    // Return created invoice
    return res.status(201).json({
      message: 'Invoice created successfully',
      invoice,
    });
  } catch (error: any) {
    console.error('Error creating invoice:', error);
    return res.status(500).json({ message: 'Failed to create invoice', error: error.message });
  }
};

// Get invoice by ID
export const getInvoiceById = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId } = req.user;
    const { id } = req.params;

    // Find invoice by ID and ensure it belongs to the tenant
    const invoice = await prisma.invoice.findFirst({
      where: {
        id,
        tenantId,
      },
      include: {
        customer: true,
        items: true,
        payments: true,
      },
    });

    if (!invoice) {
      return res.status(404).json({ message: 'Invoice not found or does not belong to your tenant' });
    }

    return res.status(200).json(invoice);
  } catch (error: any) {
    console.error('Error getting invoice:', error);
    return res.status(500).json({ message: 'Failed to get invoice', error: error.message });
  }
};

// Get all invoices for a tenant
export const getInvoices = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId } = req.user;
    const { status, customerId, page = 1, limit = 10 } = req.query;

    // Build filter conditions
    const where: any = { tenantId };

    if (status) {
      where.status = status;
    }

    if (customerId) {
      where.customerId = customerId;
    }

    // Calculate pagination
    const skip = (Number(page) - 1) * Number(limit);

    // Get invoices with pagination
    const invoices = await prisma.invoice.findMany({
      where,
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        _count: {
          select: {
            items: true,
            payments: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      skip,
      take: Number(limit),
    });

    // Get total count for pagination
    const total = await prisma.invoice.count({ where });

    return res.status(200).json({
      invoices,
      pagination: {
        total,
        page: Number(page),
        limit: Number(limit),
        totalPages: Math.ceil(total / Number(limit)),
      },
    });
  } catch (error: any) {
    console.error('Error getting invoices:', error);
    return res.status(500).json({ message: 'Failed to get invoices', error: error.message });
  }
};

// Update invoice
export const updateInvoice = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId } = req.user;
    const { id } = req.params;
    const {
      invoiceNumber,
      dueDate,
      status,
      customerId,
      items,
      tax,
      notes,
    } = req.body;

    // Check if invoice exists and belongs to the tenant
    const existingInvoice = await prisma.invoice.findFirst({
      where: {
        id,
        tenantId,
      },
    });

    if (!existingInvoice) {
      return res.status(404).json({ message: 'Invoice not found or does not belong to your tenant' });
    }

    // Check if invoice number is being changed and already exists
    if (invoiceNumber && invoiceNumber !== existingInvoice.invoiceNumber) {
      const duplicateInvoice = await prisma.invoice.findFirst({
        where: {
          invoiceNumber,
          tenantId,
          id: { not: id },
        },
      });

      if (duplicateInvoice) {
        return res.status(400).json({ message: 'Invoice number already exists for this tenant' });
      }
    }

    // Calculate total amount if items are provided
    let totalAmount = existingInvoice.totalAmount;
    if (items && items.length > 0) {
      totalAmount = items.reduce(
        (sum: number, item: { quantity: number; unitPrice: number }) =>
          sum + item.quantity * item.unitPrice,
        0
      );
    }

    // Update invoice and items in a transaction
    const updatedInvoice = await prisma.$transaction(async (tx: any) => {
      // Update invoice
      const invoice = await tx.invoice.update({
        where: { id },
        data: {
          invoiceNumber,
          dueDate: dueDate ? new Date(dueDate) : undefined,
          status,
          totalAmount,
          tax,
          notes,
          customerId,
        },
      });

      // Update items if provided
      if (items && items.length > 0) {
        // Delete existing items
        await tx.invoiceItem.deleteMany({
          where: { invoiceId: id },
        });

        // Create new items
        for (const item of items) {
          await tx.invoiceItem.create({
            data: {
              description: item.description,
              quantity: item.quantity,
              unitPrice: item.unitPrice,
              amount: item.quantity * item.unitPrice,
              invoiceId: id,
            },
          });
        }
      }

      return invoice;
    });

    return res.status(200).json({
      message: 'Invoice updated successfully',
      invoice: updatedInvoice,
    });
  } catch (error: any) {
    console.error('Error updating invoice:', error);
    return res.status(500).json({ message: 'Failed to update invoice', error: error.message });
  }
};

// Delete invoice
export const deleteInvoice = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId } = req.user;
    const { id } = req.params;

    // Check if invoice exists and belongs to the tenant
    const existingInvoice = await prisma.invoice.findFirst({
      where: {
        id,
        tenantId,
      },
    });

    if (!existingInvoice) {
      return res.status(404).json({ message: 'Invoice not found or does not belong to your tenant' });
    }

    // Delete invoice (cascade will delete related items, payments, and reminders)
    await prisma.invoice.delete({
      where: { id },
    });

    return res.status(200).json({ message: 'Invoice deleted successfully' });
  } catch (error: any) {
    console.error('Error deleting invoice:', error);
    return res.status(500).json({ message: 'Failed to delete invoice', error: error.message });
  }
};
