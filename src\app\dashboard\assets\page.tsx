'use client';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { PlusCircle } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useRouter } from 'next/navigation';

// Mock data for demonstration
const mockAssets = [
  {
    id: '1',
    assetNumber: 'ASSET-001',
    name: 'Dell Laptop XPS 15',
    category: 'COMPUTER',
    purchaseDate: '2022-01-15',
    purchasePrice: 5000.00,
    currentValue: 4000.00,
    location: 'Main Office',
    assignedTo: '<PERSON>',
    status: 'ACTIVE',
  },
  {
    id: '2',
    assetNumber: 'ASSET-002',
    name: 'Office Desk',
    category: 'FURNITURE',
    purchaseDate: '2021-05-10',
    purchasePrice: 1200.00,
    currentValue: 1000.00,
    location: 'Main Office',
    assignedTo: '<PERSON>',
    status: 'ACTIVE',
  },
  {
    id: '3',
    assetNumber: 'ASSET-003',
    name: 'Company Car',
    category: 'VEHICLE',
    purchaseDate: '2020-10-05',
    purchasePrice: 80000.00,
    currentValue: 60000.00,
    location: 'Main Office',
    assignedTo: 'Transport Department',
    status: 'MAINTENANCE',
  },
];

const mockDepreciations = [
  {
    id: '1',
    assetId: '1',
    assetName: 'Dell Laptop XPS 15',
    depreciationDate: '2022-12-31',
    depreciationAmount: 1000.00,
    bookValue: 4000.00,
  },
  {
    id: '2',
    assetId: '2',
    assetName: 'Office Desk',
    depreciationDate: '2022-12-31',
    depreciationAmount: 200.00,
    bookValue: 1000.00,
  },
  {
    id: '3',
    assetId: '3',
    assetName: 'Company Car',
    depreciationDate: '2022-12-31',
    depreciationAmount: 20000.00,
    bookValue: 60000.00,
  },
];

const mockMaintenances = [
  {
    id: '1',
    assetId: '3',
    assetName: 'Company Car',
    maintenanceDate: '2023-05-15',
    description: 'Regular service',
    cost: 500.00,
    provider: 'Auto Service Center',
    nextMaintenanceDate: '2023-11-15',
  },
];

// Column definitions for tables
const assetColumns: ColumnDef<any>[] = [
  {
    accessorKey: 'assetNumber',
    header: 'Asset Number',
  },
  {
    accessorKey: 'name',
    header: 'Name',
  },
  {
    accessorKey: 'category',
    header: 'Category',
  },
  {
    accessorKey: 'purchaseDate',
    header: 'Purchase Date',
    cell: ({ row }) => {
      return new Date(row.getValue('purchaseDate')).toLocaleDateString();
    },
  },
  {
    accessorKey: 'currentValue',
    header: 'Current Value',
    cell: ({ row }) => {
      return `RM ${row.getValue('currentValue').toFixed(2)}`;
    },
  },
  {
    accessorKey: 'assignedTo',
    header: 'Assigned To',
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.getValue('status');
      return status === 'ACTIVE' ? (
        <Badge className="bg-green-500">Active</Badge>
      ) : status === 'MAINTENANCE' ? (
        <Badge className="bg-yellow-500">Maintenance</Badge>
      ) : status === 'DISPOSED' ? (
        <Badge variant="destructive">Disposed</Badge>
      ) : (
        <Badge variant="outline">{status}</Badge>
      );
    },
  },
  {
    id: 'actions',
    cell: ({ row }) => {
      return (
        <div className="flex items-center justify-end gap-2">
          <Button variant="ghost" size="sm">
            View
          </Button>
          <Button variant="ghost" size="sm">
            Edit
          </Button>
        </div>
      );
    },
  },
];

const depreciationColumns: ColumnDef<any>[] = [
  {
    accessorKey: 'assetName',
    header: 'Asset',
  },
  {
    accessorKey: 'depreciationDate',
    header: 'Date',
    cell: ({ row }) => {
      return new Date(row.getValue('depreciationDate')).toLocaleDateString();
    },
  },
  {
    accessorKey: 'depreciationAmount',
    header: 'Depreciation Amount',
    cell: ({ row }) => {
      return `RM ${row.getValue('depreciationAmount').toFixed(2)}`;
    },
  },
  {
    accessorKey: 'bookValue',
    header: 'Book Value After',
    cell: ({ row }) => {
      return `RM ${row.getValue('bookValue').toFixed(2)}`;
    },
  },
  {
    id: 'actions',
    cell: ({ row }) => {
      return (
        <Button variant="ghost" size="sm">
          View
        </Button>
      );
    },
  },
];

const maintenanceColumns: ColumnDef<any>[] = [
  {
    accessorKey: 'assetName',
    header: 'Asset',
  },
  {
    accessorKey: 'maintenanceDate',
    header: 'Date',
    cell: ({ row }) => {
      return new Date(row.getValue('maintenanceDate')).toLocaleDateString();
    },
  },
  {
    accessorKey: 'description',
    header: 'Description',
  },
  {
    accessorKey: 'cost',
    header: 'Cost',
    cell: ({ row }) => {
      return `RM ${row.getValue('cost').toFixed(2)}`;
    },
  },
  {
    accessorKey: 'provider',
    header: 'Provider',
  },
  {
    accessorKey: 'nextMaintenanceDate',
    header: 'Next Maintenance',
    cell: ({ row }) => {
      return row.getValue('nextMaintenanceDate')
        ? new Date(row.getValue('nextMaintenanceDate')).toLocaleDateString()
        : '-';
    },
  },
  {
    id: 'actions',
    cell: ({ row }) => {
      return (
        <div className="flex items-center justify-end gap-2">
          <Button variant="ghost" size="sm">
            Edit
          </Button>
          <Button variant="ghost" size="sm" className="text-red-500">
            Delete
          </Button>
        </div>
      );
    },
  },
];

export default function AssetsPage() {
  const { toast } = useToast();
  const router = useRouter();

  return (
    <div className="space-y-4">
      {/* Page header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between bg-white p-3 md:p-4 rounded-lg shadow-sm mb-3">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <svg className="w-6 h-6 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            Fixed Assets
          </h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage assets, depreciation, and maintenance
          </p>
        </div>
        <div className="mt-4 md:mt-0 flex space-x-3">
          <Button variant="outline" size="sm">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
            </svg>
            Filter
          </Button>
          <Button size="sm" className="bg-indigo-600 hover:bg-indigo-700">
            <PlusCircle className="mr-2 h-4 w-4" /> Add Asset
          </Button>
        </div>
      </div>


      {/* Assets Table */}
      <Card className="border-none shadow-md">
        <CardHeader className="pb-2 border-b">
          <CardTitle className="text-lg font-bold">Fixed Assets</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full text-sm text-left">
              <thead className="text-xs font-medium text-gray-500 bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-6 py-3">Asset Number</th>
                  <th className="px-6 py-3">Name</th>
                  <th className="px-6 py-3">Category</th>
                  <th className="px-6 py-3">Purchase Date</th>
                  <th className="px-6 py-3">Current Value</th>
                  <th className="px-6 py-3">Assigned To</th>
                  <th className="px-6 py-3">Status</th>
                  <th className="px-6 py-3 text-right">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {mockAssets.map((asset) => (
                  <tr key={asset.id} className="bg-white hover:bg-gray-50 transition-colors">
                    <td className="px-6 py-4 font-medium text-indigo-600">
                      {asset.assetNumber}
                    </td>
                    <td className="px-6 py-4 font-medium">
                      {asset.name}
                    </td>
                    <td className="px-6 py-4">
                      {asset.category === 'COMPUTER' ? 'Computer' :
                       asset.category === 'FURNITURE' ? 'Furniture' :
                       asset.category === 'VEHICLE' ? 'Vehicle' : asset.category}
                    </td>
                    <td className="px-6 py-4 text-gray-500">
                      {new Date(asset.purchaseDate).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 font-medium">
                      RM {asset.currentValue.toFixed(2)}
                    </td>
                    <td className="px-6 py-4">
                      {asset.assignedTo}
                    </td>
                    <td className="px-6 py-4">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        asset.status === 'ACTIVE' ? 'bg-green-100 text-green-800' :
                        asset.status === 'MAINTENANCE' ? 'bg-yellow-100 text-yellow-800' :
                        asset.status === 'DISPOSED' ? 'bg-red-100 text-red-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {asset.status === 'ACTIVE' && (
                          <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        )}
                        {asset.status === 'MAINTENANCE' && (
                          <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        )}
                        {asset.status === 'ACTIVE' ? 'Active' :
                         asset.status === 'MAINTENANCE' ? 'Maintenance' :
                         asset.status === 'DISPOSED' ? 'Disposed' : asset.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 text-right">
                      <div className="flex justify-end space-x-2">
                        <Button variant="ghost" size="sm" className="text-indigo-600 hover:text-indigo-800 hover:bg-indigo-50">
                          View
                        </Button>
                        <Button variant="ghost" size="sm" className="text-gray-600 hover:text-gray-800 hover:bg-gray-50">
                          Edit
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          <div className="flex items-center justify-between p-6 border-t border-gray-200">
            <div className="text-sm text-gray-500">
              Showing <span className="font-medium">1</span> to <span className="font-medium">{mockAssets.length}</span> of <span className="font-medium">{mockAssets.length}</span> results
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm" disabled>
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                Previous
              </Button>
              <Button variant="outline" size="sm">
                Next
                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
