'use client';

import { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import {
  Plus,
  Edit,
  Trash,
  MoveVertical,
  BarChart as BarChartIcon,
  LineChart as LineChartIcon,
  <PERSON>Chart as PieChartIcon,
  Table,
  Save,
  X
} from 'lucide-react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { DatePicker } from '@/components/ui/date-picker';
import { format, subMonths } from 'date-fns';
import reportService, { Dashboard, DashboardWidget } from '@/lib/api/report.service';

// Mock data for demonstration
const mockSalesData = [
  { month: 'Jan', revenue: 18000, expenses: 15000, profit: 3000 },
  { month: 'Feb', revenue: 20000, expenses: 16000, profit: 4000 },
  { month: 'Mar', revenue: 22000, expenses: 17000, profit: 5000 },
  { month: 'Apr', revenue: 25000, expenses: 18000, profit: 7000 },
  { month: 'May', revenue: 23000, expenses: 19000, profit: 4000 },
  { month: 'Jun', revenue: 21000, expenses: 17500, profit: 3500 },
];

const mockProductData = [
  { name: 'Premium Package', value: 67500, color: '#8884d8' },
  { name: 'Standard Service', value: 60000, color: '#83a6ed' },
  { name: 'Basic Plan', value: 25500, color: '#8dd1e1' },
  { name: 'Consulting Hours', value: 30000, color: '#82ca9d' },
  { name: 'Custom Development', value: 45000, color: '#a4de6c' },
];

const mockCustomerData = [
  { name: 'Acme Corporation', value: 45000, color: '#8884d8' },
  { name: 'TechStart Inc.', value: 32000, color: '#83a6ed' },
  { name: 'Global Enterprises', value: 60000, color: '#8dd1e1' },
  { name: 'Local Business', value: 18000, color: '#82ca9d' },
  { name: 'Mega Industries', value: 40000, color: '#a4de6c' },
];

const mockInvoiceData = [
  { id: 'INV-001', customer: 'Acme Corporation', amount: 5000, status: 'Paid', date: '2023-06-15' },
  { id: 'INV-002', customer: 'TechStart Inc.', amount: 3500, status: 'Pending', date: '2023-06-18' },
  { id: 'INV-003', customer: 'Global Enterprises', amount: 7500, status: 'Paid', date: '2023-06-20' },
  { id: 'INV-004', customer: 'Local Business', amount: 2000, status: 'Overdue', date: '2023-06-10' },
  { id: 'INV-005', customer: 'Mega Industries', amount: 4500, status: 'Paid', date: '2023-06-22' },
];

// Mock dashboards
const mockDashboards: Dashboard[] = [
  {
    id: '1',
    name: 'Sales Overview',
    description: 'Key sales metrics and trends',
    isDefault: true,
    widgets: [
      {
        id: '1',
        type: 'metric',
        title: 'Total Revenue',
        size: 'small',
        position: 1,
        data: { value: 195000, change: 12.5, changeType: 'increase' },
      },
      {
        id: '2',
        type: 'metric',
        title: 'Total Expenses',
        size: 'small',
        position: 2,
        data: { value: 102500, change: 5.2, changeType: 'increase' },
      },
      {
        id: '3',
        type: 'metric',
        title: 'Net Profit',
        size: 'small',
        position: 3,
        data: { value: 92500, change: 18.3, changeType: 'increase' },
      },
      {
        id: '4',
        type: 'chart',
        title: 'Monthly Revenue',
        size: 'medium',
        position: 4,
        chartType: 'bar',
        data: mockSalesData,
      },
      {
        id: '5',
        type: 'chart',
        title: 'Sales by Product',
        size: 'medium',
        position: 5,
        chartType: 'pie',
        data: mockProductData,
      },
      {
        id: '6',
        type: 'table',
        title: 'Recent Invoices',
        size: 'large',
        position: 6,
        data: mockInvoiceData,
      },
    ],
  },
  {
    id: '2',
    name: 'Financial Health',
    description: 'Financial metrics and analysis',
    isDefault: false,
    widgets: [
      {
        id: '7',
        type: 'metric',
        title: 'Cash Balance',
        size: 'small',
        position: 1,
        data: { value: 125000, change: 8.3, changeType: 'increase' },
      },
      {
        id: '8',
        type: 'metric',
        title: 'Accounts Receivable',
        size: 'small',
        position: 2,
        data: { value: 75000, change: -3.2, changeType: 'decrease' },
      },
      {
        id: '9',
        type: 'metric',
        title: 'Accounts Payable',
        size: 'small',
        position: 3,
        data: { value: 45000, change: 2.1, changeType: 'increase' },
      },
      {
        id: '10',
        type: 'chart',
        title: 'Profit Trend',
        size: 'medium',
        position: 4,
        chartType: 'line',
        data: mockSalesData,
      },
    ],
  },
];

export default function DashboardPage() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [dashboards, setDashboards] = useState<Dashboard[]>(mockDashboards);
  const [selectedDashboard, setSelectedDashboard] = useState<Dashboard | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isAddWidgetDialogOpen, setIsAddWidgetDialogOpen] = useState(false);
  const [isAddDashboardDialogOpen, setIsAddDashboardDialogOpen] = useState(false);

  // New dashboard form state
  const [newDashboard, setNewDashboard] = useState({
    name: '',
    description: '',
    isDefault: false,
  });

  // New widget form state
  const [newWidget, setNewWidget] = useState({
    type: 'metric',
    title: '',
    size: 'medium',
    chartType: 'bar',
    reportType: 'sales',
  });

  // Load dashboards
  useEffect(() => {
    loadDashboards();
  }, []);

  const loadDashboards = async () => {
    setIsLoading(true);
    try {
      // In a real app, this would be an API call
      // const dashboardsData = await reportService.getDashboards();
      // setDashboards(dashboardsData);

      // Use mock data
      setDashboards(mockDashboards);

      // Set default dashboard
      const defaultDashboard = mockDashboards.find(d => d.isDefault) || mockDashboards[0];
      setSelectedDashboard(defaultDashboard);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to load dashboards',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDashboardChange = (dashboardId: string) => {
    const dashboard = dashboards.find(d => d.id === dashboardId);
    if (dashboard) {
      setSelectedDashboard(dashboard);
      setIsEditMode(false);
    }
  };

  const handleAddDashboard = async () => {
    setIsLoading(true);
    try {
      // In a real app, this would be an API call
      // const newDashboardData = await reportService.createDashboard(newDashboard);

      // Mock creating a new dashboard
      const newDashboardData: Dashboard = {
        id: `dashboard-${Date.now()}`,
        name: newDashboard.name,
        description: newDashboard.description,
        isDefault: newDashboard.isDefault,
        widgets: [],
      };

      // Update dashboards list
      setDashboards([...dashboards, newDashboardData]);

      // Select the new dashboard
      setSelectedDashboard(newDashboardData);

      // Reset form
      setNewDashboard({
        name: '',
        description: '',
        isDefault: false,
      });

      // Close dialog
      setIsAddDashboardDialogOpen(false);

      toast({
        title: 'Dashboard Created',
        description: 'The dashboard has been created successfully.',
        variant: 'default',
      });
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create dashboard',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddWidget = async () => {
    if (!selectedDashboard) return;

    setIsLoading(true);
    try {
      // In a real app, this would be an API call
      // const newWidgetData = await reportService.addDashboardWidget(selectedDashboard.id, {
      //   type: newWidget.type,
      //   title: newWidget.title,
      //   size: newWidget.size,
      //   chartType: newWidget.type === 'chart' ? newWidget.chartType : undefined,
      //   reportType: newWidget.reportType,
      // });

      // Mock creating a new widget
      let widgetData: any;

      if (newWidget.type === 'metric') {
        widgetData = { value: 50000, change: 10, changeType: 'increase' };
      } else if (newWidget.type === 'chart') {
        if (newWidget.chartType === 'pie') {
          widgetData = newWidget.reportType === 'sales' ? mockProductData : mockCustomerData;
        } else {
          widgetData = mockSalesData;
        }
      } else if (newWidget.type === 'table') {
        widgetData = mockInvoiceData;
      }

      const newWidgetData: DashboardWidget = {
        id: `widget-${Date.now()}`,
        type: newWidget.type as any,
        title: newWidget.title,
        size: newWidget.size as any,
        position: selectedDashboard.widgets.length + 1,
        chartType: newWidget.type === 'chart' ? newWidget.chartType as any : undefined,
        data: widgetData,
      };

      // Update dashboard
      const updatedDashboard = {
        ...selectedDashboard,
        widgets: [...selectedDashboard.widgets, newWidgetData],
      };

      // Update dashboards list
      setDashboards(dashboards.map(d => d.id === selectedDashboard.id ? updatedDashboard : d));

      // Update selected dashboard
      setSelectedDashboard(updatedDashboard);

      // Reset form
      setNewWidget({
        type: 'metric',
        title: '',
        size: 'medium',
        chartType: 'bar',
        reportType: 'sales',
      });

      // Close dialog
      setIsAddWidgetDialogOpen(false);

      toast({
        title: 'Widget Added',
        description: 'The widget has been added to the dashboard.',
        variant: 'default',
      });
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to add widget',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveWidget = async (widgetId: string) => {
    if (!selectedDashboard) return;

    setIsLoading(true);
    try {
      // In a real app, this would be an API call
      // await reportService.deleteDashboardWidget(selectedDashboard.id, widgetId);

      // Update dashboard
      const updatedDashboard = {
        ...selectedDashboard,
        widgets: selectedDashboard.widgets.filter(w => w.id !== widgetId),
      };

      // Update dashboards list
      setDashboards(dashboards.map(d => d.id === selectedDashboard.id ? updatedDashboard : d));

      // Update selected dashboard
      setSelectedDashboard(updatedDashboard);

      toast({
        title: 'Widget Removed',
        description: 'The widget has been removed from the dashboard.',
        variant: 'default',
      });
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to remove widget',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-MY', {
      style: 'currency',
      currency: 'MYR',
      minimumFractionDigits: 2,
    }).format(value);
  };

  const renderWidget = (widget: DashboardWidget) => {
    const widgetSizeClass = widget.size === 'small' ? 'col-span-1' :
                           widget.size === 'medium' ? 'col-span-2' :
                           'col-span-3';

    return (
      <Card key={widget.id} className={`${widgetSizeClass} ${isEditMode ? 'border-dashed border-2 border-gray-300' : ''}`}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">{widget.title}</CardTitle>
          {isEditMode && (
            <Button variant="ghost" size="sm" onClick={() => handleRemoveWidget(widget.id)}>
              <Trash className="h-4 w-4 text-red-500" />
            </Button>
          )}
        </CardHeader>
        <CardContent>
          {widget.type === 'metric' && (
            <div className="space-y-2">
              <div className="text-2xl font-bold">{formatCurrency(widget.data.value)}</div>
              <div className="flex items-center text-xs">
                {widget.data.changeType === 'increase' ? (
                  <span className="text-green-500">↑ {widget.data.change}%</span>
                ) : (
                  <span className="text-red-500">↓ {widget.data.change}%</span>
                )}
                <span className="text-text-secondary ml-1">from previous period</span>
              </div>
            </div>
          )}

          {widget.type === 'chart' && widget.chartType === 'bar' && (
            <div className="h-60">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={widget.data}
                  margin={{ top: 10, right: 10, left: 0, bottom: 20 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip formatter={(value) => formatCurrency(value as number)} />
                  <Legend />
                  <Bar dataKey="revenue" name="Revenue" fill="#8884d8" />
                  <Bar dataKey="expenses" name="Expenses" fill="#82ca9d" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          )}

          {widget.type === 'chart' && widget.chartType === 'line' && (
            <div className="h-60">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={widget.data}
                  margin={{ top: 10, right: 10, left: 0, bottom: 20 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip formatter={(value) => formatCurrency(value as number)} />
                  <Legend />
                  <Line type="monotone" dataKey="profit" name="Profit" stroke="#8884d8" activeDot={{ r: 8 }} />
                </LineChart>
              </ResponsiveContainer>
            </div>
          )}

          {widget.type === 'chart' && widget.chartType === 'pie' && (
            <div className="h-60">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={widget.data}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    outerRadius={60}
                    fill="#8884d8"
                    dataKey="value"
                    nameKey="name"
                  >
                    {widget.data.map((entry: any, index: number) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => formatCurrency(value as number)} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          )}

          {widget.type === 'table' && (
            <div className="overflow-x-auto">
              <table className="w-full text-sm text-left" className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-2 font-medium">Invoice</th>
                    <th className="text-left py-2 font-medium">Customer</th>
                    <th className="text-right py-2 font-medium">Amount</th>
                    <th className="text-right py-2 font-medium">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {widget.data.map((row: any) => (
                    <tr key={row.id} className="border-b">
                      <td className="py-2">{row.id}</td>
                      <td className="py-2">{row.customer}</td>
                      <td className="py-2 text-right">{formatCurrency(row.amount)}</td>
                      <td className="py-2 text-right">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          row.status === 'Paid' ? 'bg-green-100 text-green-800' :
                          row.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {row.status}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between bg-white p-6 rounded-lg shadow-sm mb-6">
        <div>
          <h1 className="text-2xl font-bold">Dashboard</h1>
          <p className="text-text-secondary">Visualize your business performance</p>
        </div>
        <div className="mt-4 md:mt-0 flex flex-col sm:flex-row gap-2">
          <Select value={selectedDashboard?.id} onValueChange={handleDashboardChange}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Select Dashboard" />
            </SelectTrigger>
            <SelectContent>
              {dashboards.map(dashboard => (
                <SelectItem key={dashboard.id} value={dashboard.id}>
                  {dashboard.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Dialog open={isAddDashboardDialogOpen} onOpenChange={setIsAddDashboardDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                <Plus className="w-4 h-4 mr-2" />
                New Dashboard
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Dashboard</DialogTitle>
                <DialogDescription>
                  Create a new dashboard to visualize your business data.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <Input
                  label="Dashboard Name"
                  value={newDashboard.name}
                  onChange={(e) => setNewDashboard({ ...newDashboard, name: e.target.value })}
                  required
                />
                <Input
                  label="Description"
                  value={newDashboard.description}
                  onChange={(e) => setNewDashboard({ ...newDashboard, description: e.target.value })}
                />
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="isDefault"
                    checked={newDashboard.isDefault}
                    onChange={(e) => setNewDashboard({ ...newDashboard, isDefault: e.target.checked })}
                    className="rounded border-gray-300"
                  />
                  <label htmlFor="isDefault" className="text-sm font-medium">
                    Set as default dashboard
                  </label>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddDashboardDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleAddDashboard} disabled={!newDashboard.name || isLoading}>
                  {isLoading ? 'Creating...' : 'Create Dashboard'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {selectedDashboard && (
            <>
              {isEditMode ? (
                <Button variant="default" onClick={() => setIsEditMode(false)}>
                  <Save className="w-4 h-4 mr-2" />
                  Save Changes
                </Button>
              ) : (
                <Button variant="outline" onClick={() => setIsEditMode(true)}>
                  <Edit className="w-4 h-4 mr-2" />
                  Edit Dashboard
                </Button>
              )}
            </>
          )}
        </div>
      </div>

      {selectedDashboard && (
        <>
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold">{selectedDashboard.name}</h2>
              {selectedDashboard.description && (
                <p className="text-text-secondary">{selectedDashboard.description}</p>
              )}
            </div>

            {isEditMode && (
              <Dialog open={isAddWidgetDialogOpen} onOpenChange={setIsAddWidgetDialogOpen}>
                <DialogTrigger asChild>
                  <Button size="sm" className="bg-indigo-600 hover:bg-indigo-700">
                    <Plus className="w-4 h-4 mr-2" />
                    Add Widget
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Add Widget</DialogTitle>
                    <DialogDescription>
                      Add a new widget to your dashboard.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4 py-4">
                    <Input
                      label="Widget Title"
                      value={newWidget.title}
                      onChange={(e) => setNewWidget({ ...newWidget, title: e.target.value })}
                      required
                    />

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Widget Type</label>
                      <Select
                        value={newWidget.type}
                        onValueChange={(value) => setNewWidget({ ...newWidget, type: value })}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select widget type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="metric">Metric</SelectItem>
                          <SelectItem value="chart">Chart</SelectItem>
                          <SelectItem value="table">Table</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Widget Size</label>
                      <Select
                        value={newWidget.size}
                        onValueChange={(value) => setNewWidget({ ...newWidget, size: value })}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select widget size" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="small">Small</SelectItem>
                          <SelectItem value="medium">Medium</SelectItem>
                          <SelectItem value="large">Large</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {newWidget.type === 'chart' && (
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Chart Type</label>
                        <Select
                          value={newWidget.chartType}
                          onValueChange={(value) => setNewWidget({ ...newWidget, chartType: value })}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select chart type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="bar">Bar Chart</SelectItem>
                            <SelectItem value="line">Line Chart</SelectItem>
                            <SelectItem value="pie">Pie Chart</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    )}

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Data Source</label>
                      <Select
                        value={newWidget.reportType}
                        onValueChange={(value) => setNewWidget({ ...newWidget, reportType: value })}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select data source" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="sales">Sales Data</SelectItem>
                          <SelectItem value="financial">Financial Data</SelectItem>
                          <SelectItem value="inventory">Inventory Data</SelectItem>
                          <SelectItem value="customers">Customer Data</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setIsAddWidgetDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleAddWidget} disabled={!newWidget.title || isLoading}>
                      {isLoading ? 'Adding...' : 'Add Widget'}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {selectedDashboard.widgets.map(widget => renderWidget(widget))}
          </div>
        </>
      )}
    </div>
  );
}
