// Notification system schema extensions

// Email configuration for tenants
model EmailSetting {
  id                String              @id @default(uuid())
  host              String              // SMTP host
  port              Int                 // SMTP port
  username          String              // SMTP username
  password          String              @db.Text // SMTP password (encrypted)
  encryption        String              @default("tls") // tls, ssl, none
  fromEmail         String              // From email address
  fromName          String              // From name
  isActive          Boolean             @default(true)
  tenantId          String              @unique
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
}

// Email templates for different notification types
model EmailTemplate {
  id                String              @id @default(uuid())
  name              String              // Template name (invoice_created, payment_reminder, etc.)
  subject           String              // Email subject
  body              String              @db.Text // Email body (HTML)
  variables         String?             @db.Text // JSON string of available variables
  isDefault         Boolean             @default(false) // Is this a default template?
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@unique([name, tenantId])
  @@index([tenantId])
}

// WhatsApp message templates
model WhatsAppTemplate {
  id                String              @id @default(uuid())
  name              String              // Template name (invoice_created, payment_reminder, etc.)
  content           String              @db.Text // Template content
  variables         String?             @db.Text // JSON string of available variables
  status            String              @default("PENDING") // PENDING, APPROVED, REJECTED
  isDefault         Boolean             @default(false) // Is this a default template?
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@unique([name, tenantId])
  @@index([tenantId])
}

// WhatsApp messages sent
model WhatsAppMessage {
  id                String              @id @default(uuid())
  to                String              // Recipient phone number
  templateName      String              // Template name used
  templateData      String              @db.Text // JSON string of template data
  status            String              @default("QUEUED") // QUEUED, SENT, DELIVERED, READ, FAILED
  providerMessageId String?             // Message ID from provider (Twilio, etc.)
  error             String?             @db.Text // Error message if failed
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  relatedId         String?             // Related entity ID (invoice, etc.)
  relatedType       String?             // Related entity type (invoice, etc.)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([tenantId])
  @@index([status])
  @@index([createdAt])
}

// Notification settings for tenants
model NotificationSetting {
  id                String              @id @default(uuid())
  invoiceCreated    Json                // Settings for invoice created notifications
  paymentReminder   Json                // Settings for payment reminder notifications
  paymentOverdue    Json                // Settings for payment overdue notifications
  paymentReceived   Json                // Settings for payment received notifications
  businessHours     Json                // Business hours settings
  tenantId          String              @unique
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
}

// Customer notification preferences
model CustomerNotificationPreference {
  id                String              @id @default(uuid())
  preferredChannel  String              @default("default") // default, email, whatsapp, both
  optOut            Boolean             @default(false) // Opt out of all notifications
  customReminderDays Boolean            @default(false) // Use custom reminder days
  reminderDaysBefore Int                @default(3) // Days before due date to send reminder
  customerId        String              @unique
  customer          Customer            @relation(fields: [customerId], references: [id], onDelete: Cascade)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
}

// Invoice notification settings
model InvoiceNotification {
  id                String              @id @default(uuid())
  autoSend          Boolean             @default(true) // Automatically send invoice
  channels          Json                // Email, WhatsApp, or both
  reminders         Json                // Reminder settings
  escalation        Json                // Escalation settings
  invoiceId         String              @unique
  invoice           Invoice             @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
}

// Notification history
model Notification {
  id                String              @id @default(uuid())
  type              String              // invoice_created, payment_reminder, etc.
  channel           String              // email, whatsapp
  recipient         String              // Email or phone number
  recipientName     String?             // Recipient name
  subject           String?             // Email subject or message preview
  content           String?             @db.Text // Full message content
  status            String              // queued, sent, delivered, read, failed
  error             String?             @db.Text // Error message if failed
  sentAt            DateTime            // When the notification was sent
  deliveredAt       DateTime?           // When the notification was delivered
  readAt            DateTime?           // When the notification was read
  relatedId         String?             // Related entity ID (invoice, etc.)
  relatedType       String?             // Related entity type (invoice, etc.)
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([tenantId])
  @@index([type])
  @@index([channel])
  @@index([status])
  @@index([sentAt])
  @@index([relatedId, relatedType])
}

// Notification queue for scheduled notifications
model NotificationQueue {
  id                String              @id @default(uuid())
  type              String              // invoice_created, payment_reminder, etc.
  channel           String              // email, whatsapp, both
  data              String              @db.Text // JSON string of notification data
  scheduledFor      DateTime            // When to send the notification
  status            String              @default("PENDING") // PENDING, PROCESSING, COMPLETED, FAILED
  attempts          Int                 @default(0) // Number of attempts
  lastAttempt       DateTime?           // Last attempt time
  error             String?             @db.Text // Error message if failed
  relatedId         String?             // Related entity ID (invoice, etc.)
  relatedType       String?             // Related entity type (invoice, etc.)
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([tenantId])
  @@index([status])
  @@index([scheduledFor])
  @@index([relatedId, relatedType])
}
