import request from 'supertest';
import express from 'express';
import { prisma } from '../../index';
import authRoutes from '../../routes/auth.routes';
import bcrypt from 'bcrypt';

// Create a test app
const app = express();
app.use(express.json());
app.use('/api/auth', authRoutes);

describe('Auth Controller', () => {
  // Test user data
  const testUser = {
    name: 'Test User',
    email: '<EMAIL>',
    password: 'Password123!',
    tenantId: '',
  };

  // Create a test tenant and user before tests
  beforeAll(async () => {
    // Create a test tenant
    const tenant = await prisma.tenant.create({
      data: {
        name: 'Test Company',
        businessName: 'Test Company Sdn Bhd',
        email: '<EMAIL>',
      },
    });
    
    testUser.tenantId = tenant.id;
    
    // Create a test user
    const hashedPassword = await bcrypt.hash(testUser.password, 10);
    await prisma.user.create({
      data: {
        name: testUser.name,
        email: testUser.email,
        password: hashedPassword,
        tenantId: tenant.id,
      },
    });
  });
  
  // Clean up after tests
  afterAll(async () => {
    await prisma.user.deleteMany({
      where: { email: testUser.email },
    });
    
    await prisma.tenant.deleteMany({
      where: { email: '<EMAIL>' },
    });
  });
  
  describe('POST /api/auth/login', () => {
    it('should login a user with valid credentials', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password,
        });
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('accessToken');
      expect(response.body).toHaveProperty('refreshToken');
      expect(response.body).toHaveProperty('user');
      expect(response.body.user.email).toBe(testUser.email);
    });
    
    it('should return 401 with invalid credentials', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: 'wrongpassword',
        });
      
      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('message');
    });
  });
  
  describe('POST /api/auth/register', () => {
    const newUser = {
      name: 'New User',
      email: '<EMAIL>',
      password: 'Password123!',
    };
    
    afterEach(async () => {
      // Clean up the created user
      await prisma.user.deleteMany({
        where: { email: newUser.email },
      });
    });
    
    it('should register a new user', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          name: newUser.name,
          email: newUser.email,
          password: newUser.password,
          tenantId: testUser.tenantId,
        });
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('accessToken');
      expect(response.body).toHaveProperty('refreshToken');
      expect(response.body).toHaveProperty('user');
      expect(response.body.user.email).toBe(newUser.email);
    });
    
    it('should return 400 with invalid data', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          name: newUser.name,
          email: 'invalid-email',
          password: 'short',
        });
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('message');
    });
  });
});
