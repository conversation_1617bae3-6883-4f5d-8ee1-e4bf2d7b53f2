{"name": "backend", "version": "1.0.0", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon --exec ts-node src/index.ts", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:deploy": "prisma migrate deploy", "prisma:studio": "prisma studio", "test": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@prisma/client": "^6.7.0", "@types/fs-extra": "^11.0.4", "@types/multer": "^1.4.12", "@types/pdfkit": "^0.13.9", "bcrypt": "^5.1.1", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.1.5", "fs-extra": "^11.3.0", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.2", "pdfkit": "^0.17.1", "pg": "^8.15.6", "prisma": "^6.7.0", "winston": "^3.17.0"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/compression": "^1.7.5", "@types/cors": "^2.8.18", "@types/express": "^5.0.1", "@types/helmet": "^4.0.0", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.15.17", "@types/uuid": "^10.0.0", "jest": "^29.7.0", "nodemon": "^3.1.10", "supertest": "^6.3.4", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}