# LHDN MyInvois API Integration Guide

This document provides instructions on how to integrate the LHDN (Lembaga Hasil Dalam Negeri) MyInvois API with the Invoix application for Malaysian tax compliance.

## Implementation Status

**Current Phase**: Production Ready

### Completed
- ✅ Basic integration structure
- ✅ Business registration validation
- ✅ Invoice validation with UBL 2.1 format
- ✅ Platform-level certificate management
- ✅ Admin configuration interface
- ✅ Invoice creation/editing with LHDN validation
- ✅ Customer TIN management
- ✅ Bulk TIN validation
- ✅ LHDN compliance dashboard
- ✅ Certificate management UI
- ✅ Secure certificate storage with encryption
- ✅ Document cancellation and rejection
- ✅ Document search and retrieval
- ✅ Enhanced error handling and resilience
- ✅ Comprehensive logging system
- ✅ Retry mechanism with exponential backoff
- ✅ Rate limiting for API calls
- ✅ Health check endpoints
- ✅ Production monitoring capabilities
- ✅ Clear separation of admin and user responsibilities
- ✅ Simplified user interface for end users
- ✅ Consistent design across all dashboard tabs

### Future Enhancements
- ⏳ Comprehensive test suite
- ⏳ Performance optimizations
- ⏳ Advanced analytics and reporting

## Overview

The LHDN MyInvois API allows businesses to validate and submit invoices to the Malaysian tax authority for compliance with e-invoicing regulations. This integration enables Invoix to:

1. Validate taxpayer TIN (Tax Identification Number)
2. Submit invoices for validation
3. Check validation status
4. Retrieve document details

## Prerequisites

Before you can use the LHDN MyInvois API, you need to:

1. Register with LHDN as a taxpayer or service provider
2. Apply for API access through the LHDN portal
3. Obtain API credentials (Client ID, Client Secret, and API Key)

## Configuration

The LHDN MyInvois API integration requires the following environment variables to be set in the `.env` file:

```
LHDN_API_BASE_URL="https://sandbox.myinvois.hasil.gov.my/einvoicing"
BUSINESS_REG_NO="your-business-registration-number"
BUSINESS_NAME="your-business-name"
LHDN_CERT_PATH="/path/to/certificates"
LHDN_CERT_FILE="your-certificate.p12"
LHDN_CERT_PASSWORD="your-certificate-password"
```

For development and testing purposes, the application will use mock data if the digital certificate is not provided.

### Digital Certificate

The LHDN MyInvois API uses digital certificates for authentication. You need to:

1. Obtain a digital certificate from LHDN or a certified certificate authority
2. Store the certificate securely (typically a .p12 file)
3. Configure the application with the certificate path and password

## API Endpoints

The Invoix application exposes the following endpoints for LHDN integration:

### 1. Validate Business Registration Number

```
GET /api/lhdn/validate-business/:regNo
```

This endpoint validates a business registration number with LHDN.

**Parameters:**
- `regNo`: The business registration number to validate

**Response:**
```json
{
  "regNo": "*********",
  "isValid": true,
  "businessName": "Example Company Sdn Bhd",
  "businessType": "COMPANY"
}
```

### 2. Validate Invoice

```
POST /api/lhdn/validate/:invoiceId
```

This endpoint validates an invoice with LHDN MyInvois.

**Parameters:**
- `invoiceId`: The ID of the invoice to validate

**Response:**
```json
{
  "message": "Invoice validated successfully with LHDN MyInvois",
  "validationId": "LHDN-12345678"
}
```

### 3. Check Validation Status

```
GET /api/lhdn/status/:invoiceId
```

This endpoint checks the validation status of an invoice.

**Parameters:**
- `invoiceId`: The ID of the invoice to check

**Response:**
```json
{
  "invoiceId": "invoice-123",
  "invoiceNumber": "INV-2023-001",
  "isValidated": true,
  "validationId": "LHDN-12345678"
}
```

## Implementation Details

The LHDN integration is implemented in the following files:

1. `src/services/lhdn.service.ts` - Core service for interacting with the LHDN MyInvois API
2. `src/controllers/lhdn.controller.ts` - Controllers for handling LHDN-related requests
3. `src/routes/lhdn.routes.ts` - Route definitions for LHDN endpoints

The service supports both real API calls using digital certificates and mock data for development/testing.

### Authentication

The LHDN MyInvois API uses digital certificates for authentication:

1. The application loads the digital certificate (.p12 file)
2. When making API calls, the certificate is used for client authentication
3. All communication is secured using HTTPS

This is different from typical API key or OAuth authentication methods.

## UBL 2.1 Format

The LHDN MyInvois API requires invoices to be submitted in UBL 2.1 format. The `convertToUBL` function in `lhdn.service.ts` converts Invoix invoice data to the required format.

For production use, you may need to enhance this function to include all required fields according to the LHDN specifications.

## Error Handling

The LHDN service includes comprehensive error handling to deal with various scenarios:

1. Authentication failures
2. Validation errors
3. API communication errors
4. Document rejection

Errors are logged and appropriate error responses are returned to the client.

## Testing

For testing purposes, you can:

1. Use the mock implementation by not setting the LHDN API credentials
2. Use the LHDN sandbox environment (if available)
3. Create test invoices and validate them through the API

## Resources

- [LHDN MyInvois API Documentation](https://sdk.myinvois.hasil.gov.my/einvoicingapi/)
- [UBL 2.1 Specification](https://docs.oasis-open.org/ubl/UBL-2.1.html)
- [LHDN Contact Information](https://sdk.myinvois.hasil.gov.my/contacts/)

## Production Readiness

The LHDN MyInvois integration has been designed with production readiness in mind. The following features ensure reliable operation in a production environment:

### Secure Certificate Management

- **Encrypted Storage**: Certificates are stored with AES-256-CBC encryption
- **Database Integration**: Certificate metadata is stored in the database while encrypted files are stored on disk
- **Access Control**: Only administrators can manage certificates
- **Certificate Rotation**: Support for multiple certificates with easy activation/deactivation

### Error Handling and Resilience

- **Retry Mechanism**: Automatic retries with exponential backoff for transient errors
- **Rate Limiting**: Protection against API rate limits with configurable thresholds
- **Graceful Degradation**: Fallback mechanisms when the LHDN API is unavailable
- **Comprehensive Logging**: Detailed logs for troubleshooting and auditing

### Monitoring and Health Checks

- **Health Check Endpoints**: `/health` and `/health/detailed` endpoints for monitoring
- **LHDN Status Monitoring**: Continuous monitoring of LHDN API connectivity
- **Certificate Validity Checks**: Automatic validation of certificate status
- **Performance Metrics**: Tracking of API call latency and success rates

### Deployment Considerations

- **Environment Variables**: All sensitive configuration is managed via environment variables
- **Database Migrations**: Schema changes are managed through migrations
- **Containerization**: Docker support for consistent deployment
- **Horizontal Scaling**: Stateless design allows for horizontal scaling

## Support

For issues related to the LHDN API integration, contact:

1. LHDN support for API-specific issues
2. Invoix support for application integration issues
