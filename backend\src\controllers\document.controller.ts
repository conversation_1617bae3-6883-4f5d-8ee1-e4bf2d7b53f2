import { Request, Response } from 'express';
import { prisma } from '../index';
import { cancelDocument, rejectDocument, searchDocuments } from '../services/lhdn.service';

/**
 * Cancel a document in LHDN MyInvois
 */
export const cancelDocumentController = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { id } = req.params;
    const { reason } = req.body;

    if (!reason) {
      return res.status(400).json({ message: 'Cancellation reason is required' });
    }

    // Check if invoice exists and belongs to the user's tenant
    const invoice = await prisma.invoice.findFirst({
      where: {
        id,
        tenantId: req.user.tenantId,
      },
    });

    if (!invoice) {
      return res.status(404).json({ message: 'Invoice not found' });
    }

    // Check if invoice has a LHDN validation ID
    if (!invoice.lhdnValidationId) {
      return res.status(400).json({ message: 'Invoice has not been validated with LHDN MyInvois' });
    }

    // Cancel document in LHDN MyInvois
    const result = await cancelDocument(invoice.lhdnValidationId, reason);

    // Update invoice status in database
    await prisma.invoice.update({
      where: { id },
      data: {
        status: 'CANCELLED',
        updatedAt: new Date(),
      },
    });

    return res.status(200).json({
      message: 'Invoice cancelled successfully',
      invoice: {
        id: invoice.id,
        status: 'CANCELLED',
      },
      lhdn: result,
    });
  } catch (error: any) {
    console.error('Error cancelling document:', error);
    return res.status(500).json({ message: 'Failed to cancel document', error: error.message });
  }
};

/**
 * Reject a document in LHDN MyInvois
 */
export const rejectDocumentController = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { id } = req.params;
    const { reason } = req.body;

    if (!reason) {
      return res.status(400).json({ message: 'Rejection reason is required' });
    }

    // Check if invoice exists and belongs to the user's tenant
    const invoice = await prisma.invoice.findFirst({
      where: {
        id,
        tenantId: req.user.tenantId,
      },
    });

    if (!invoice) {
      return res.status(404).json({ message: 'Invoice not found' });
    }

    // Check if invoice has a LHDN validation ID
    if (!invoice.lhdnValidationId) {
      return res.status(400).json({ message: 'Invoice has not been validated with LHDN MyInvois' });
    }

    // Reject document in LHDN MyInvois
    const result = await rejectDocument(invoice.lhdnValidationId, reason);

    // Update invoice status in database
    await prisma.invoice.update({
      where: { id },
      data: {
        status: 'REJECTED',
        updatedAt: new Date(),
      },
    });

    return res.status(200).json({
      message: 'Invoice rejected successfully',
      invoice: {
        id: invoice.id,
        status: 'REJECTED',
      },
      lhdn: result,
    });
  } catch (error: any) {
    console.error('Error rejecting document:', error);
    return res.status(500).json({ message: 'Failed to reject document', error: error.message });
  }
};

/**
 * Search documents in LHDN MyInvois
 */
export const searchDocumentsController = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { startDate, endDate, status, documentType, page, limit } = req.query;

    // Search documents in LHDN MyInvois
    const result = await searchDocuments({
      startDate: startDate as string,
      endDate: endDate as string,
      status: status as string,
      documentType: documentType as string,
      page: page ? parseInt(page as string) : 1,
      limit: limit ? parseInt(limit as string) : 10,
    });

    return res.status(200).json(result);
  } catch (error: any) {
    console.error('Error searching documents:', error);
    return res.status(500).json({ message: 'Failed to search documents', error: error.message });
  }
};
