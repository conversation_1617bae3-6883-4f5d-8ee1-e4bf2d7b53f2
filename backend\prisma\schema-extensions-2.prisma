model AttendanceRecord {
  id                String              @id @default(uuid())
  employeeId        String
  employee          Employee            @relation(fields: [employeeId], references: [id])
  date              DateTime
  checkIn           DateTime?
  checkOut          DateTime?
  status            AttendanceStatus    @default(PRESENT)
  notes             String?
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@unique([employeeId, date, tenantId])
  @@index([tenantId])
  @@index([employeeId])
}

model LeaveRequest {
  id                String              @id @default(uuid())
  employeeId        String
  employee          Employee            @relation(fields: [employeeId], references: [id])
  leaveType         LeaveType
  startDate         DateTime
  endDate           DateTime
  totalDays         Float
  reason            String?
  status            ApprovalStatus      @default(PENDING)
  approvedBy        String?             // User ID who approved/rejected
  approvalDate      DateTime?
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([tenantId])
  @@index([employeeId])
}

enum AttendanceStatus {
  PRESENT
  ABSENT
  LATE
  HALF_DAY
  ON_LEAVE
}

enum LeaveType {
  ANNUAL
  SICK
  MATERNITY
  PATERNITY
  UNPAID
  OTHER
}

enum ApprovalStatus {
  PENDING
  APPROVED
  REJECTED
}

// Supplier and Procurement Module
model Supplier {
  id                String              @id @default(uuid())
  name              String
  contactPerson     String?
  email             String
  phone             String?
  address           String?
  taxId             String?             // For Malaysian GST/SST
  paymentTerms      Int                 @default(30)     // Days
  isActive          Boolean             @default(true)
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  purchaseOrders    PurchaseOrder[]
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@unique([email, tenantId])
  @@index([tenantId])
}

model PurchaseOrder {
  id                String              @id @default(uuid())
  poNumber          String
  supplierId        String
  supplier          Supplier            @relation(fields: [supplierId], references: [id])
  orderDate         DateTime            @default(now())
  expectedDelivery  DateTime?
  status            PurchaseOrderStatus @default(DRAFT)
  totalAmount       Float
  tax               Float               @default(0)
  notes             String?
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  items             PurchaseOrderItem[]
  receipts          GoodsReceipt[]
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@unique([poNumber, tenantId])
  @@index([tenantId])
  @@index([supplierId])
}

model PurchaseOrderItem {
  id                String              @id @default(uuid())
  purchaseOrderId   String
  purchaseOrder     PurchaseOrder       @relation(fields: [purchaseOrderId], references: [id], onDelete: Cascade)
  productId         String
  product           Product             @relation(fields: [productId], references: [id])
  description       String
  quantity          Int
  unitPrice         Float
  amount            Float
  receivedQuantity  Int                 @default(0)
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([purchaseOrderId])
  @@index([productId])
  @@index([tenantId])
}

enum PurchaseOrderStatus {
  DRAFT
  SENT
  PARTIALLY_RECEIVED
  RECEIVED
  CANCELLED
}
