/**
 * Authentication Tests
 * 
 * This file contains tests for the authentication system.
 * Run with: npm test -- auth
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import authService from '../lib/api/auth.service';
import { login, logout, register, refreshAccessToken, isAuthenticated } from '../lib/auth';

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {};
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => { store[key] = value; },
    removeItem: (key: string) => { delete store[key]; },
    clear: () => { store = {}; },
  };
})();

// Mock fetch
global.fetch = vi.fn();

// Mock window.location
const windowLocationMock = {
  href: '',
};

Object.defineProperty(window, 'localStorage', { value: localStorageMock });
Object.defineProperty(window, 'location', { value: windowLocationMock, writable: true });

describe('Authentication Service', () => {
  beforeEach(() => {
    localStorageMock.clear();
    vi.clearAllMocks();
  });

  describe('Login', () => {
    it('should login successfully with valid credentials', async () => {
      // Mock successful login response
      (global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          message: 'Login successful',
          user: {
            id: 'user-123',
            name: 'Test User',
            email: '<EMAIL>',
            role: 'USER',
            emailVerified: true,
            tenant: {
              id: 'tenant-123',
              name: 'Test Tenant',
              businessName: 'Test Business',
            },
          },
          accessToken: 'test-access-token',
          refreshToken: 'test-refresh-token',
        }),
      });

      // Call login function
      const result = await login('<EMAIL>', 'password123');

      // Verify fetch was called with correct arguments
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/auth/login'),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
          }),
          body: JSON.stringify({
            email: '<EMAIL>',
            password: 'password123',
          }),
        })
      );

      // Verify tokens were stored in localStorage
      expect(localStorageMock.getItem('invoix_access_token')).toBe('test-access-token');
      expect(localStorageMock.getItem('invoix_refresh_token')).toBe('test-refresh-token');
      expect(localStorageMock.getItem('invoix_user')).toBeTruthy();

      // Verify return value
      expect(result.user.email).toBe('<EMAIL>');
      expect(result.accessToken).toBe('test-access-token');
    });

    it('should handle login failure', async () => {
      // Mock failed login response
      (global.fetch as any).mockResolvedValueOnce({
        ok: false,
        json: async () => ({
          message: 'Invalid credentials',
        }),
      });

      // Call login function and expect it to throw
      await expect(login('<EMAIL>', 'wrong-password')).rejects.toThrow('Invalid credentials');

      // Verify localStorage was not updated
      expect(localStorageMock.getItem('invoix_access_token')).toBeNull();
      expect(localStorageMock.getItem('invoix_refresh_token')).toBeNull();
      expect(localStorageMock.getItem('invoix_user')).toBeNull();
    });

    it('should handle 2FA requirement', async () => {
      // Mock 2FA required response
      (global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          message: 'Two-factor authentication required',
          requiresTwoFactor: true,
          twoFactorToken: 'two-factor-session-token',
        }),
      });

      // Call login function
      const result = await login('<EMAIL>', 'password123');

      // Verify return value
      expect(result.requiresTwoFactor).toBe(true);
      expect(result.twoFactorToken).toBe('two-factor-session-token');
    });
  });

  describe('Logout', () => {
    it('should logout successfully', async () => {
      // Setup localStorage with tokens
      localStorageMock.setItem('invoix_access_token', 'test-access-token');
      localStorageMock.setItem('invoix_refresh_token', 'test-refresh-token');
      localStorageMock.setItem('invoix_user', JSON.stringify({ name: 'Test User' }));

      // Mock successful logout response
      (global.fetch as any).mockResolvedValueOnce({
        ok: true,
      });

      // Call logout function
      await logout();

      // Verify fetch was called with correct arguments
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/auth/logout'),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Authorization': 'Bearer test-access-token',
          }),
        })
      );

      // Verify localStorage was cleared
      expect(localStorageMock.getItem('invoix_access_token')).toBeNull();
      expect(localStorageMock.getItem('invoix_refresh_token')).toBeNull();
      expect(localStorageMock.getItem('invoix_user')).toBeNull();
    });
  });

  describe('Token Refresh', () => {
    it('should refresh token successfully', async () => {
      // Setup localStorage with refresh token
      localStorageMock.setItem('invoix_refresh_token', 'test-refresh-token');

      // Mock successful refresh response
      (global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          accessToken: 'new-access-token',
        }),
      });

      // Call refreshAccessToken function
      const result = await refreshAccessToken();

      // Verify fetch was called with correct arguments
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/auth/refresh-token'),
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify({ refreshToken: 'test-refresh-token' }),
        })
      );

      // Verify new access token was stored in localStorage
      expect(localStorageMock.getItem('invoix_access_token')).toBe('new-access-token');

      // Verify return value
      expect(result).toBe('new-access-token');
    });

    it('should handle refresh token failure', async () => {
      // Setup localStorage with refresh token
      localStorageMock.setItem('invoix_refresh_token', 'invalid-refresh-token');

      // Mock failed refresh response
      (global.fetch as any).mockResolvedValueOnce({
        ok: false,
        json: async () => ({
          message: 'Invalid refresh token',
        }),
      });

      // Call refreshAccessToken function and expect it to throw
      await expect(refreshAccessToken()).rejects.toThrow('Failed to refresh token');

      // Verify localStorage tokens were removed
      expect(localStorageMock.getItem('invoix_access_token')).toBeNull();
      expect(localStorageMock.getItem('invoix_refresh_token')).toBeNull();
    });
  });

  describe('Registration', () => {
    it('should register successfully', async () => {
      // Mock successful registration response
      (global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          message: 'User registered successfully',
          user: {
            id: 'user-123',
            name: 'New User',
            email: '<EMAIL>',
            role: 'USER',
            tenant: {
              id: 'tenant-123',
              name: 'Test Tenant',
              businessName: 'Test Business',
            },
          },
          accessToken: 'test-access-token',
          refreshToken: 'test-refresh-token',
        }),
      });

      // Call register function
      const result = await register('New User', '<EMAIL>', 'password123', 'tenant-123');

      // Verify fetch was called with correct arguments
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/auth/register'),
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify({
            name: 'New User',
            email: '<EMAIL>',
            password: 'password123',
            tenantId: 'tenant-123',
          }),
        })
      );

      // Verify tokens were stored in localStorage
      expect(localStorageMock.getItem('invoix_access_token')).toBe('test-access-token');
      expect(localStorageMock.getItem('invoix_refresh_token')).toBe('test-refresh-token');
      expect(localStorageMock.getItem('invoix_user')).toBeTruthy();

      // Verify return value
      expect(result.user.email).toBe('<EMAIL>');
      expect(result.accessToken).toBe('test-access-token');
    });
  });

  describe('Authentication Status', () => {
    it('should return true when valid token exists', () => {
      // Setup localStorage with valid token (not expired)
      const futureTime = Math.floor(Date.now() / 1000) + 3600; // 1 hour in the future
      const validToken = `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiZXhwIjoke futureTime}}`;
      localStorageMock.setItem('invoix_access_token', validToken);

      // Mock jwt decode to return future expiration
      vi.mock('jwt-decode', () => ({
        jwtDecode: () => ({ exp: futureTime }),
      }));

      // Check authentication status
      const result = isAuthenticated();
      expect(result).toBe(true);
    });

    it('should return false when no token exists', () => {
      // Ensure localStorage has no token
      localStorageMock.removeItem('invoix_access_token');

      // Check authentication status
      const result = isAuthenticated();
      expect(result).toBe(false);
    });
  });
});
