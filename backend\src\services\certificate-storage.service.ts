import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import { promisify } from 'util';
import { prisma } from '../index';

// Promisify fs functions
const mkdir = promisify(fs.mkdir);
const writeFile = promisify(fs.writeFile);
const readFile = promisify(fs.readFile);
const unlink = promisify(fs.unlink);
const access = promisify(fs.access);

// Environment variables
const CERT_STORAGE_PATH = process.env.CERT_STORAGE_PATH || path.join(process.cwd(), 'certificates');
const ENCRYPTION_KEY = process.env.CERT_ENCRYPTION_KEY || 'default-encryption-key-change-in-production';
const ENCRYPTION_IV_LENGTH = 16;

/**
 * Ensure the certificate storage directory exists
 */
async function ensureStorageDirectory(): Promise<void> {
  try {
    await access(CERT_STORAGE_PATH);
  } catch (error) {
    // Directory doesn't exist, create it
    await mkdir(CERT_STORAGE_PATH, { recursive: true });
  }
}

/**
 * Encrypt data using AES-256-CBC
 * @param data Data to encrypt
 * @returns Encrypted data as a hex string with IV prepended
 */
function encryptData(data: Buffer): string {
  // Generate a random initialization vector
  const iv = crypto.randomBytes(ENCRYPTION_IV_LENGTH);
  
  // Create cipher
  const cipher = crypto.createCipheriv('aes-256-cbc', Buffer.from(ENCRYPTION_KEY), iv);
  
  // Encrypt the data
  const encrypted = Buffer.concat([cipher.update(data), cipher.final()]);
  
  // Return IV + encrypted data as hex string
  return iv.toString('hex') + ':' + encrypted.toString('hex');
}

/**
 * Decrypt data using AES-256-CBC
 * @param encryptedData Encrypted data as a hex string with IV prepended
 * @returns Decrypted data
 */
function decryptData(encryptedData: string): Buffer {
  // Split IV and encrypted data
  const [ivHex, encryptedHex] = encryptedData.split(':');
  
  // Convert hex strings to buffers
  const iv = Buffer.from(ivHex, 'hex');
  const encrypted = Buffer.from(encryptedHex, 'hex');
  
  // Create decipher
  const decipher = crypto.createDecipheriv('aes-256-cbc', Buffer.from(ENCRYPTION_KEY), iv);
  
  // Decrypt the data
  return Buffer.concat([decipher.update(encrypted), decipher.final()]);
}

/**
 * Store a certificate securely
 * @param name Certificate name
 * @param certificateData Certificate data as Buffer
 * @param password Certificate password
 * @returns Certificate ID
 */
export async function storeCertificate(
  name: string,
  certificateData: Buffer,
  password: string
): Promise<string> {
  try {
    // Ensure storage directory exists
    await ensureStorageDirectory();
    
    // Generate a unique filename
    const certificateId = crypto.randomUUID();
    const filename = `${certificateId}.p12.enc`;
    const filepath = path.join(CERT_STORAGE_PATH, filename);
    
    // Encrypt the certificate data
    const encryptedData = encryptData(certificateData);
    
    // Encrypt the password
    const encryptedPassword = encryptData(Buffer.from(password));
    
    // Write encrypted certificate to disk
    await writeFile(filepath, encryptedData);
    
    // Store certificate metadata in database
    const certificate = await prisma.certificate.create({
      data: {
        id: certificateId,
        name,
        filename,
        password: encryptedPassword,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });
    
    return certificate.id;
  } catch (error) {
    console.error('Error storing certificate:', error);
    throw new Error('Failed to store certificate securely');
  }
}

/**
 * Retrieve a certificate
 * @param certificateId Certificate ID
 * @returns Certificate data and password
 */
export async function retrieveCertificate(
  certificateId: string
): Promise<{ data: Buffer; password: string }> {
  try {
    // Get certificate metadata from database
    const certificate = await prisma.certificate.findUnique({
      where: { id: certificateId },
    });
    
    if (!certificate) {
      throw new Error('Certificate not found');
    }
    
    // Read encrypted certificate from disk
    const filepath = path.join(CERT_STORAGE_PATH, certificate.filename);
    const encryptedData = await readFile(filepath, 'utf8');
    
    // Decrypt the certificate data
    const decryptedData = decryptData(encryptedData);
    
    // Decrypt the password
    const decryptedPassword = decryptData(certificate.password).toString();
    
    return {
      data: decryptedData,
      password: decryptedPassword,
    };
  } catch (error) {
    console.error('Error retrieving certificate:', error);
    throw new Error('Failed to retrieve certificate');
  }
}

/**
 * Delete a certificate
 * @param certificateId Certificate ID
 */
export async function deleteCertificate(certificateId: string): Promise<void> {
  try {
    // Get certificate metadata from database
    const certificate = await prisma.certificate.findUnique({
      where: { id: certificateId },
    });
    
    if (!certificate) {
      throw new Error('Certificate not found');
    }
    
    // Delete certificate file
    const filepath = path.join(CERT_STORAGE_PATH, certificate.filename);
    await unlink(filepath);
    
    // Delete certificate metadata from database
    await prisma.certificate.delete({
      where: { id: certificateId },
    });
  } catch (error) {
    console.error('Error deleting certificate:', error);
    throw new Error('Failed to delete certificate');
  }
}

/**
 * Get active certificate
 * @returns Active certificate ID
 */
export async function getActiveCertificate(): Promise<string | null> {
  try {
    // Get active certificate from database
    const certificate = await prisma.certificate.findFirst({
      where: { isActive: true },
      orderBy: { createdAt: 'desc' },
    });
    
    return certificate?.id || null;
  } catch (error) {
    console.error('Error getting active certificate:', error);
    throw new Error('Failed to get active certificate');
  }
}

/**
 * Set a certificate as active
 * @param certificateId Certificate ID
 */
export async function setActiveCertificate(certificateId: string): Promise<void> {
  try {
    // Verify certificate exists
    const certificate = await prisma.certificate.findUnique({
      where: { id: certificateId },
    });
    
    if (!certificate) {
      throw new Error('Certificate not found');
    }
    
    // Set all certificates as inactive
    await prisma.certificate.updateMany({
      data: { isActive: false },
    });
    
    // Set the specified certificate as active
    await prisma.certificate.update({
      where: { id: certificateId },
      data: { isActive: true },
    });
  } catch (error) {
    console.error('Error setting active certificate:', error);
    throw new Error('Failed to set active certificate');
  }
}
