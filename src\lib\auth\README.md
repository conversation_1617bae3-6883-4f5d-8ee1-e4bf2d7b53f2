# Authentication System

This document describes the authentication system used in the Invoix ERP platform frontend.

## Overview

The authentication system uses a JWT-based approach with both access tokens and refresh tokens:

- **Access Tokens**: Short-lived tokens (1 hour) used for API authentication
- **Refresh Tokens**: Long-lived tokens (7 days) used to obtain new access tokens

## Components

### Authentication Service (`src/lib/auth.ts`)

The core authentication service that provides:

- Login and registration functions
- Token management (storage, retrieval, refresh)
- User information management
- Authentication status checking

### API Client (`src/lib/api/client.ts`)

An HTTP client that:

- Automatically adds authentication headers to requests
- Handles token expiration by refreshing tokens
- Provides a clean interface for API calls

### Authentication Context (`src/contexts/AuthContext.tsx`)

A React context that:

- Provides authentication state to the entire application
- Manages user information
- Provides login and logout functions
- Includes a `ProtectedRoute` component for route protection

## Usage

### Protected Routes

Wrap any route that requires authentication with the `ProtectedRoute` component:

```tsx
import { ProtectedRoute } from '@/contexts/AuthContext';

export default function DashboardLayout({ children }) {
  return (
    <ProtectedRoute>
      <DashboardLayout>{children}</DashboardLayout>
    </ProtectedRoute>
  );
}
```

### Accessing User Information

Use the `useAuth` hook to access authentication state and user information:

```tsx
import { useAuth } from '@/contexts/AuthContext';

function ProfileComponent() {
  const { user, isAuthenticated, logout } = useAuth();
  
  return (
    <div>
      <h1>Welcome, {user?.name}</h1>
      <button onClick={logout}>Logout</button>
    </div>
  );
}
```

### Making Authenticated API Calls

Use the API client for all API calls:

```tsx
import { apiClient } from '@/lib/api/client';

// Authenticated request (default)
const userData = await apiClient.get('/users/me');

// Public request (no auth header)
const publicData = await apiClient.get('/public-endpoint', { skipAuth: true });
```

## Token Flow

1. **Login/Registration**: User receives both access token and refresh token
2. **API Requests**: Access token is sent in the Authorization header
3. **Token Expiration**: When access token expires, client uses refresh token to get a new access token
4. **Logout**: Refresh token is invalidated in the database

## Security Considerations

- Access tokens are stored in localStorage for easy access
- Refresh tokens are also stored in localStorage, but in a production environment should be stored in an HTTP-only cookie
- The API client automatically handles token refresh when the access token expires
- If the refresh token is invalid or expired, the user is redirected to the login page

## Future Improvements

- Move refresh token storage to HTTP-only cookies
- Implement token rotation for refresh tokens
- Add CSRF protection
- Implement silent refresh for better UX
