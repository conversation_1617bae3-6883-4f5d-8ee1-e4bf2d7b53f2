'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Spinner } from '@/components/ui/spinner';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface PreferencesSettingsProps {
  userData: {
    emailNotifications: boolean;
    language: string;
    timezone: string;
  };
  onToggleEmailNotifications: (enabled: boolean) => void;
  onSavePreferences: (data: any) => void;
}

export default function PreferencesSettings({ 
  userData, 
  onToggleEmailNotifications,
  onSavePreferences
}: PreferencesSettingsProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [preferences, setPreferences] = useState({
    language: userData.language,
    timezone: userData.timezone,
  });

  const handleToggleEmailNotifications = async () => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);
    
    try {
      // In a real implementation, this would call your API
      // Simulate API call with timeout
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      onToggleEmailNotifications(!userData.emailNotifications);
      
      setSuccess(`Email notifications ${userData.emailNotifications ? 'disabled' : 'enabled'} successfully`);
      
      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err: any) {
      console.error('Error toggling email notifications:', err);
      setError(err.message || 'Failed to toggle email notifications');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSavePreferences = async () => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);
    
    try {
      // In a real implementation, this would call your API
      // Simulate API call with timeout
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      onSavePreferences(preferences);
      
      setSuccess('Preferences saved successfully');
      
      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err: any) {
      console.error('Error saving preferences:', err);
      setError(err.message || 'Failed to save preferences');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      
      {success && (
        <Alert variant="success" className="bg-green-50 text-green-800 border-green-200">
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}
      
      <div>
        <h3 className="text-lg font-medium">Notifications</h3>
        <Separator className="my-4" />
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <p className="text-sm font-medium">Email Notifications</p>
            <p className="text-sm text-gray-500">
              Receive email notifications for important updates
            </p>
          </div>
          <Switch
            checked={userData.emailNotifications}
            onCheckedChange={handleToggleEmailNotifications}
            disabled={isLoading}
          />
        </div>
      </div>
      
      <div>
        <h3 className="text-lg font-medium">Language and Region</h3>
        <Separator className="my-4" />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="language">Language</Label>
            <Select
              value={preferences.language}
              onValueChange={(value) => setPreferences({...preferences, language: value})}
            >
              <SelectTrigger id="language">
                <SelectValue placeholder="Select a language" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="en">English</SelectItem>
                <SelectItem value="ms">Bahasa Malaysia</SelectItem>
                <SelectItem value="zh">Chinese</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="timezone">Timezone</Label>
            <Select
              value={preferences.timezone}
              onValueChange={(value) => setPreferences({...preferences, timezone: value})}
            >
              <SelectTrigger id="timezone">
                <SelectValue placeholder="Select a timezone" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Asia/Kuala_Lumpur">Malaysia (GMT+8)</SelectItem>
                <SelectItem value="Asia/Singapore">Singapore (GMT+8)</SelectItem>
                <SelectItem value="Asia/Bangkok">Thailand (GMT+7)</SelectItem>
                <SelectItem value="Asia/Jakarta">Indonesia (GMT+7)</SelectItem>
                <SelectItem value="Asia/Manila">Philippines (GMT+8)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>
      
      <div className="flex justify-end">
        <Button 
          onClick={handleSavePreferences}
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <Spinner size="sm" className="mr-2" />
              Saving...
            </>
          ) : (
            <>Save Preferences</>
          )}
        </Button>
      </div>
    </div>
  );
}
