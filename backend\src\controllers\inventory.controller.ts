import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { handleError } from '../utils/errorHandler';

const prisma = new PrismaClient();

// Product Controllers
export const createProduct = async (req: Request, res: Response) => {
  try {
    const { sku, name, description, category, unitPrice, costPrice, taxRate } = req.body;
    const tenantId = req.user.tenantId;

    const product = await prisma.product.create({
      data: {
        sku,
        name,
        description,
        category,
        unitPrice: parseFloat(unitPrice),
        costPrice: parseFloat(costPrice),
        taxRate: parseFloat(taxRate || 0),
        tenant: {
          connect: { id: tenantId }
        }
      }
    });

    res.status(201).json(product);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const getProducts = async (req: Request, res: Response) => {
  try {
    const tenantId = req.user.tenantId;
    const products = await prisma.product.findMany({
      where: {
        tenantId
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    res.status(200).json(products);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const getProduct = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenantId;

    const product = await prisma.product.findFirst({
      where: {
        id,
        tenantId
      },
      include: {
        inventoryItems: {
          include: {
            warehouse: true
          }
        }
      }
    });

    if (!product) {
      return res.status(404).json({ message: 'Product not found' });
    }

    res.status(200).json(product);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const updateProduct = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { sku, name, description, category, unitPrice, costPrice, taxRate, isActive } = req.body;
    const tenantId = req.user.tenantId;

    const product = await prisma.product.findFirst({
      where: {
        id,
        tenantId
      }
    });

    if (!product) {
      return res.status(404).json({ message: 'Product not found' });
    }

    const updatedProduct = await prisma.product.update({
      where: { id },
      data: {
        sku,
        name,
        description,
        category,
        unitPrice: parseFloat(unitPrice),
        costPrice: parseFloat(costPrice),
        taxRate: parseFloat(taxRate || 0),
        isActive
      }
    });

    res.status(200).json(updatedProduct);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const deleteProduct = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenantId;

    const product = await prisma.product.findFirst({
      where: {
        id,
        tenantId
      }
    });

    if (!product) {
      return res.status(404).json({ message: 'Product not found' });
    }

    await prisma.product.delete({
      where: { id }
    });

    res.status(200).json({ message: 'Product deleted successfully' });
  } catch (error) {
    handleError(error, req, res);
  }
};

// Warehouse Controllers
export const createWarehouse = async (req: Request, res: Response) => {
  try {
    const { name, location, isDefault } = req.body;
    const tenantId = req.user.tenantId;

    // If this is the default warehouse, update any existing default
    if (isDefault) {
      await prisma.warehouse.updateMany({
        where: { tenantId, isDefault: true },
        data: { isDefault: false }
      });
    }

    const warehouse = await prisma.warehouse.create({
      data: {
        name,
        location,
        isDefault: isDefault || false,
        tenant: {
          connect: { id: tenantId }
        }
      }
    });

    res.status(201).json(warehouse);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const getWarehouses = async (req: Request, res: Response) => {
  try {
    const tenantId = req.user.tenantId;
    const warehouses = await prisma.warehouse.findMany({
      where: {
        tenantId
      },
      orderBy: {
        name: 'asc'
      }
    });

    res.status(200).json(warehouses);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const getWarehouse = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenantId;

    const warehouse = await prisma.warehouse.findFirst({
      where: {
        id,
        tenantId
      },
      include: {
        inventoryItems: {
          include: {
            product: true
          }
        }
      }
    });

    if (!warehouse) {
      return res.status(404).json({ message: 'Warehouse not found' });
    }

    res.status(200).json(warehouse);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const updateWarehouse = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { name, location, isDefault } = req.body;
    const tenantId = req.user.tenantId;

    const warehouse = await prisma.warehouse.findFirst({
      where: {
        id,
        tenantId
      }
    });

    if (!warehouse) {
      return res.status(404).json({ message: 'Warehouse not found' });
    }

    // If this is being set as default, update any existing default
    if (isDefault) {
      await prisma.warehouse.updateMany({
        where: {
          tenantId,
          isDefault: true,
          id: { not: id }
        },
        data: { isDefault: false }
      });
    }

    const updatedWarehouse = await prisma.warehouse.update({
      where: { id },
      data: {
        name,
        location,
        isDefault: isDefault || false
      }
    });

    res.status(200).json(updatedWarehouse);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const deleteWarehouse = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenantId;

    const warehouse = await prisma.warehouse.findFirst({
      where: {
        id,
        tenantId
      },
      include: {
        inventoryItems: true
      }
    });

    if (!warehouse) {
      return res.status(404).json({ message: 'Warehouse not found' });
    }

    // Check if warehouse has inventory items
    if (warehouse.inventoryItems.length > 0) {
      return res.status(400).json({
        message: 'Cannot delete warehouse with inventory items. Please transfer or remove inventory first.'
      });
    }

    // Check if this is the only warehouse
    const warehouseCount = await prisma.warehouse.count({
      where: { tenantId }
    });

    if (warehouseCount <= 1) {
      return res.status(400).json({
        message: 'Cannot delete the only warehouse. Please create another warehouse first.'
      });
    }

    // Check if this is the default warehouse
    if (warehouse.isDefault) {
      return res.status(400).json({
        message: 'Cannot delete the default warehouse. Please set another warehouse as default first.'
      });
    }

    await prisma.warehouse.delete({
      where: { id }
    });

    res.status(200).json({ message: 'Warehouse deleted successfully' });
  } catch (error) {
    handleError(error, req, res);
  }
};

// Inventory Item Controllers
export const updateInventoryItem = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { quantity, reorderLevel } = req.body;
    const tenantId = req.user.tenantId;

    const inventoryItem = await prisma.inventoryItem.findFirst({
      where: {
        id,
        tenantId
      }
    });

    if (!inventoryItem) {
      return res.status(404).json({ message: 'Inventory item not found' });
    }

    const updatedInventoryItem = await prisma.inventoryItem.update({
      where: { id },
      data: {
        quantity: parseInt(quantity),
        reorderLevel: parseInt(reorderLevel || 10)
      }
    });

    res.status(200).json(updatedInventoryItem);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const getInventoryItems = async (req: Request, res: Response) => {
  try {
    const tenantId = req.user.tenantId;
    const inventoryItems = await prisma.inventoryItem.findMany({
      where: {
        tenantId
      },
      include: {
        product: true,
        warehouse: true
      },
      orderBy: {
        product: {
          name: 'asc'
        }
      }
    });

    res.status(200).json(inventoryItems);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const getInventoryItem = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenantId;

    const inventoryItem = await prisma.inventoryItem.findFirst({
      where: {
        id,
        tenantId
      },
      include: {
        product: true,
        warehouse: true,
        transactions: {
          orderBy: {
            createdAt: 'desc'
          },
          take: 10
        }
      }
    });

    if (!inventoryItem) {
      return res.status(404).json({ message: 'Inventory item not found' });
    }

    res.status(200).json(inventoryItem);
  } catch (error) {
    handleError(error, req, res);
  }
};

// Inventory Transaction Controllers
export const createInventoryTransaction = async (req: Request, res: Response) => {
  try {
    const { inventoryItemId, type, quantity, notes, referenceId, referenceType } = req.body;
    const tenantId = req.user.tenantId;
    const userId = req.user.id;

    // Validate inventory item exists and belongs to tenant
    const inventoryItem = await prisma.inventoryItem.findFirst({
      where: {
        id: inventoryItemId,
        tenantId
      }
    });

    if (!inventoryItem) {
      return res.status(404).json({ message: 'Inventory item not found' });
    }

    // Calculate new quantity based on transaction type
    let newQuantity = inventoryItem.quantity;

    switch (type) {
      case 'PURCHASE':
      case 'ADJUSTMENT':
      case 'TRANSFER':
        newQuantity += parseInt(quantity);
        break;
      case 'SALE':
      case 'RETURN':
        // Ensure we don't go below zero
        if (inventoryItem.quantity < parseInt(quantity)) {
          return res.status(400).json({ message: 'Insufficient inventory quantity' });
        }
        newQuantity -= parseInt(quantity);
        break;
      case 'STOCK_TAKE':
        newQuantity = parseInt(quantity);
        break;
      default:
        return res.status(400).json({ message: 'Invalid transaction type' });
    }

    // Create transaction and update inventory in a transaction
    const result = await prisma.$transaction([
      // Create the transaction record
      prisma.inventoryTransaction.create({
        data: {
          type,
          quantity: parseInt(quantity),
          notes,
          referenceId,
          referenceType,
          createdBy: userId,
          inventoryItem: {
            connect: { id: inventoryItemId }
          },
          tenant: {
            connect: { id: tenantId }
          }
        }
      }),
      // Update the inventory quantity
      prisma.inventoryItem.update({
        where: { id: inventoryItemId },
        data: {
          quantity: newQuantity,
          lastStockTake: type === 'STOCK_TAKE' ? new Date() : undefined
        }
      })
    ]);

    res.status(201).json(result[0]);
  } catch (error) {
    handleError(error, req, res);
  }
};

export const getInventoryTransactions = async (req: Request, res: Response) => {
  try {
    const tenantId = req.user.tenantId;
    const { inventoryItemId } = req.query;

    const whereClause: any = { tenantId };

    if (inventoryItemId) {
      whereClause.inventoryItemId = inventoryItemId as string;
    }

    const transactions = await prisma.inventoryTransaction.findMany({
      where: whereClause,
      include: {
        inventoryItem: {
          include: {
            product: true,
            warehouse: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    res.status(200).json(transactions);
  } catch (error) {
    handleError(error, req, res);
  }
};
