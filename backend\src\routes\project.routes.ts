import express from 'express';
import { authenticateJWT } from '../middleware/auth';
import {
  createProject,
  getProjects,
  getProject,
  updateProject,
  deleteProject,
  createProjectTask,
  getProjectTasks,
  getProjectTask,
  updateProjectTask,
  deleteProjectTask,
  createTimeEntry,
  getTimeEntries,
  updateTimeEntry,
  deleteTimeEntry,
  createProjectExpense,
  getProjectExpenses,
  updateProjectExpense,
  deleteProjectExpense
} from '../controllers/project.controller';

const router = express.Router();

// Apply authentication middleware to all project routes
router.use(authenticateJWT);

// Project routes
router.post('/projects', createProject);
router.get('/projects', getProjects);
router.get('/projects/:id', getProject);
router.put('/projects/:id', updateProject);
router.delete('/projects/:id', deleteProject);

// Project Task routes
router.post('/tasks', createProjectTask);
router.get('/tasks', getProjectTasks);
router.get('/tasks/:id', getProjectTask);
router.put('/tasks/:id', updateProjectTask);
router.delete('/tasks/:id', deleteProjectTask);

// Time Entry routes
router.post('/time-entries', createTimeEntry);
router.get('/time-entries', getTimeEntries);
router.put('/time-entries/:id', updateTimeEntry);
router.delete('/time-entries/:id', deleteTimeEntry);

// Project Expense routes
router.post('/expenses', createProjectExpense);
router.get('/expenses', getProjectExpenses);
router.put('/expenses/:id', updateProjectExpense);
router.delete('/expenses/:id', deleteProjectExpense);

export default router;
