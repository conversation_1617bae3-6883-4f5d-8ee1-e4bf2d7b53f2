import { apiClient } from './client';

class AIService {
  /**
   * Process document using OCR and extract data
   */
  async processDocument(formData: FormData): Promise<any> {
    try {
      const response = await apiClient.post('/ai/process-document', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error processing document:', error);

      // Fallback to mock data if API call fails
      return {
        invoiceNumber: 'INV-2023-0042',
        date: '2023-05-15',
        vendor: 'Tech Solutions Sdn Bhd',
        totalAmount: 'RM 2,450.00',
        lineItems: [
          {
            description: 'Web Development Services',
            quantity: 1,
            unitPrice: 'RM 2,000.00',
            amount: 'RM 2,000.00',
          },
          {
            description: 'Domain Registration',
            quantity: 2,
            unitPrice: 'RM 75.00',
            amount: 'RM 150.00',
          },
          {
            description: 'SSL Certificate',
            quantity: 1,
            unitPrice: 'RM 300.00',
            amount: 'RM 300.00',
          },
        ],
      };
    }
  }

  /**
   * Get AI insights for dashboard
   */
  async getInsights(): Promise<any> {
    try {
      const response = await apiClient.get('/ai/insights');
      return response.data;
    } catch (error) {
      console.error('Error getting AI insights:', error);

      // Fallback to mock data if API call fails
      return {
        businessHealth: {
          score: 78,
          trend: '+3%',
          status: 'healthy',
          recommendations: 2
        },
        financialMetrics: {
          cashFlow: {
            current: 25000,
            previous: 22000,
            trend: '+13.6%'
          },
          accountsReceivable: {
            current: 45000,
            previous: 48000,
            trend: '-6.3%'
          },
          accountsPayable: {
            current: 32000,
            previous: 30000,
            trend: '+6.7%'
          },
          profitMargin: {
            current: 22,
            previous: 20,
            trend: '+10%'
          }
        },
        inventoryInsights: {
          lowStock: 5,
          overstock: 3,
          optimalStock: 42,
          totalItems: 50
        },
        customerMetrics: {
          totalCustomers: 120,
          activeCustomers: 85,
          atRiskCustomers: 12,
          newCustomers: 8
        },
        fraudAlerts: {
          highRisk: 2,
          mediumRisk: 5,
          lowRisk: 8,
          total: 15
        }
      };
    }
  }

  /**
   * Get financial insights and predictions
   */
  async getFinancialInsights(params?: {
    startDate?: string;
    endDate?: string;
    predictionPeriod?: string;
  }): Promise<any> {
    try {
      const response = await apiClient.get('/ai/insights', { params });
      return response.data;
    } catch (error) {
      console.error('Error getting financial insights:', error);

      // Fallback to mock data if API call fails
      return {
        cashFlowHistory: [
          { month: 'Jan', income: 45000, expenses: 38000, balance: 7000 },
          { month: 'Feb', income: 48000, expenses: 42000, balance: 6000 },
          { month: 'Mar', income: 52000, expenses: 45000, balance: 7000 },
          { month: 'Apr', income: 49000, expenses: 44000, balance: 5000 },
          { month: 'May', income: 53000, expenses: 47000, balance: 6000 },
          { month: 'Jun', income: 56000, expenses: 48000, balance: 8000 },
        ],
        cashFlowPrediction: [
          { month: 'Jul', income: 58000, expenses: 49000, balance: 9000 },
          { month: 'Aug', income: 60000, expenses: 51000, balance: 9000 },
          { month: 'Sep', income: 62000, expenses: 52000, balance: 10000 },
        ],
        topExpenseCategories: [
          { name: 'Salaries', value: 25000 },
          { name: 'Rent', value: 8000 },
          { name: 'Marketing', value: 6000 },
          { name: 'Utilities', value: 4000 },
          { name: 'Software', value: 3500 },
        ],
        anomalies: [
          {
            date: '2023-05-15',
            category: 'Marketing',
            amount: 3500,
            expected: 2000,
            description: 'Unusually high marketing expense'
          },
          {
            date: '2023-06-02',
            category: 'Utilities',
            amount: 1800,
            expected: 1200,
            description: 'Utility bill significantly higher than average'
          },
        ],
        insights: [
          'Cash flow is projected to improve by 25% in the next quarter',
          'Marketing expenses are showing higher ROI than previous quarter',
          'Consider reducing software expenses which have increased 15% with no corresponding revenue increase',
          'Accounts receivable aging is improving, with 85% of invoices paid within terms',
        ]
      };
    }
  }

  /**
   * Get customer insights and predictions
   */
  async getCustomerInsights(params?: {
    startDate?: string;
    endDate?: string;
    customerId?: string;
  }): Promise<any> {
    try {
      const response = await apiClient.get('/ai/customer-insights', { params });
      return response.data;
    } catch (error) {
      console.error('Error getting customer insights:', error);

      // Fallback to mock data if API call fails
      return {
        customerSegments: [
          { name: 'High Value', count: 25, percentage: 20, growth: '+5%' },
          { name: 'Regular', count: 45, percentage: 37.5, growth: '+2%' },
          { name: 'Occasional', count: 30, percentage: 25, growth: '-3%' },
          { name: 'At Risk', count: 12, percentage: 10, growth: '+1%' },
          { name: 'Inactive', count: 8, percentage: 7.5, growth: '-1%' },
        ],
        customerLifecycle: {
          acquisition: 8,
          engagement: 85,
          retention: 15,
          churn: 12
        },
        topCustomers: [
          { id: 'C001', name: 'Acme Corporation', revenue: 15000, growth: '+12%', risk: 'low' },
          { id: 'C002', name: 'TechStart Sdn Bhd', revenue: 12500, growth: '+8%', risk: 'low' },
          { id: 'C003', name: 'Global Traders', revenue: 10800, growth: '-3%', risk: 'medium' },
          { id: 'C004', name: 'Innovative Solutions', revenue: 9500, growth: '+5%', risk: 'low' },
          { id: 'C005', name: 'Prime Retail', revenue: 8200, growth: '-7%', risk: 'high' },
        ],
        churnPredictions: [
          { id: 'C005', name: 'Prime Retail', probability: 75, reason: 'Declining order volume, payment delays' },
          { id: 'C012', name: 'Sunrise Hotels', probability: 65, reason: 'Reduced engagement, competitor activity' },
          { id: 'C018', name: 'Green Energy Co', probability: 55, reason: 'Recent service complaints, price sensitivity' },
        ],
        insights: [
          'High-value customer segment has grown 5% in the last quarter',
          'Customer acquisition cost has decreased by 12%',
          'Customers in the technology sector show 15% higher retention rates',
          'Implementing a loyalty program could reduce churn by an estimated 20%',
        ]
      };
    }
  }

  /**
   * Validate invoice for LHDN compliance
   */
  async validateInvoice(invoiceId: string): Promise<any> {
    try {
      const response = await apiClient.get(`/ai/validate-invoice/${invoiceId}`);
      return response.data;
    } catch (error) {
      console.error('Error validating invoice:', error);

      // Fallback to mock data if API call fails
      const issues = [];
      const suggestions = [];

      issues.push({
        id: 'missing-tin',
        message: 'Customer Tax ID (TIN) is missing. This is required for LHDN compliance.',
        severity: 'error',
      });

      suggestions.push({
        id: 'add-tin',
        message: 'Add the customer Tax ID to the invoice.',
        action: 'Update customer profile with Tax ID information.',
      });

      return {
        isValid: issues.length === 0,
        issues,
        suggestions,
        complianceScore: issues.length === 0 ? 100 : Math.max(0, 100 - (issues.length * 15)),
      };
    }
  }

  /**
   * Get AI-powered recommendations
   */
  async getRecommendations(type: 'business' | 'compliance' | 'customer'): Promise<any> {
    try {
      const response = await apiClient.get(`/ai/recommendations/${type}`);
      return response.data;
    } catch (error) {
      console.error('Error getting recommendations:', error);

      // Fallback to mock data if API call fails
      const recommendations = {
        business: [
          { id: 'B001', title: 'Optimize Cash Flow', description: 'Implement early payment discounts to improve cash flow by an estimated 15%', impact: 'high', effort: 'medium' },
          { id: 'B002', title: 'Reduce Operating Costs', description: 'Switch to cloud-based software to reduce IT costs by approximately 25%', impact: 'medium', effort: 'medium' },
          { id: 'B003', title: 'Expand Product Line', description: 'Data suggests demand for complementary products in your current market', impact: 'high', effort: 'high' },
        ],
        compliance: [
          { id: 'C001', title: 'Update Invoice Templates', description: 'Add required LHDN fields to all invoice templates to ensure 100% compliance', impact: 'high', effort: 'low' },
          { id: 'C002', title: 'Implement Automated Checks', description: 'Set up pre-submission validation to catch common compliance issues', impact: 'high', effort: 'medium' },
          { id: 'C003', title: 'Schedule Regular Audits', description: 'Monthly internal audits can reduce compliance issues by up to 90%', impact: 'medium', effort: 'medium' },
        ],
        customer: [
          { id: 'CU001', title: 'Implement Loyalty Program', description: 'A tiered loyalty program could increase retention by an estimated 20%', impact: 'high', effort: 'medium' },
          { id: 'CU002', title: 'Personalized Outreach', description: 'Targeted communication to at-risk customers can reduce churn by 35%', impact: 'high', effort: 'medium' },
          { id: 'CU003', title: 'Customer Feedback System', description: 'Implementing a structured feedback system can improve satisfaction scores by 25%', impact: 'medium', effort: 'low' },
        ],
      };

      return recommendations[type];
    }
  }
}

export const aiService = new AIService();
export default aiService;
