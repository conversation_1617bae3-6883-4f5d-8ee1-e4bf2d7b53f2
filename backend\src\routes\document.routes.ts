import { Router } from 'express';
import { 
  cancelDocument<PERSON><PERSON>roller, 
  rejectDocumentController, 
  searchDocumentsController 
} from '../controllers/document.controller';
import { authenticate } from '../middleware/auth.middleware';
import { ExpressHandler } from '../types/express';

const router = Router();

// All routes are protected
router.post('/:id/cancel', authenticate as ExpressHandler, cancelDocumentController as ExpressHandler);
router.post('/:id/reject', authenticate as ExpressHandler, rejectDocumentController as ExpressHandler);
router.get('/search', authenticate as ExpressHandler, searchDocumentsController as ExpressHandler);

export default router;
