# Invoix ERP Modules

This document provides an overview of the ERP modules implemented in the Invoix system.

## Overview

Invoix has been extended from a specialized invoicing system with Malaysian tax compliance into a comprehensive ERP solution. The following modules have been added:

1. Inventory Management
2. Human Resources
3. Procurement
4. Fixed Asset Management
5. Project Management

## Module Details

### 1. Inventory Management

**Features:**
- Product catalog management
- Warehouse management
- Stock level tracking
- Inventory transactions (purchases, sales, adjustments)
- Low stock alerts
- Inventory reporting

**Key Entities:**
- Products
- Warehouses
- Inventory Items
- Inventory Transactions

**API Endpoints:**
- `/inventory/products` - Manage products
- `/inventory/warehouses` - Manage warehouses
- `/inventory/items` - Manage inventory items
- `/inventory/transactions` - Track inventory movements

### 2. Human Resources

**Features:**
- Employee management
- Attendance tracking
- Leave management
- Payroll processing with Malaysian EPF and SOCSO calculations
- Employee performance tracking

**Key Entities:**
- Employees
- Salary Records
- Attendance Records
- Leave Requests

**API Endpoints:**
- `/hr/employees` - Manage employees
- `/hr/salaries` - Process payroll
- `/hr/attendance` - Track attendance
- `/hr/leave` - Manage leave requests

### 3. Procurement

**Features:**
- Supplier management
- Purchase order processing
- Goods receipt handling
- Integration with inventory
- Supplier performance tracking

**Key Entities:**
- Suppliers
- Purchase Orders
- Purchase Order Items
- Goods Receipts

**API Endpoints:**
- `/procurement/suppliers` - Manage suppliers
- `/procurement/purchase-orders` - Create and manage purchase orders
- `/procurement/goods-receipts` - Record received goods

### 4. Fixed Asset Management

**Features:**
- Asset tracking
- Depreciation calculation
- Maintenance scheduling
- Asset disposal
- Asset reporting

**Key Entities:**
- Fixed Assets
- Asset Depreciations
- Asset Maintenance Records

**API Endpoints:**
- `/assets/fixed-assets` - Manage fixed assets
- `/assets/depreciations` - Record asset depreciation
- `/assets/maintenance` - Track asset maintenance

### 5. Project Management

**Features:**
- Project tracking
- Task management
- Time tracking
- Project expense management
- Project reporting

**Key Entities:**
- Projects
- Project Tasks
- Time Entries
- Project Expenses

**API Endpoints:**
- `/projects/projects` - Manage projects
- `/projects/tasks` - Manage project tasks
- `/projects/time-entries` - Track time spent on projects
- `/projects/expenses` - Track project expenses

## Integration with Existing Features

These ERP modules are fully integrated with the existing Invoix features:

1. **Multi-tenant Architecture**: All ERP modules support the multi-tenant architecture, ensuring data isolation between different tenants.

2. **Malaysian Tax Compliance**: The ERP modules are designed to work with the Malaysian tax compliance features, including LHDN MyInvois integration.

3. **User Authentication**: All ERP module endpoints are secured using the existing authentication system.

4. **Invoicing System**: The inventory and procurement modules are integrated with the invoicing system, allowing for seamless creation of invoices from sales and purchases.

5. **Consistent UI/UX**: All modules follow a consistent design language, with uniform table designs, title colors, and spacing across all dashboard tabs.

## Technical Implementation

The ERP modules are implemented using:

- **Backend**: Node.js/Express with TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Frontend**: Next.js with React and Tailwind CSS

Each module follows a consistent architecture:

1. **Models**: Defined in the Prisma schema
2. **Controllers**: Handle business logic and API requests
3. **Routes**: Define API endpoints
4. **Frontend Components**: Provide user interface for interacting with the modules

## Future Enhancements

Planned enhancements for the ERP modules include:

1. **Advanced Reporting**: More comprehensive reporting capabilities for each module
2. **Mobile App Integration**: Extend the ERP functionality to mobile devices
3. **AI-Powered Insights**: Implement AI features for business intelligence
4. **Integration with External Systems**: Connect with other business systems like e-commerce platforms
5. **Workflow Automation**: Add workflow capabilities for approval processes
