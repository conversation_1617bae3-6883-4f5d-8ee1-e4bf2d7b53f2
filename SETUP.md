# Invoix Setup Guide

This guide provides instructions for setting up the Invoix application for development and production environments.

## Prerequisites

- Node.js (v18 or higher)
- PostgreSQL (v14 or higher)
- Docker and Docker Compose (optional, for containerized setup)

## Development Setup

### Option 1: Local Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/invoix.git
   cd invoix
   ```

2. **Install frontend dependencies**
   ```bash
   npm install
   ```

3. **Install backend dependencies**
   ```bash
   cd backend
   npm install
   ```

4. **Set up environment variables**
   ```bash
   # In backend directory
   cp .env.example .env
   # Edit .env file with your database credentials and other settings
   ```

5. **Set up the database**
   ```bash
   # In backend directory
   npm run prisma:migrate
   npm run prisma:generate
   ```

6. **Start the backend server**
   ```bash
   # In backend directory
   npm run dev
   ```

7. **Start the frontend server**
   ```bash
   # In root directory
   npm run dev
   ```

8. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:5000/api

### Option 2: Docker Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/invoix.git
   cd invoix
   ```

2. **Start the containers**
   ```bash
   docker-compose up -d
   ```

3. **Run database migrations**
   ```bash
   docker-compose exec backend npm run prisma:migrate
   ```

4. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:5000/api

## Production Setup

### Prerequisites

- Node.js (v18 or higher)
- PostgreSQL (v14 or higher)
- Nginx or similar web server for reverse proxy
- SSL certificate for HTTPS

### Steps

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/invoix.git
   cd invoix
   ```

2. **Install dependencies and build the application**
   ```bash
   # Frontend
   npm install
   npm run build
   
   # Backend
   cd backend
   npm install
   npm run build
   ```

3. **Set up environment variables**
   ```bash
   # In backend directory
   cp .env.example .env
   # Edit .env file with production settings
   # Make sure to set NODE_ENV=production
   ```

4. **Set up the database**
   ```bash
   # In backend directory
   npm run prisma:deploy
   ```

5. **Set up a process manager (PM2)**
   ```bash
   npm install -g pm2
   
   # Start backend
   cd backend
   pm2 start dist/index.js --name invoix-backend
   
   # Start frontend (if not using a static file server)
   cd ..
   pm2 start npm --name invoix-frontend -- start
   ```

6. **Configure Nginx as a reverse proxy**
   ```nginx
   server {
       listen 80;
       server_name yourdomain.com;
       
       # Redirect to HTTPS
       return 301 https://$host$request_uri;
   }
   
   server {
       listen 443 ssl;
       server_name yourdomain.com;
       
       ssl_certificate /path/to/certificate.crt;
       ssl_certificate_key /path/to/private.key;
       
       # Frontend
       location / {
           proxy_pass http://localhost:3000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_cache_bypass $http_upgrade;
       }
       
       # Backend API
       location /api {
           proxy_pass http://localhost:5000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_cache_bypass $http_upgrade;
       }
   }
   ```

7. **Set up SSL with Let's Encrypt**
   ```bash
   sudo apt install certbot python3-certbot-nginx
   sudo certbot --nginx -d yourdomain.com
   ```

8. **Set up automatic database backups**
   ```bash
   # Create a backup script
   mkdir -p /opt/invoix/backups
   
   # Add to crontab
   crontab -e
   # Add the following line for daily backups at 2 AM
   0 2 * * * pg_dump -U postgres invoix > /opt/invoix/backups/invoix_$(date +\%Y\%m\%d).sql
   ```

## Testing

### Running Tests

```bash
# In backend directory
npm test

# Run with coverage
npm run test:coverage
```

## Troubleshooting

### Common Issues

1. **Database Connection Issues**
   - Check that PostgreSQL is running
   - Verify database credentials in .env file
   - Ensure the database exists

2. **Prisma Migration Issues**
   - Run `npx prisma migrate reset` to reset the database
   - Check for syntax errors in schema.prisma

3. **API Connection Issues**
   - Verify that NEXT_PUBLIC_API_URL is set correctly
   - Check that the backend server is running

## Maintenance

### Database Maintenance

```bash
# Check database size
psql -U postgres -c "SELECT pg_size_pretty(pg_database_size('invoix'));"

# Vacuum the database
psql -U postgres -c "VACUUM FULL ANALYZE invoix;"
```

### Log Rotation

```bash
# Install logrotate if not already installed
sudo apt install logrotate

# Create a logrotate configuration
sudo nano /etc/logrotate.d/invoix

# Add the following configuration
/opt/invoix/logs/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 0640 www-data www-data
}
```
