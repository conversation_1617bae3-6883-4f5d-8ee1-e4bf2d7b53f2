'use client';

import { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Spinner } from '@/components/ui/spinner';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';

export default function LHDNSettingsPage() {
  const [settings, setSettings] = useState({
    apiBaseUrl: 'https://sandbox.myinvois.hasil.gov.my/einvoicing',
    businessRegNo: '',
    businessName: '',
    certificatePath: '/path/to/certificate.p12',
    certificatePassword: '',
    isActive: false,
    environment: 'sandbox',
  });

  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [testResult, setTestResult] = useState<{
    success: boolean;
    message: string;
  } | null>(null);
  const [certificateStatus, setCertificateStatus] = useState<'valid' | 'expired' | 'missing'>('missing');

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        // In a real implementation, this would fetch settings from your API
        // const response = await fetch('/api/lhdn-config');
        // const data = await response.json();
        // setSettings(data);

        // For development, use mock data
        await new Promise(resolve => setTimeout(resolve, 1000));

        setSettings({
          apiBaseUrl: 'https://sandbox.myinvois.hasil.gov.my/einvoicing',
          businessRegNo: '123456789',
          businessName: 'Invoix Sdn Bhd',
          certificatePath: '/path/to/certificate.p12',
          certificatePassword: '********',
          isActive: true,
          environment: 'sandbox',
        });

        setCertificateStatus('valid');
      } catch (err: any) {
        console.error('Error fetching LHDN settings:', err);
        setError(err.message || 'Failed to load LHDN settings');
      } finally {
        setIsLoading(false);
      }
    };

    fetchSettings();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    setSettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);
    setError(null);
    setSuccess(null);

    try {
      // In a real implementation, this would save settings to your API
      // const response = await fetch('/api/lhdn-config', {
      //   method: 'PUT',
      //   headers: {
      //     'Content-Type': 'application/json',
      //   },
      //   body: JSON.stringify(settings),
      // });
      //
      // if (!response.ok) {
      //   const errorData = await response.json();
      //   throw new Error(errorData.message || 'Failed to save settings');
      // }

      // For development, simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      setSuccess('LHDN settings saved successfully');
      setTestResult({
        success: true,
        message: 'Settings saved successfully',
      });
    } catch (err: any) {
      console.error('Error saving LHDN settings:', err);
      setError(err.message || 'Failed to save settings');
      setTestResult({
        success: false,
        message: 'Failed to save settings',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const testConnection = async () => {
    setIsTesting(true);
    setTestResult(null);
    setError(null);

    try {
      // In a real implementation, this would test the connection to LHDN API
      // const response = await fetch('/api/lhdn-config/test', {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/json',
      //   },
      //   body: JSON.stringify({
      //     apiBaseUrl: settings.apiBaseUrl,
      //     businessRegNo: settings.businessRegNo,
      //     certificatePath: settings.certificatePath,
      //     certificatePassword: settings.certificatePassword,
      //   }),
      // });
      //
      // const data = await response.json();
      // setTestResult(data);

      // For development, simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Randomly succeed or fail for demonstration purposes
      const success = Math.random() > 0.3;

      setTestResult({
        success,
        message: success
          ? 'Successfully connected to LHDN MyInvois API'
          : 'Failed to connect to LHDN MyInvois API. Please check your credentials.',
      });
    } catch (err: any) {
      console.error('Error testing LHDN connection:', err);
      setError(err.message || 'Failed to test connection');
      setTestResult({
        success: false,
        message: err.message || 'An error occurred while testing the connection',
      });
    } finally {
      setIsTesting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spinner size="lg" />
        <span className="ml-2 text-text-secondary">Loading...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow-sm mb-6">
        <h1 className="text-2xl font-bold text-text-primary flex items-center">
          <svg className="w-6 h-6 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          LHDN MyInvois Settings
        </h1>
        <p className="mt-1 text-sm text-text-secondary">
          Configure your LHDN MyInvois API integration for Malaysian tax compliance.
        </p>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert variant="success">
          <AlertTitle>Success</AlertTitle>
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      <Card className="border-none shadow-md">
        <CardHeader className="pb-2 border-b">
          <CardTitle className="text-lg font-bold text-text-primary">API Configuration</CardTitle>
          <CardDescription className="text-text-secondary">Configure your connection to LHDN MyInvois API</CardDescription>
        </CardHeader>
        <CardContent>
          <form id="lhdn-settings-form" onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="apiBaseUrl" className="block text-sm font-medium text-text-primary">
                API Base URL
              </label>
              <input
                type="text"
                name="apiBaseUrl"
                id="apiBaseUrl"
                value={settings.apiBaseUrl}
                onChange={handleChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              />
              <p className="mt-1 text-xs text-text-secondary">
                The base URL for the LHDN MyInvois API.
              </p>
            </div>

            <div>
              <label htmlFor="businessRegNo" className="block text-sm font-medium text-text-primary">
                Business Registration Number
              </label>
              <input
                type="text"
                name="businessRegNo"
                id="businessRegNo"
                value={settings.businessRegNo}
                onChange={handleChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              />
              <p className="mt-1 text-xs text-text-secondary">
                Your company's business registration number.
              </p>
            </div>

            <div>
              <label htmlFor="businessName" className="block text-sm font-medium text-text-primary">
                Business Name
              </label>
              <input
                type="text"
                name="businessName"
                id="businessName"
                value={settings.businessName}
                onChange={handleChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              />
              <p className="mt-1 text-xs text-text-secondary">
                Your company's registered business name.
              </p>
            </div>

            <div>
              <label htmlFor="certificatePath" className="block text-sm font-medium text-text-primary">
                Digital Certificate Path
              </label>
              <input
                type="text"
                name="certificatePath"
                id="certificatePath"
                value={settings.certificatePath}
                onChange={handleChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              />
              <p className="mt-1 text-xs text-text-secondary">
                Path to your LHDN MyInvois digital certificate (.p12 file).
              </p>
            </div>

            <div>
              <label htmlFor="certificatePassword" className="block text-sm font-medium text-text-primary">
                Certificate Password
              </label>
              <input
                type="password"
                name="certificatePassword"
                id="certificatePassword"
                value={settings.certificatePassword}
                onChange={handleChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              />
              <p className="mt-1 text-xs text-text-secondary">
                Password for your digital certificate.
              </p>
            </div>

            <div className="flex items-center space-x-2">
              <label className="block text-sm font-medium text-text-primary">
                Environment
              </label>
              <select
                name="environment"
                value={settings.environment}
                onChange={handleChange}
                className="rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              >
                <option value="sandbox">Sandbox</option>
                <option value="production">Production</option>
              </select>
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="isActive"
                name="isActive"
                checked={settings.isActive}
                onChange={(e) => setSettings(prev => ({
                  ...prev,
                  isActive: e.target.checked,
                }))}
                className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
              />
              <label htmlFor="isActive" className="text-sm font-medium text-text-primary">
                Enable LHDN MyInvois Integration
              </label>
            </div>

            {testResult && (
              <div
                className={cn(
                  "p-4 rounded-md",
                  testResult.success ? "bg-green-50" : "bg-red-50"
                )}
              >
                <div className="flex">
                  <div className="flex-shrink-0">
                    {testResult.success ? (
                      <svg className="h-5 w-5 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    ) : (
                      <svg className="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    )}
                  </div>
                  <div className="ml-3">
                    <p
                      className={cn(
                        "text-sm font-medium",
                        testResult.success ? "text-green-800" : "text-red-800"
                      )}
                    >
                      {testResult.message}
                    </p>
                  </div>
                </div>
              </div>
            )}

            <div className="flex justify-end space-x-3">
              <Button
                type="button"
                variant="outline"
                onClick={testConnection}
                disabled={isTesting}
              >
                {isTesting ? (
                  <>
                    <Spinner size="sm" className="mr-2" />
                    Testing...
                  </>
                ) : (
                  'Test Connection'
                )}
              </Button>
              <Button
                type="submit"
                disabled={isSaving}
              >
                {isSaving ? (
                  <>
                    <Spinner size="sm" className="mr-2" />
                    Saving...
                  </>
                ) : (
                  'Save Settings'
                )}
              </Button>
            </div>
          </form>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button
            type="button"
            variant="outline"
            onClick={testConnection}
            disabled={isTesting}
          >
            {isTesting ? (
              <>
                <Spinner size="sm" className="mr-2" />
                Testing...
              </>
            ) : (
              'Test Connection'
            )}
          </Button>
          <Button
            type="submit"
            form="lhdn-settings-form"
            disabled={isSaving}
          >
            {isSaving ? (
              <>
                <Spinner size="sm" className="mr-2" />
                Saving...
              </>
            ) : (
              'Save Settings'
            )}
          </Button>
        </CardFooter>
      </Card>

      <Card className="border-none shadow-md">
        <CardHeader className="pb-2 border-b">
          <CardTitle className="text-lg font-bold text-text-primary">Certificate Management</CardTitle>
          <CardDescription className="text-text-secondary">Manage digital certificates for LHDN MyInvois</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              <span className="font-medium text-sm">Certificate Status:</span>
              {certificateStatus === 'valid' ? (
                <Badge variant="success">Valid</Badge>
              ) : certificateStatus === 'expired' ? (
                <Badge variant="destructive">Expired</Badge>
              ) : (
                <Badge variant="outline">Missing</Badge>
              )}
            </div>
          </div>

          <p className="text-sm text-text-secondary mb-4">
            Digital certificates are required for secure communication with the LHDN MyInvois API.
            These certificates authenticate your application and encrypt the data transmitted between your system and LHDN.
          </p>

          <Link href="/dashboard/admin/certificates">
            <Button size="sm" className="bg-indigo-600 hover:bg-indigo-700">
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
              Manage Certificates
            </Button>
          </Link>
        </CardContent>
      </Card>

      <Card className="border-none shadow-md">
        <CardHeader className="pb-2 border-b">
          <CardTitle className="text-lg font-bold text-text-primary">LHDN MyInvois API Documentation</CardTitle>
          <CardDescription className="text-text-secondary">Learn more about the LHDN MyInvois API and how to use it</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-text-primary">Official Documentation</h3>
              <p className="mt-1 text-sm text-text-secondary">
                Access the official LHDN MyInvois API documentation for detailed information on endpoints and requirements.
              </p>
              <a
                href="https://sdk.myinvois.hasil.gov.my/einvoicingapi/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-indigo-600 hover:text-indigo-800 text-sm font-medium mt-2 inline-block"
              >
                View Documentation →
              </a>
            </div>

            <div>
              <h3 className="text-sm font-medium text-text-primary">Integration Guide</h3>
              <p className="mt-1 text-sm text-text-secondary">
                Read our guide on integrating with the LHDN MyInvois API.
              </p>
              <Link
                href="/docs/lhdn-integration"
                className="text-indigo-600 hover:text-indigo-800 text-sm font-medium mt-2 inline-block"
              >
                View Guide →
              </Link>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
