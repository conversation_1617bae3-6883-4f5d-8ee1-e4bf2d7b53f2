import express from 'express';
import { notificationController } from '../controllers/notification.controller';
import { authenticate } from '../middleware/auth.middleware';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticate);

// Notification settings
router.get('/settings', notificationController.getSettings);
router.put('/settings', notificationController.updateSettings);

// Email settings
router.get('/email/settings', notificationController.getEmailSettings);
router.put('/email/settings', notificationController.updateEmailSettings);
router.post('/email/test', notificationController.testEmailConnection);

// Email templates
router.get('/email/templates', notificationController.getEmailTemplates);
router.get('/email/templates/:name', notificationController.getEmailTemplate);
router.put('/email/templates/:name', notificationController.updateEmailTemplate);

// WhatsApp settings
router.get('/whatsapp/settings', notificationController.getWhatsAppSettings);
router.put('/whatsapp/settings', notificationController.updateWhatsAppSettings);
router.post('/whatsapp/test', notificationController.testWhatsAppConnection);

// WhatsApp templates
router.get('/whatsapp/templates', notificationController.getWhatsAppTemplates);

// Customer notification preferences
router.get('/customers/:customerId/preferences', notificationController.getCustomerPreferences);
router.put('/customers/:customerId/preferences', notificationController.updateCustomerPreferences);

// Invoice notification settings
router.get('/invoices/:invoiceId/settings', notificationController.getInvoiceNotificationSettings);
router.put('/invoices/:invoiceId/settings', notificationController.updateInvoiceNotificationSettings);
router.post('/invoices/:invoiceId/send', notificationController.sendInvoiceNotification);

// Notification history
router.get('/history', notificationController.getNotificationHistory);
router.post('/:notificationId/resend', notificationController.resendNotification);

export default router;
