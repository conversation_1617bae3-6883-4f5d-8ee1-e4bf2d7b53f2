'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import ResetPasswordForm from '@/components/forms/ResetPasswordForm';
import { apiClient } from '@/lib/api/client';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';

export default function ResetPasswordPage({ params }: { params: { token: string } }) {
  const [isValidToken, setIsValidToken] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    const verifyToken = async () => {
      try {
        // Verify the token is valid
        await apiClient.post('/auth/verify-reset-token', { token: params.token }, { skipAuth: true });
        setIsValidToken(true);
      } catch (err: any) {
        console.error('Token verification error:', err);
        setError(err.message || 'Invalid or expired password reset token.');
        setIsValidToken(false);
      } finally {
        setIsLoading(false);
      }
    };

    verifyToken();
  }, [params.token]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="w-full max-w-md text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Verifying your reset link...</p>
        </div>
      </div>
    );
  }

  if (!isValidToken) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          <div className="mb-8 text-center">
            <Link href="/" className="inline-block">
              <h1 className="text-3xl font-bold text-indigo-600">Invoix</h1>
            </Link>
          </div>
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{error || 'Invalid or expired password reset link.'}</AlertDescription>
          </Alert>
          <p className="text-center mb-6">
            The password reset link is invalid or has expired. Please request a new password reset link.
          </p>
          <div className="flex justify-center">
            <Link href="/forgot-password">
              <Button>Request New Reset Link</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col md:flex-row">
      {/* Left side - Form */}
      <div className="flex-1 flex flex-col justify-center items-center p-8 md:p-12">
        <div className="w-full max-w-md">
          <div className="mb-8 text-center">
            <Link href="/" className="inline-block">
              <h1 className="text-3xl font-bold text-indigo-600">Invoix</h1>
            </Link>
          </div>
          <ResetPasswordForm token={params.token} />
        </div>
      </div>
      
      {/* Right side - Image and info */}
      <div className="hidden md:flex flex-1 bg-indigo-600 text-white p-12 flex-col justify-center">
        <div className="max-w-md mx-auto">
          <h2 className="text-3xl font-bold mb-6">Create a new password</h2>
          <p className="text-lg mb-8">
            Choose a strong password to keep your account secure.
          </p>
          <div className="bg-indigo-500/30 p-6 rounded-lg">
            <h3 className="text-xl font-semibold mb-4">Password Tips</h3>
            <ul className="space-y-2">
              <li className="flex items-start">
                <svg className="h-5 w-5 mr-2 text-indigo-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span>Use at least 8 characters</span>
              </li>
              <li className="flex items-start">
                <svg className="h-5 w-5 mr-2 text-indigo-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span>Include uppercase and lowercase letters</span>
              </li>
              <li className="flex items-start">
                <svg className="h-5 w-5 mr-2 text-indigo-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span>Include at least one number and symbol</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
