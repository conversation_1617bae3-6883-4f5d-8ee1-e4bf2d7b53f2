import { Router } from 'express';
import { basicHealth, detailedHealth } from '../controllers/health.controller';
import { authenticate } from '../middleware/auth.middleware';
import { ExpressHandler } from '../types/express';

const router = Router();

// Basic health check - public
router.get('/', basicHealth as ExpressHandler);

// Detailed health check - requires authentication
router.get('/detailed', authenticate as ExpressHandler, detailedHealth as ExpressHandler);

export default router;
