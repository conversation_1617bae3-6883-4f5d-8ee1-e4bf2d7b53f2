'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';
import { DataTable } from '@/components/ui/data-table';
import { ColumnDef } from '@tanstack/react-table';

interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  taxId: string;
  status: 'active' | 'inactive';
  totalSpent: number;
  lastInvoice: string;
}

export default function CustomersPage() {
  const [searchQuery, setSearchQuery] = useState('');

  // Mock data for customers
  const customers: Customer[] = [
    {
      id: 'CUST-001',
      name: 'Acme Corporation',
      email: '<EMAIL>',
      phone: '+60*********',
      taxId: '*********',
      status: 'active',
      totalSpent: 25000,
      lastInvoice: '2023-06-15',
    },
    {
      id: 'CUST-002',
      name: 'Wayne Enterprises',
      email: '<EMAIL>',
      phone: '+60123456790',
      taxId: '*********',
      status: 'active',
      totalSpent: 18000,
      lastInvoice: '2023-06-10',
    },
    {
      id: 'CUST-003',
      name: 'Stark Industries',
      email: '<EMAIL>',
      phone: '+60123456791',
      taxId: '*********',
      status: 'inactive',
      totalSpent: 32000,
      lastInvoice: '2023-05-20',
    },
    {
      id: 'CUST-004',
      name: 'LexCorp',
      email: '<EMAIL>',
      phone: '+60123456792',
      taxId: '*********',
      status: 'active',
      totalSpent: 15000,
      lastInvoice: '2023-06-05',
    },
    {
      id: 'CUST-005',
      name: 'Umbrella Corporation',
      email: '<EMAIL>',
      phone: '+60123456793',
      taxId: '*********',
      status: 'active',
      totalSpent: 28000,
      lastInvoice: '2023-06-12',
    },
  ];

  // Define columns for the customer table
  const columns: ColumnDef<Customer>[] = [
    {
      accessorKey: 'name',
      header: 'Customer Name',
      cell: ({ row }) => (
        <div>
          <div className="font-medium">{row.original.name}</div>
          <div className="text-sm text-text-secondary">{row.original.email}</div>
        </div>
      ),
    },
    {
      accessorKey: 'phone',
      header: 'Phone',
    },
    {
      accessorKey: 'taxId',
      header: 'Tax ID',
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          row.original.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
        }`}>
          {row.original.status === 'active' && (
            <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          )}
          {row.original.status === 'active' ? 'Active' : 'Inactive'}
        </span>
      ),
    },
    {
      accessorKey: 'totalSpent',
      header: 'Total Spent',
      cell: ({ row }) => (
        <div className="font-medium">
          RM {row.original.totalSpent.toLocaleString()}
        </div>
      ),
    },
    {
      accessorKey: 'lastInvoice',
      header: 'Last Invoice',
      cell: ({ row }) => {
        const date = new Date(row.original.lastInvoice);
        return date.toLocaleDateString('en-MY', { year: 'numeric', month: 'short', day: 'numeric' });
      },
    },
    {
      id: 'actions',
      cell: ({ row }) => (
        <div className="flex justify-end space-x-2">
          <Link href={`/dashboard/customers/${row.original.id}/edit`}>
            <Button variant="ghost" size="sm" className="text-indigo-600 hover:text-indigo-800 hover:bg-indigo-50">
              View
            </Button>
          </Link>
          <Button variant="ghost" size="sm" className="text-gray-600 hover:text-gray-800 hover:bg-gray-50">
            Edit
          </Button>
          <Button variant="ghost" size="sm" className="text-gray-600 hover:text-gray-800 hover:bg-gray-50">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
            </svg>
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between bg-white p-6 rounded-lg shadow-sm mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <svg className="w-6 h-6 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            Customers
          </h1>
          <p className="mt-1 text-sm text-text-secondary">
            Manage your customer information and tax IDs
          </p>
        </div>
        <div className="mt-4 md:mt-0 flex space-x-3">
          <Link href="/dashboard/customers/validate-tins">
            <Button variant="outline" size="sm">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Validate Tax IDs
            </Button>
          </Link>
          <Link href="/dashboard/customers/new">
            <Button size="sm" className="bg-indigo-600 hover:bg-indigo-700">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Add Customer
            </Button>
          </Link>
        </div>
      </div>

      {/* Customer Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="border-none shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-text-secondary">Total Customers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">125</div>
          </CardContent>
        </Card>

        <Card className="border-none shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-text-secondary">Active Customers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">112</div>
          </CardContent>
        </Card>

        <Card className="border-none shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-text-secondary">Validated Tax IDs</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-indigo-600">98</div>
          </CardContent>
        </Card>
      </div>

      {/* Customer Table */}
      <Card className="border-none shadow-md">
        <CardHeader className="pb-2 border-b">
          <CardTitle className="text-lg font-bold text-text-primary">Customer Directory</CardTitle>
          <CardDescription className="text-text-secondary">View and manage your customers</CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <DataTable columns={columns} data={customers} searchKey="name" />
        </CardContent>
      </Card>
    </div>
  );
}
