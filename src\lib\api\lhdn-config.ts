import { apiClient } from './client';

/**
 * Get the current LHDN configuration
 */
export const getLHDNConfig = async () => {
  try {
    // For development, return mock data
    if (process.env.NODE_ENV === 'development') {
      // Simulate API response with mock data
      return {
        apiBaseUrl: 'https://sandbox.myinvois.hasil.gov.my/einvoicing',
        isActive: true,
        environment: 'sandbox',
        certificatePath: '/path/to/certificate.p12',
        certificatePassword: '********',
      };
    }

    // In production, use the API client
    const response = await apiClient.get('/lhdn-config');
    return response;
  } catch (error: any) {
    console.error('Error getting LHDN configuration:', error);
    throw new Error(error.message || 'Failed to get LHDN configuration');
  }
};

/**
 * Update the LHDN configuration
 * @param config LHDN configuration to update
 */
export const updateLHDNConfig = async (config: {
  apiBaseUrl: string;
  certificatePath?: string;
  certificatePassword?: string;
  isActive?: boolean;
}) => {
  try {
    // For development, return mock data
    if (process.env.NODE_ENV === 'development') {
      // Simulate API response with mock data
      await new Promise(resolve => setTimeout(resolve, 500)); // Simulate network delay
      return {
        success: true,
        message: 'LHDN configuration updated successfully',
        config: {
          apiBaseUrl: config.apiBaseUrl,
          isActive: config.isActive,
          certificatePath: config.certificatePath ? '/path/to/certificate.p12' : undefined,
        },
      };
    }

    // In production, use the API client
    const response = await apiClient.put('/lhdn-config', config);
    return response;
  } catch (error: any) {
    console.error('Error updating LHDN configuration:', error);
    throw new Error(error.message || 'Failed to update LHDN configuration');
  }
};

/**
 * Test the LHDN configuration
 */
export const testLHDNConfig = async () => {
  try {
    // For development, return mock data
    if (process.env.NODE_ENV === 'development') {
      // Simulate API response with mock data
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate network delay

      // Randomly succeed or fail for demonstration purposes
      const success = Math.random() > 0.3;

      return {
        success,
        message: success
          ? 'Successfully connected to LHDN MyInvois API'
          : 'Failed to connect to LHDN MyInvois API. Please check your credentials.',
      };
    }

    // In production, use the API client
    const response = await apiClient.post('/lhdn-config/test');
    return response;
  } catch (error: any) {
    console.error('Error testing LHDN configuration:', error);
    throw new Error(error.message || 'Failed to test LHDN configuration');
  }
};
