// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Multi-tenant models
model Tenant {
  id                String              @id @default(uuid())
  name              String
  businessName      String
  businessRegNo     String?             // Business registration number for LHDN
  businessRegValid  Boolean             @default(false) // Whether the business registration is validated
  email             String              @unique
  phone             String?
  address           String?
  logo              String?
  isActive          Boolean             @default(true)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  // Core modules
  users             User[]
  customers         Customer[]
  invoices          Invoice[]
  invoiceItems      InvoiceItem[]
  payments          Payment[]
  invoiceTemplates  InvoiceTemplate[]
  paymentSettings   PaymentSetting[]
  whatsappSettings  WhatsappSetting?

  @@index([businessRegNo])
}

model User {
  id                String              @id @default(uuid())
  email             String              @unique
  password          String
  name              String
  role              UserRole            @default(USER)
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  refreshToken      String?             // For JWT refresh token
  lastLogin         DateTime?
  emailVerified     Boolean             @default(false)
  twoFactorEnabled  Boolean             @default(false)
  twoFactorSecret   String?
  recoveryCodes     String?             // Comma-separated list of recovery codes
  loginAttempts     Int                 @default(0)
  lockedUntil       DateTime?           // Account lockout time
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([tenantId])
}

enum UserRole {
  ADMIN
  MANAGER
  USER
}

// Digital certificates for LHDN MyInvois
model LHDNCertificate {
  id                String              @id @default(uuid())
  name              String
  filename          String              @unique
  encryptedData     String              @db.Text
  isActive          Boolean             @default(false)
  expiryDate        DateTime
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
}

// LHDN MyInvois platform-level configuration
model LHDNConfig {
  id                String              @id @default(uuid())
  apiBaseUrl        String              @default("https://sandbox.myinvois.hasil.gov.my/einvoicing")
  certificatePath   String?
  certificatePassword String?           @db.Text
  isActive          Boolean             @default(false)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
}

// Core modules
model Customer {
  id                String              @id @default(uuid())
  name              String
  email             String
  phone             String?
  address           String?
  taxId             String?             // Tax Identification Number
  taxIdValidated    Boolean             @default(false)
  creditLimit       Float?
  notes             String?
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  invoices          Invoice[]
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@unique([email, tenantId])
  @@index([tenantId])
  @@index([taxId])
}

model Invoice {
  id                String              @id @default(uuid())
  invoiceNumber     String
  issueDate         DateTime            @default(now())
  dueDate           DateTime
  status            InvoiceStatus       @default(DRAFT)
  totalAmount       Float
  tax               Float               @default(0)
  notes             String?
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  customerId        String
  customer          Customer            @relation(fields: [customerId], references: [id])
  items             InvoiceItem[]
  payments          Payment[]
  validationId      String?             // LHDN validation ID
  validationStatus  ValidationStatus?
  validationDate    DateTime?
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@unique([invoiceNumber, tenantId])
  @@index([tenantId])
  @@index([customerId])
  @@index([status])
}

enum InvoiceStatus {
  DRAFT
  SENT
  PAID
  OVERDUE
  CANCELLED
  REJECTED
}

enum ValidationStatus {
  PENDING
  VALID
  INVALID
  FAILED
}

model InvoiceItem {
  id                String              @id @default(uuid())
  description       String
  quantity          Float
  unitPrice         Float
  amount            Float
  invoiceId         String
  invoice           Invoice             @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([invoiceId])
  @@index([tenantId])
}

model Payment {
  id                String              @id @default(uuid())
  amount            Float
  paymentDate       DateTime
  paymentMethod     PaymentMethod
  referenceNumber   String?
  notes             String?
  invoiceId         String
  invoice           Invoice             @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([invoiceId])
  @@index([tenantId])
}

enum PaymentMethod {
  BANK_TRANSFER
  CREDIT_CARD
  CASH
  CHEQUE
  ONLINE
}

model InvoiceTemplate {
  id                String              @id @default(uuid())
  name              String
  template          String              @db.Text
  isDefault         Boolean             @default(false)
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([tenantId])
}

model PaymentSetting {
  id                String              @id @default(uuid())
  provider          String              // Stripe, Razorpay, etc.
  apiKey            String              @db.Text
  apiSecret         String              @db.Text
  isActive          Boolean             @default(true)
  tenantId          String
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([tenantId])
}

model WhatsappSetting {
  id                String              @id @default(uuid())
  provider          String              // Twilio, 360dialog, etc.
  apiKey            String              @db.Text
  phoneNumber       String
  isActive          Boolean             @default(true)
  tenantId          String              @unique
  tenant            Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
}

