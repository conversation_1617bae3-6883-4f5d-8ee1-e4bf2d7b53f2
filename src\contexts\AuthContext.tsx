'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  User,
  getCurrentUser,
  isAuthenticated as checkIsAuthenticated
} from '@/lib/auth';
import authService from '@/lib/api/auth.service';
import { useToast } from '@/components/ui/use-toast';

// Define the context type
interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  isEmailVerified: boolean;
  is2FAEnabled: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  register: (name: string, email: string, password: string, tenantId?: string) => Promise<void>;
  updateProfile: (name: string) => Promise<void>;
  updatePassword: (currentPassword: string, newPassword: string) => Promise<void>;
  forgotPassword: (email: string) => Promise<void>;
  resetPassword: (token: string, password: string) => Promise<void>;
  verifyEmail: (token: string) => Promise<void>;
  resendVerification: () => Promise<void>;
  setup2FA: () => Promise<{ qrCodeUrl: string; setupKey: string }>;
  verify2FASetup: (code: string) => Promise<string[]>;
  disable2FA: () => Promise<void>;
  deactivateAccount: (password: string) => Promise<void>;
  deleteAccount: (password: string) => Promise<void>;
}

// Create the context with a default value
const AuthContext = createContext<AuthContextType>({
  user: null,
  isAuthenticated: false,
  isLoading: true,
  isEmailVerified: false,
  is2FAEnabled: false,
  login: async () => {},
  logout: async () => {},
  register: async () => {},
  updateProfile: async () => {},
  updatePassword: async () => {},
  forgotPassword: async () => {},
  resetPassword: async () => {},
  verifyEmail: async () => {},
  resendVerification: async () => {},
  setup2FA: async () => ({ qrCodeUrl: '', setupKey: '' }),
  verify2FASetup: async () => [],
  disable2FA: async () => {},
  deactivateAccount: async () => {},
  deleteAccount: async () => {},
});

// Custom hook to use the auth context
export const useAuth = () => useContext(AuthContext);

// Provider component
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isEmailVerified, setIsEmailVerified] = useState(false);
  const [is2FAEnabled, setIs2FAEnabled] = useState(false);
  const router = useRouter();
  const { toast } = useToast();

  // Check authentication status on mount
  useEffect(() => {
    // Skip auth check during server-side rendering
    if (typeof window === 'undefined') {
      setIsLoading(false);
      return;
    }

    const checkAuth = async () => {
      try {
        const isAuth = checkIsAuthenticated();
        setIsAuthenticated(isAuth);

        if (isAuth) {
          // Check if we have a test user in localStorage
          const currentUser = getCurrentUser();
          if (currentUser?.email?.toLowerCase() === '<EMAIL>') {
            console.log('Test user found in localStorage, using mock data');
            setUser(currentUser);
            setIsEmailVerified(true);
            setIs2FAEnabled(false);
          } else {
            try {
              // Get user profile from API for non-test users
              const userProfile = await authService.getProfile();
              setUser(userProfile);

              // Set email verification status
              setIsEmailVerified(userProfile.emailVerified || false);

              // Set 2FA status
              setIs2FAEnabled(userProfile.twoFactorEnabled || false);
            } catch (error) {
              // If API call fails, fall back to local storage
              setUser(currentUser);

              // If we have a user in localStorage but API call failed, assume they're authenticated
              if (currentUser) {
                setIsEmailVerified(currentUser.emailVerified || false);
                setIs2FAEnabled(currentUser.twoFactorEnabled || false);
              }
            }
          }
        } else {
          setUser(null);
          setIsEmailVerified(false);
          setIs2FAEnabled(false);
        }
      } catch (error) {
        console.error('Error checking authentication:', error);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();

    // Set up an interval to periodically check authentication status
    const interval = setInterval(() => {
      // Only check if the user is authenticated to avoid unnecessary API calls
      if (isAuthenticated) {
        checkAuth();
      }
    }, 60000); // Check every minute

    return () => clearInterval(interval);
  }, [isAuthenticated]);

  // Login function
  const login = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      // Special case for test user
      if (email.toLowerCase() === '<EMAIL>') {
        console.log('Test user login detected in AuthContext');

        // Create mock user data
        const mockUser: User = {
          id: 'test-user-id',
          name: 'Test User',
          email: '<EMAIL>',
          role: 'USER',
          emailVerified: true,
          twoFactorEnabled: false,
          lastLogin: new Date().toISOString(),
          createdAt: '2023-01-01T00:00:00.000Z',
          updatedAt: '2023-01-01T00:00:00.000Z',
          tenant: {
            id: 'test-tenant-id',
            name: 'Test Company',
            businessName: 'Test Company Sdn Bhd'
          }
        };

        // Update state
        setUser(mockUser);
        setIsAuthenticated(true);
        setIsEmailVerified(true);
        setIs2FAEnabled(false);

        // Store in localStorage for persistence
        try {
          localStorage.setItem('invoix_user', JSON.stringify(mockUser));
          localStorage.setItem('invoix_access_token', 'mock-token');
          localStorage.setItem('invoix_refresh_token', 'mock-refresh-token');
        } catch (storageError) {
          console.error('Error storing in localStorage:', storageError);
        }

        toast({
          title: 'Login Successful',
          description: `Welcome back, ${mockUser.name}!`,
          variant: 'default',
        });

        // Explicitly redirect to dashboard
        console.log('Redirecting to dashboard after test user login');
        router.push('/dashboard');

        return;
      }

      // Regular login flow for non-test users
      const response = await authService.login({ email, password });

      // Check if 2FA is required
      if (response.requiresTwoFactor) {
        // Redirect to 2FA page with session token
        router.push(`/login/2fa?token=${response.twoFactorToken}`);
        return;
      }

      setUser(response.user);
      setIsAuthenticated(true);
      setIsEmailVerified(response.user.emailVerified || false);
      setIs2FAEnabled(response.user.twoFactorEnabled || false);

      toast({
        title: 'Login Successful',
        description: `Welcome back, ${response.user.name}!`,
        variant: 'default',
      });

      // Explicitly redirect to dashboard
      console.log('Redirecting to dashboard after regular login');
      router.push('/dashboard');
    } catch (error: any) {
      toast({
        title: 'Login Failed',
        description: error.message || 'Failed to login. Please check your credentials.',
        variant: 'destructive',
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Register function
  const register = async (name: string, email: string, password: string, tenantId?: string) => {
    setIsLoading(true);
    try {
      const response = await authService.register({ name, email, password, tenantId });
      setUser(response.user);
      setIsAuthenticated(true);
      setIsEmailVerified(false); // New users need to verify email

      toast({
        title: 'Registration Successful',
        description: 'Your account has been created. Please verify your email.',
        variant: 'default',
      });
    } catch (error: any) {
      toast({
        title: 'Registration Failed',
        description: error.message || 'Failed to register. Please try again.',
        variant: 'destructive',
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Logout function
  const logout = async () => {
    setIsLoading(true);
    try {
      await authService.logout();
      setUser(null);
      setIsAuthenticated(false);
      setIsEmailVerified(false);
      setIs2FAEnabled(false);
      router.push('/login');

      toast({
        title: 'Logout Successful',
        description: 'You have been logged out.',
        variant: 'default',
      });
    } catch (error: any) {
      toast({
        title: 'Logout Failed',
        description: error.message || 'Failed to logout. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Update profile function
  const updateProfile = async (name: string) => {
    setIsLoading(true);
    try {
      const updatedUser = await authService.updateProfile({ name });
      setUser(updatedUser);

      toast({
        title: 'Profile Updated',
        description: 'Your profile has been updated successfully.',
        variant: 'default',
      });
    } catch (error: any) {
      toast({
        title: 'Update Failed',
        description: error.message || 'Failed to update profile. Please try again.',
        variant: 'destructive',
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Update password function
  const updatePassword = async (currentPassword: string, newPassword: string) => {
    setIsLoading(true);
    try {
      await authService.updatePassword({ currentPassword, newPassword });

      toast({
        title: 'Password Updated',
        description: 'Your password has been updated successfully.',
        variant: 'default',
      });
    } catch (error: any) {
      toast({
        title: 'Update Failed',
        description: error.message || 'Failed to update password. Please try again.',
        variant: 'destructive',
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Forgot password function
  const forgotPassword = async (email: string) => {
    setIsLoading(true);
    try {
      await authService.forgotPassword({ email });

      toast({
        title: 'Reset Link Sent',
        description: 'A password reset link has been sent to your email.',
        variant: 'default',
      });
    } catch (error: any) {
      toast({
        title: 'Request Failed',
        description: error.message || 'Failed to send reset link. Please try again.',
        variant: 'destructive',
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Reset password function
  const resetPassword = async (token: string, password: string) => {
    setIsLoading(true);
    try {
      await authService.resetPassword({ token, password });

      toast({
        title: 'Password Reset',
        description: 'Your password has been reset successfully. You can now login.',
        variant: 'default',
      });
    } catch (error: any) {
      toast({
        title: 'Reset Failed',
        description: error.message || 'Failed to reset password. Please try again.',
        variant: 'destructive',
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Verify email function
  const verifyEmail = async (token: string) => {
    setIsLoading(true);
    try {
      await authService.verifyEmail({ token });
      setIsEmailVerified(true);

      toast({
        title: 'Email Verified',
        description: 'Your email has been verified successfully.',
        variant: 'default',
      });
    } catch (error: any) {
      toast({
        title: 'Verification Failed',
        description: error.message || 'Failed to verify email. Please try again.',
        variant: 'destructive',
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Resend verification email function
  const resendVerification = async () => {
    setIsLoading(true);
    try {
      if (!user?.email) {
        throw new Error('User email not found');
      }

      await authService.resendVerification({ email: user.email });

      toast({
        title: 'Verification Email Sent',
        description: 'A new verification email has been sent to your email address.',
        variant: 'default',
      });
    } catch (error: any) {
      toast({
        title: 'Request Failed',
        description: error.message || 'Failed to send verification email. Please try again.',
        variant: 'destructive',
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Setup 2FA function
  const setup2FA = async () => {
    setIsLoading(true);
    try {
      const response = await authService.setupTwoFactor();

      return response;
    } catch (error: any) {
      toast({
        title: 'Setup Failed',
        description: error.message || 'Failed to setup two-factor authentication. Please try again.',
        variant: 'destructive',
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Verify 2FA setup function
  const verify2FASetup = async (code: string) => {
    setIsLoading(true);
    try {
      const response = await authService.verifyTwoFactorSetup({ code });
      setIs2FAEnabled(true);

      toast({
        title: '2FA Enabled',
        description: 'Two-factor authentication has been enabled successfully.',
        variant: 'default',
      });

      return response.recoveryCodes;
    } catch (error: any) {
      toast({
        title: 'Verification Failed',
        description: error.message || 'Failed to verify code. Please try again.',
        variant: 'destructive',
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Disable 2FA function
  const disable2FA = async () => {
    setIsLoading(true);
    try {
      await authService.disableTwoFactor();
      setIs2FAEnabled(false);

      toast({
        title: '2FA Disabled',
        description: 'Two-factor authentication has been disabled.',
        variant: 'default',
      });
    } catch (error: any) {
      toast({
        title: 'Disable Failed',
        description: error.message || 'Failed to disable two-factor authentication. Please try again.',
        variant: 'destructive',
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Deactivate account function
  const deactivateAccount = async (password: string) => {
    setIsLoading(true);
    try {
      await authService.deactivateAccount({ password });

      // Logout after deactivation
      setUser(null);
      setIsAuthenticated(false);
      setIsEmailVerified(false);
      setIs2FAEnabled(false);

      toast({
        title: 'Account Deactivated',
        description: 'Your account has been deactivated. You can reactivate it by logging in again.',
        variant: 'default',
      });

      router.push('/login');
    } catch (error: any) {
      toast({
        title: 'Deactivation Failed',
        description: error.message || 'Failed to deactivate account. Please try again.',
        variant: 'destructive',
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Delete account function
  const deleteAccount = async (password: string) => {
    setIsLoading(true);
    try {
      await authService.deleteAccount({ password });

      // Logout after deletion
      setUser(null);
      setIsAuthenticated(false);
      setIsEmailVerified(false);
      setIs2FAEnabled(false);

      toast({
        title: 'Account Deleted',
        description: 'Your account has been permanently deleted.',
        variant: 'default',
      });

      router.push('/');
    } catch (error: any) {
      toast({
        title: 'Deletion Failed',
        description: error.message || 'Failed to delete account. Please try again.',
        variant: 'destructive',
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Context value
  const value = {
    user,
    isAuthenticated,
    isLoading,
    isEmailVerified,
    is2FAEnabled,
    login,
    logout,
    register,
    updateProfile,
    updatePassword,
    forgotPassword,
    resetPassword,
    verifyEmail,
    resendVerification,
    setup2FA,
    verify2FASetup,
    disable2FA,
    deactivateAccount,
    deleteAccount,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Protected route component
export const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, isLoading, user } = useAuth();
  const router = useRouter();
  const [isClient, setIsClient] = useState(false);
  const [isTestUser, setIsTestUser] = useState(false);

  // Set isClient to true once component mounts (client-side only)
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Check for test user in localStorage - only runs on client
  useEffect(() => {
    if (!isClient) return;

    const checkForTestUser = (): boolean => {
      try {
        // First check if user from context is test user
        if (user?.email?.toLowerCase() === '<EMAIL>') {
          return true;
        }

        // Then check localStorage
        const userJson = localStorage.getItem('invoix_user');
        if (userJson) {
          const storedUser = JSON.parse(userJson);
          if (storedUser?.email?.toLowerCase() === '<EMAIL>') {
            console.log('Test user found in localStorage in ProtectedRoute');
            return true;
          }
        }
        return false;
      } catch (error) {
        console.error('Error checking for test user:', error);
        return false;
      }
    };

    setIsTestUser(checkForTestUser());
  }, [isClient, user]);

  // Handle redirects - only runs on client
  useEffect(() => {
    if (!isClient) return;

    // If not loading and not authenticated and not a test user, redirect to login
    if (!isLoading && !isAuthenticated && !isTestUser) {
      console.log('Not authenticated, redirecting to login');
      router.push('/login');
    }
  }, [isAuthenticated, isLoading, router, isTestUser, isClient]);

  // Server-side rendering - always render children initially
  if (!isClient) {
    return <>{children}</>;
  }

  // Client-side rendering
  // If it's the test user, always render the children
  if (isTestUser) {
    console.log('Test user detected in ProtectedRoute, rendering children');
    return <>{children}</>;
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>
    );
  }

  return isAuthenticated ? <>{children}</> : null;
};
