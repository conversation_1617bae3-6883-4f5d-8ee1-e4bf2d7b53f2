import { NextRequest, NextResponse } from 'next/server';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    const { reason } = body;
    
    if (!reason) {
      return NextResponse.json(
        { message: 'Rejection reason is required' },
        { status: 400 }
      );
    }
    
    // In a real implementation, this would call your backend API
    // const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/documents/${id}/reject`, {
    //   method: 'POST',
    //   headers: {
    //     'Content-Type': 'application/json',
    //     'Authorization': `Bearer ${session.accessToken}`,
    //   },
    //   body: JSON.stringify({ reason }),
    // });
    // const data = await response.json();
    
    // For development, use mock data
    return NextResponse.json({
      message: 'Invoice rejected successfully',
      invoice: {
        id,
        status: 'REJECTED',
      },
      lhdn: {
        success: true,
        documentId: id,
        status: 'REJECTED',
        timestamp: new Date().toISOString(),
        message: 'Document rejected successfully (mock)',
      },
    });
  } catch (error) {
    console.error('Error rejecting document:', error);
    return NextResponse.json(
      { message: 'Failed to reject document' },
      { status: 500 }
    );
  }
}
