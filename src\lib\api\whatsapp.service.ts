import { apiClient } from './client';

// Types
export interface WhatsAppTemplate {
  id: string;
  name: string;
  language: string;
  category: string;
  status: 'APPROVED' | 'PENDING' | 'REJECTED';
  components: any[];
}

export interface WhatsAppMessage {
  id: string;
  to: string;
  templateName: string;
  templateData: Record<string, any>;
  status: 'QUEUED' | 'SENT' | 'DELIVERED' | 'READ' | 'FAILED';
  createdAt: string;
  updatedAt: string;
}

export interface WhatsAppSettings {
  enabled: boolean;
  provider: 'TWILIO' | 'META' | 'OTHER';
  accountSid?: string;
  authToken?: string;
  phoneNumber?: string;
  apiKey?: string;
  apiSecret?: string;
}

/**
 * WhatsApp API Service
 * Provides methods for interacting with WhatsApp messaging API
 */
class WhatsAppService {
  /**
   * Get WhatsApp settings
   */
  async getSettings(): Promise<WhatsAppSettings> {
    return apiClient.get('/whatsapp/settings');
  }

  /**
   * Update WhatsApp settings
   */
  async updateSettings(settings: Partial<WhatsAppSettings>): Promise<WhatsAppSettings> {
    return apiClient.put('/whatsapp/settings', settings);
  }

  /**
   * Get all WhatsApp templates
   */
  async getTemplates(): Promise<WhatsAppTemplate[]> {
    return apiClient.get('/whatsapp/templates');
  }

  /**
   * Get WhatsApp template by ID
   */
  async getTemplate(templateId: string): Promise<WhatsAppTemplate> {
    return apiClient.get(`/whatsapp/templates/${templateId}`);
  }

  /**
   * Create WhatsApp template
   */
  async createTemplate(template: Partial<WhatsAppTemplate>): Promise<WhatsAppTemplate> {
    return apiClient.post('/whatsapp/templates', template);
  }

  /**
   * Update WhatsApp template
   */
  async updateTemplate(templateId: string, template: Partial<WhatsAppTemplate>): Promise<WhatsAppTemplate> {
    return apiClient.put(`/whatsapp/templates/${templateId}`, template);
  }

  /**
   * Delete WhatsApp template
   */
  async deleteTemplate(templateId: string): Promise<{ success: boolean }> {
    return apiClient.delete(`/whatsapp/templates/${templateId}`);
  }

  /**
   * Send WhatsApp message using template
   */
  async sendTemplateMessage(
    to: string,
    templateName: string,
    templateData: Record<string, any>
  ): Promise<WhatsAppMessage> {
    return apiClient.post('/whatsapp/messages', {
      to,
      templateName,
      templateData,
    });
  }

  /**
   * Send invoice via WhatsApp
   */
  async sendInvoice(invoiceId: string, phoneNumber: string): Promise<WhatsAppMessage> {
    return apiClient.post(`/whatsapp/invoices/${invoiceId}/send`, {
      phoneNumber,
    });
  }

  /**
   * Send payment reminder via WhatsApp
   */
  async sendPaymentReminder(invoiceId: string, phoneNumber: string): Promise<WhatsAppMessage> {
    return apiClient.post(`/whatsapp/invoices/${invoiceId}/remind`, {
      phoneNumber,
    });
  }

  /**
   * Send payment receipt via WhatsApp
   */
  async sendPaymentReceipt(paymentId: string, phoneNumber: string): Promise<WhatsAppMessage> {
    return apiClient.post(`/whatsapp/payments/${paymentId}/receipt`, {
      phoneNumber,
    });
  }

  /**
   * Get all WhatsApp messages
   */
  async getMessages(params?: {
    page?: number;
    limit?: number;
    status?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<{
    data: WhatsAppMessage[];
    total: number;
    page: number;
    limit: number;
  }> {
    return apiClient.get('/whatsapp/messages', { params });
  }

  /**
   * Get WhatsApp message by ID
   */
  async getMessage(messageId: string): Promise<WhatsAppMessage> {
    return apiClient.get(`/whatsapp/messages/${messageId}`);
  }

  /**
   * Test WhatsApp connection
   */
  async testConnection(): Promise<{ success: boolean; message: string }> {
    return apiClient.post('/whatsapp/test-connection');
  }
}

export const whatsappService = new WhatsAppService();
export default whatsappService;
