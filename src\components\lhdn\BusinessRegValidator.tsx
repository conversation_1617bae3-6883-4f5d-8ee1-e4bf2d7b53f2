'use client';

import { useState } from 'react';
import { cn } from '@/lib/utils';

interface BusinessRegValidatorProps {
  initialValue?: string;
  onValidation?: (result: { isValid: boolean; regNo: string; businessName?: string }) => void;
  className?: string;
}

export default function BusinessRegValidator({ initialValue = '', onValidation, className }: BusinessRegValidatorProps) {
  const [regNo, setRegNo] = useState(initialValue);
  const [isValidating, setIsValidating] = useState(false);
  const [validationResult, setValidationResult] = useState<{
    isValid: boolean;
    message: string;
    businessName?: string;
  } | null>(null);

  const validateBusinessReg = async () => {
    if (!regNo.trim()) {
      setValidationResult({
        isValid: false,
        message: 'Please enter a Business Registration Number',
      });
      return;
    }

    setIsValidating(true);
    setValidationResult(null);

    try {
      // Call the backend API to validate the business registration number
      const response = await fetch(`/api/lhdn/validate-business/${regNo}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (response.ok) {
        const result = {
          isValid: data.isValid,
          message: data.isValid 
            ? `Valid Business: ${data.businessName}` 
            : 'Invalid Business Registration Number. Please check and try again.',
          businessName: data.businessName,
        };
        
        setValidationResult(result);
        
        if (onValidation) {
          onValidation({
            isValid: data.isValid,
            regNo,
            businessName: data.businessName,
          });
        }
      } else {
        setValidationResult({
          isValid: false,
          message: data.message || 'Failed to validate Business Registration Number. Please try again.',
        });
      }
    } catch (error) {
      console.error('Error validating Business Registration Number:', error);
      setValidationResult({
        isValid: false,
        message: 'An error occurred while validating the Business Registration Number. Please try again.',
      });
    } finally {
      setIsValidating(false);
    }
  };

  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex items-center space-x-2">
        <input
          type="text"
          value={regNo}
          onChange={(e) => setRegNo(e.target.value)}
          placeholder="Enter Business Registration Number"
          className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
        />
        <button
          onClick={validateBusinessReg}
          disabled={isValidating}
          className="px-4 py-2 text-white bg-indigo-600 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50"
        >
          {isValidating ? 'Validating...' : 'Validate'}
        </button>
      </div>
      
      {validationResult && (
        <div
          className={cn(
            "p-3 rounded-md text-sm",
            validationResult.isValid
              ? "bg-green-50 text-green-800 border border-green-200"
              : "bg-red-50 text-red-800 border border-red-200"
          )}
        >
          <div className="flex items-center">
            <span className="mr-2">
              {validationResult.isValid ? (
                <svg className="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              ) : (
                <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              )}
            </span>
            <span>{validationResult.message}</span>
          </div>
        </div>
      )}
    </div>
  );
}
