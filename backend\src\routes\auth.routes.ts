import { Router } from 'express';
import {
  login,
  register,
  getProfile,
  updatePassword,
  refreshToken,
  logout
} from '../controllers/auth.controller';
import { authenticate } from '../middleware/auth.middleware';
import { ExpressHandler } from '../types/express';

const router = Router();

// Public routes
router.post('/login', login as unknown as ExpressHandler);
router.post('/register', register as unknown as ExpressHandler);
router.post('/refresh-token', refreshToken as unknown as ExpressHandler);

// Protected routes
router.get('/profile', authenticate as ExpressHandler, getProfile as unknown as ExpressHandler);
router.put('/password', authenticate as ExpressHandler, updatePassword as unknown as ExpressHandler);
router.post('/logout', authenticate as ExpressHandler, logout as unknown as ExpressHandler);

export default router;
