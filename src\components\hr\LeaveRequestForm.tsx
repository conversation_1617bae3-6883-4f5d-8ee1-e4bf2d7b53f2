'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Spinner } from '@/components/ui/spinner';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface LeaveRequestFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
}

export default function LeaveRequestForm({ isOpen, onClose, onSubmit }: LeaveRequestFormProps) {
  const [formData, setFormData] = useState({
    employeeId: '',
    leaveType: '',
    startDate: '',
    endDate: '',
    reason: '',
    attachmentUrl: '',
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [days, setDays] = useState(0);
  
  // Mock employees data
  const employees = [
    { id: 'EMP001', name: 'John Doe' },
    { id: 'EMP002', name: 'Jane Smith' },
    { id: 'EMP003', name: 'Michael Johnson' },
    { id: 'EMP004', name: 'Sarah Williams' },
    { id: 'EMP005', name: 'David Brown' },
  ];
  
  // Leave types
  const leaveTypes = [
    'Annual Leave',
    'Sick Leave',
    'Maternity Leave',
    'Paternity Leave',
    'Compassionate Leave',
    'Unpaid Leave',
  ];

  useEffect(() => {
    // Calculate number of days between start and end date
    if (formData.startDate && formData.endDate) {
      const start = new Date(formData.startDate);
      const end = new Date(formData.endDate);
      const diffTime = Math.abs(end.getTime() - start.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 to include both start and end days
      setDays(diffDays);
    } else {
      setDays(0);
    }
  }, [formData.startDate, formData.endDate]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    
    try {
      // Validate form data
      if (!formData.employeeId) {
        throw new Error('Employee is required');
      }
      
      if (!formData.leaveType) {
        throw new Error('Leave type is required');
      }
      
      if (!formData.startDate) {
        throw new Error('Start date is required');
      }
      
      if (!formData.endDate) {
        throw new Error('End date is required');
      }
      
      if (new Date(formData.endDate) < new Date(formData.startDate)) {
        throw new Error('End date cannot be before start date');
      }
      
      if (!formData.reason.trim()) {
        throw new Error('Reason is required');
      }
      
      // In a real implementation, this would call your API
      // Simulate API call with timeout
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Add days to the submitted data
      const submittedData = {
        ...formData,
        days,
        status: 'Pending',
        employeeName: employees.find(emp => emp.id === formData.employeeId)?.name,
      };
      
      onSubmit(submittedData);
      onClose();
    } catch (err: any) {
      console.error('Error submitting leave request:', err);
      setError(err.message || 'Failed to submit leave request');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Apply for Leave</DialogTitle>
          <DialogDescription>
            Submit a new leave request for approval.
          </DialogDescription>
        </DialogHeader>
        
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="employeeId">Employee</Label>
            <Select
              value={formData.employeeId}
              onValueChange={(value) => handleSelectChange('employeeId', value)}
            >
              <SelectTrigger id="employeeId">
                <SelectValue placeholder="Select employee" />
              </SelectTrigger>
              <SelectContent>
                {employees.map((employee) => (
                  <SelectItem key={employee.id} value={employee.id}>
                    {employee.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="leaveType">Leave Type</Label>
            <Select
              value={formData.leaveType}
              onValueChange={(value) => handleSelectChange('leaveType', value)}
            >
              <SelectTrigger id="leaveType">
                <SelectValue placeholder="Select leave type" />
              </SelectTrigger>
              <SelectContent>
                {leaveTypes.map((type) => (
                  <SelectItem key={type} value={type}>
                    {type}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="startDate">Start Date</Label>
              <Input 
                id="startDate"
                name="startDate"
                type="date" 
                value={formData.startDate} 
                onChange={handleChange}
                min={new Date().toISOString().split('T')[0]}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="endDate">End Date</Label>
              <Input 
                id="endDate"
                name="endDate"
                type="date" 
                value={formData.endDate} 
                onChange={handleChange}
                min={formData.startDate || new Date().toISOString().split('T')[0]}
              />
            </div>
          </div>
          
          {days > 0 && (
            <div className="text-sm">
              <span className="font-medium">Duration: </span>
              <span>{days} day{days !== 1 ? 's' : ''}</span>
            </div>
          )}
          
          <div className="space-y-2">
            <Label htmlFor="reason">Reason</Label>
            <Textarea 
              id="reason"
              name="reason"
              value={formData.reason} 
              onChange={handleChange}
              placeholder="Please provide a reason for your leave request"
              rows={3}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="attachmentUrl">Attachment (Optional)</Label>
            <Input 
              id="attachmentUrl"
              name="attachmentUrl"
              type="file" 
              onChange={(e) => {
                // In a real implementation, this would handle file upload
                // For now, we'll just store the file name
                if (e.target.files && e.target.files.length > 0) {
                  setFormData({
                    ...formData,
                    attachmentUrl: e.target.files[0].name
                  });
                }
              }}
            />
            <p className="text-xs text-gray-500">
              Upload medical certificate or other supporting documents if applicable.
            </p>
          </div>
          
          <DialogFooter>
            <Button 
              type="button" 
              variant="outline" 
              onClick={onClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button 
              type="submit"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Spinner size="sm" className="mr-2" />
                  Submitting...
                </>
              ) : (
                <>Submit Request</>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
