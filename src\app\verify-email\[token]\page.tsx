'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { apiClient } from '@/lib/api/client';

export default function VerifyEmailPage({ params }: { params: { token: string } }) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isVerified, setIsVerified] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const verifyEmail = async () => {
      try {
        // Call the API to verify the email
        await apiClient.post('/auth/verify-email', { token: params.token }, { skipAuth: true });
        setIsVerified(true);
      } catch (err: any) {
        console.error('Email verification error:', err);
        setError(err.message || 'Invalid or expired verification token.');
      } finally {
        setIsLoading(false);
      }
    };

    verifyEmail();
  }, [params.token]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="w-full max-w-md text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Verifying your email...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <div className="mb-4 text-center">
            <Link href="/" className="inline-block">
              <h1 className="text-3xl font-bold text-indigo-600">Invoix</h1>
            </Link>
          </div>
          <CardTitle className="text-2xl font-bold text-center">
            {isVerified ? 'Email Verified' : 'Verification Failed'}
          </CardTitle>
          <CardDescription className="text-center">
            {isVerified 
              ? 'Your email has been successfully verified.' 
              : 'We could not verify your email address.'}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {isVerified ? (
            <div className="bg-green-50 text-green-700 p-4 rounded-md text-sm">
              <p>Your email has been verified. You can now access all features of your account.</p>
            </div>
          ) : (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </CardContent>
        <CardFooter className="flex justify-center">
          {isVerified ? (
            <Button onClick={() => router.push('/dashboard')}>
              Go to Dashboard
            </Button>
          ) : (
            <div className="flex flex-col space-y-4 items-center">
              <p className="text-sm text-gray-500 text-center">
                The verification link may have expired or is invalid.
              </p>
              <Button onClick={() => router.push('/dashboard/settings/profile')}>
                Request New Verification Email
              </Button>
            </div>
          )}
        </CardFooter>
      </Card>
    </div>
  );
}
